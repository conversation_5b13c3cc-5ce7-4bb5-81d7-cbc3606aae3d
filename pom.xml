<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hishop-blw-dependincies</artifactId>
        <groupId>com.hishop</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>hishop-blw</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-export</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-nfs-oss-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-sms-ali-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-blw-wechat-wx-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hishop</groupId>
                <artifactId>hishop-blw-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://124.71.221.117:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>