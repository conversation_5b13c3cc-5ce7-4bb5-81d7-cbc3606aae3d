server:
  # 开启压缩
  compression:
    enabled: true
    min-response-size: 2048
  #上下文路径
  servlet:
    context-path: /hishop-blw
  port: 8081
spring:
  application:
    name: hishop-blw
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: **********************************************************************************************************************************************************************************************************************************************************************************************
    username: himall
    password: bozIRn5S7hH6C1
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
  redis:
    host: **************
    port: 6379
    database: 15
  #文件上传设置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
# mybaits-plus配置
mybatis-plus:
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
swagger:
  knife4j:
    enable: true
    apiInfo:
      title: 碧朗湾项目接口文档
      description: 碧朗湾项目接口文档
      version: v1.0
      apiUrl: http://127.0.0.1:8081/hishop-blw/doc.html
      baseApiPkg: com.hishop.blw.controller
      groupName: hishopBlw
      contactEmail: <EMAIL>
      contactName: hishop
#测试打开健康检查端口
management:
  endpoints:
    web:
      exposure:
        include: health,info
hishop:
  email:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: FJKRTYMAUTOMSGGJ
nfs:
  oss:
    accessKeyId: LTAI5t7UYUzLFVsjtJ1PGFeL
    accessKeySecret: ******************************
    bucketName: 0218blwbucket
    endPoint: oss-cn-beijing.aliyuncs.com
    domain: 0218blwbucket.oss-cn-beijing.aliyuncs.com
    region: cn-beijing
    internalEndPoint: https://oss-cn-beijing.aliyuncs.com
    externalEndPoint: oss-cn-beijing.aliyuncs.com
    mtsLocationRegion:
  mpc:
    productId: 6b5e628ef4f4473aa80b2e9195137687
    endpoint: https://mpc.cn-south-1.myhuaweicloud.com
    templateId: 7000524
    audioTemplateId: 7001053

wx:
  miniapp:
    isOpen: true
    envVersion: trial
    configs:
      - appid: wx539712c93924fa27
        secret: 5bdfa63b7f8912a82f4b7d9a14063e34

ali:
  accessKeyId: LTAI5t7UYUzLFVsjtJ1PGFeL
  accessKeySecret: ******************************
  signName: 北京碧朗湾健康产业集团
  applySceneContent:  http://www.sunnycare.com
  verificationReminder: SMS_479780053
blw:
  seckey: blwseckey
print:
  user: <EMAIL>
  ukey: XbYMamzdAQBBNjg7
consumer:
  statistics:
    max-row: 10000