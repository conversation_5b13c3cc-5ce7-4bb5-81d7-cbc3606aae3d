<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.OrdersMapper">

    <resultMap id="orderFootListMap" type="com.hishop.blw.model.vo.order.OrdersFootVO">
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="order_type" property="orderType" />
        <result column="order_status" property="orderStatus" />
        <result column="order_source_id" property="orderSourceId"/>
        <result column="order_date" property="orderDate" />
        <result column="pay_date" property="payDate" />
        <result column="store_id" property="storeId" />
        <result column="department_id" property="departmentId" />
        <result column="user_id" property="userId" />
        <result column="recommended_user_id" property="recommendedUserId" />
        <result column="assistant_user_id" property="assistantUserId" />
        <result column="sales_user_id" property="salesUserId" />
        <result column="product_total_amount" property="productTotalAmount" />
        <result column="order_total_amount" property="orderTotalAmount" />
        <result column="points" property="points" />
        <result column="actual_pay_amount" property="actualPayAmount" />
        <result column="board_id" property="boardId" />
        <result column="eat_numbers" property="eatNumbers" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="eat_numbers" property="eatNumbers"/>
        <result column="status" property="status" />
    </resultMap>

    <select id="orderPagesList" resultMap="orderFootListMap">
        select
            temp.*,
            (
            case when temp.cnt > 0
            then (
            case when EXISTS (SELECT 1 from blw_reservation_services where temp.id = order_id)
            then 10
            else 11 end
            )
            else temp.order_status
            end
            ) as `status`
        FROM (SELECT
        a.*,
        (SELECT count(1) from  blw_order_items where product_type in (2,4) and order_id = a.id ) 'cnt',
        (SELECT COUNT(1) FROM blw_order_items WHERE product_type IN (3,5) AND order_id = a.id) 'cpt'
            FROM blw_orders AS a
            LEFT JOIN blw_users AS b ON a.user_id = b.id
        <where>
            <if test="queryPO.orderType != null and queryPO.orderType > 0">
                and a.order_type = #{queryPO.orderType}
            </if>
            <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
                and a.order_no  LIKE CONCAT('%', #{queryPO.orderNo}, '%')
            </if>
            <if test="queryPO.userId != null and queryPO.userId > 0">
                and a.user_id = #{queryPO.userId}
            </if>
            <if test="queryPO.orderStatus !=null and queryPO.orderStatus > 0">
                and a.order_status = #{queryPO.orderStatus}
            </if>
            <if test="queryPO.startDate != null and queryPO.endDate != null">
                AND a.order_date BETWEEN #{queryPO.startDate} AND #{queryPO.endDate}
            </if>
            <if test="queryPO.payStartDate != null and queryPO.payEndDate != null">
                AND a.pay_date BETWEEN #{queryPO.payStartDate} AND #{queryPO.payEndDate}
            </if>
            <if test="queryPO.keyWords != null and queryPO.keyWords != ''">
                AND (
                a.order_no LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                <if test="queryPO.orderType == 2">
                    OR EXISTS (SELECT 1 FROM blw_board WHERE id = a.board_id AND board_number = #{queryPO.keyWords})
                </if>
                OR b.`name` LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.phone LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR EXISTS (SELECT 1 FROM blw_order_items AS ai WHERE ai.order_no = a.order_no AND ai.product_name LIKE CONCAT('%', #{queryPO.keyWords}, '%'))
                )
            </if>
            <if test="queryPO.productCategoryId != null">
                and a.id in (
                SELECT orderItem.order_id from blw_order_items as orderItem
                left JOIN blw_products product  on orderItem.product_id = product.id
                where product.category_id = #{queryPO.productCategoryId})
            </if>
            <if test="queryPO.departmentId != null">
                and a.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.paymentId != null">
                <if test="queryPO.paymentId == 1">
                and a.id in (
                select payment.order_id from blw_payments payment where payment.card_number is not null
                )
                </if>
                <if test="queryPO.paymentId == 2">
                and a.points > 0
                </if>
                  <if test="queryPO.paymentId != 1 and queryPO.paymentId != 2">
                  and a.id in (
                select payment.order_id from blw_payments payment where payment.pay_method_id = #{queryPO.paymentId}
                )
                </if>
            </if>
            <if test="queryPO.orderSourceId != null">
                and a.order_source_id = #{queryPO.orderSourceId}
            </if>
            <if test="queryPO.salesUserId != null">
                and a.sales_user_id = #{queryPO.salesUserId}
            </if>
            <if test="queryPO.userNick != null and queryPO.userNick != ''">
                and b.`nickname` LIKE CONCAT('%', #{queryPO.userNick}, '%')
            </if>
            <if test="queryPO.userPhone != null and queryPO.userPhone != ''">
                and b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
            </if>
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and a.id in (
                select item.order_id from blw_order_items item
                left join blw_products product on item.product_id = product.id
                where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
                )
            </if>
               <if test="queryPO.productType != null">
                and a.id in (
                select distinct item.order_id from blw_order_items item
                where item.product_type = #{queryPO.productType}
                )
            </if>
            <if test="queryPO.storeId != null and  queryPO.storeId > 0">
                and a.store_id = #{queryPO.storeId}
            </if>

            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (a.sales_user_id = #{powerDTO.loginUserId} or a.proxy_user_id = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND a.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND (a.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or a.sales_department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>

        </where>
        ) as temp
        <where>
            <if test="queryPO.status != null">
                <!-- 订单状态 0-全部 1-待使用 2-使用中 3-已完成 4-已关闭 -->
                <if test="queryPO.status == 1">
                    and
                    (
                    temp.cnt > 0 and temp.cpt = 0 and  EXISTS (
                    SELECT 1 from blw_user_services where temp.id = order_id
                    GROUP BY order_id
                    HAVING	 SUM(COALESCE(used_quantity, 0)) = 0
                    AND SUM(COALESCE(available_quantity, 0)) > 0
                    )
                    )
                </if>
                <if test="queryPO.status == 2">
                    and (
                    (
                    temp.cnt > 0 AND temp.cpt = 0
                    AND (
                    SELECT
                    sum( ifnull( us.available_quantity, 0 ) ) + sum( ifnull( us.used_quantity, 0 ) )
                    FROM
                    blw_user_services us
                    WHERE
                    us.order_id = temp.id
                    ) > (
                    SELECT count(1) from blw_verify ver WHERE ver.reservation_id in (
                    select sser.id from blw_reservation_services sser WHERE sser.order_id = temp.id  and sser.status = 2
                    ) ) and
                    (
                    SELECT count(1) from blw_verify ver WHERE ver.reservation_id in (
                    select sser.id from blw_reservation_services sser WHERE sser.order_id = temp.id  and sser.status = 2
                    )
                    ) > 0

                    )
                    or
                    (
                    temp.cnt = 0 AND temp.cpt > 0 AND temp.order_status = 1
                    )
                    )
                </if>
                <if test="queryPO.status == 3 ">
                    and (
                    temp.cnt > 0
                    AND temp.cpt = 0
                    AND (
                    SELECT
                    sum( ifnull( us.available_quantity, 0 ) ) + sum( ifnull( us.used_quantity, 0 ) )
                    FROM
                    blw_user_services us
                    WHERE
                    us.order_id = temp.id
                    ) = (
                    SELECT count(1) from blw_verify ver WHERE ver.reservation_id in (
                    select sser.id from blw_reservation_services sser WHERE sser.order_id = temp.id  and sser.status = 2
                    )
                    )
                    )
                    -- 参订订单 已经支付完成
                    or
                    (
                    temp.cnt = 0 AND temp.cpt > 0 AND temp.order_status = 2
                    )
                    -- 商品订单 支付完成
                    or
                    (
                    temp.cnt = 0 AND temp.cpt = 0 AND temp.order_status = 2
                    )
                </if>
                <if test="queryPO.status == 4 ">
                    and temp.order_status = 5
                </if>
            </if>
        </where>
        order by temp.create_date desc
    </select>

    <select id="getMemberOrderCountByStatus" resultType="java.lang.Integer">
        SELECT count(1) FROM blw_orders as ord
        WHERE ord.user_id = #{userId}
          <if test="status == 1">
          AND (
                    ord.order_type = 1 AND ord.order_status = 2
                AND EXISTS (
                            SELECT 1 FROM blw_order_items where product_type in (2,4) and order_id = ord.id
                        )
              AND NOT EXISTS(select 1 from blw_reservation_services where `status` in (1,2) and order_id = ord.id)
            )
          </if>
          <if test="status == 2">
              AND (
                  (ord.order_type = 1 AND ord.order_status = 6 )
                  or (ord.order_type = 2 and ord.order_status = 1)
              )
          </if>
          <if test="status == 3">
              AND (
              (ord.order_type = 1 AND ord.order_status = 4 and  EXISTS (
              SELECT 1 FROM blw_order_items where product_type in (2,4) and order_id = ord.id
              ))
              or (ord.order_type = 1 AND ord.order_status = 2 and not EXISTS (
              SELECT 1 FROM blw_order_items where product_type in (2,3,4,5) and order_id = ord.id
              ))
              or (ord.order_type = 2 and ord.order_status = 2)
              )
          </if>
    </select>

    <select id="memberPageList" resultType="com.hishop.blw.model.vo.order.OrdersMemberVO">
        select bo.*, bs.store_name storeName
        from blw_orders bo
        left join blw_store bs on bo.store_id = bs.id
        <where>
            <if test="queryDTO.userId != null">
                and bo.user_id = #{queryDTO.userId}
            </if>
            <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
                and bo.order_no = #{queryDTO.orderNo}
            </if>
            <if test="queryDTO.storeId != null">
                and bo.store_id = #{queryDTO.storeId}
            </if>
            <if test="queryDTO.orderStatus != null">
                and bo.order_status=#{queryDTO.orderStatus}
            </if>
            <if test="queryDTO.payDateStart != null">
                and bo.pay_date BETWEEN #{queryDTO.payDateStart} AND #{queryDTO.payDateEnd}
            </if>
            <if test="powerDTO.needFilter()">
              <if test="!powerDTO.isDepartmentManager()">
                AND (bo.sales_user_id = #{powerDTO.loginUserId} or bo.proxy_user_id = #{powerDTO.loginUserId})
            </if>
            <if test="powerDTO.hasStore()">
                and bo.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
             <if test="powerDTO.hasDepartment()">
                and (bo.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or bo.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
        </if>
        </where>
        order by bo.create_date desc
    </select>



    <update id="addFootOrder">
        update blw_orders
        set product_total_amount = product_total_amount + #{amount},
            order_total_amount = order_total_amount + #{amount},
            update_date = now()
        where id = #{id}
    </update>

    <select id="consumerOrdersCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM blw_orders AS a
        LEFT JOIN blw_users AS b ON a.user_id = b.id
        WHERE
        a.user_id = #{userId}
        and (
        (a.order_type = 2 and a.order_status in (2,1))
        or (a.order_type = 1 and a.order_status in (1,2,4,6) and exists(
        select 1 from blw_order_items where product_type in (2,4) and order_id = a.id
        ))
        or (a.order_type = 1 and a.order_status in (1,2) and not exists(
        select 1 from blw_order_items where product_type in (2,4) and order_id = a.id
        ))
        )
    </select>
    <select id="consumerOrders" resultType="com.hishop.blw.model.vo.order.OrdersVO">
        SELECT a.*
        FROM blw_orders AS a
        LEFT JOIN blw_users AS b ON a.user_id = b.id
        WHERE
        a.user_id = #{userId}
        and (
            (a.order_type = 2 and a.order_status in (2,1))
            or (a.order_type = 1 and a.order_status in (1,2,4,6) and exists(
                select 1 from blw_order_items where product_type in (2,4) and order_id = a.id
            ))
            or (a.order_type = 1 and a.order_status in (1,2) and not exists(
                select 1 from blw_order_items where product_type in (2,4) and order_id = a.id
            ))
        )
        <if test="orderStatus !=null and orderStatus > 0">
            and a.order_status = #{orderStatus}
        </if>
        <if test="startDate != null and endDate != null">
            AND a.create_date BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="keyWords != null and keyWords != ''">
           and a.order_no like concat('%',#{keyWords},'%')
        </if>
<!--        <if test="powerDTO.needFilter()">-->
<!--              <if test="!powerDTO.isDepartmentManager()">-->
<!--                AND (a.sales_user_id = #{powerDTO.loginUserId} or a.proxy_user_id = #{powerDTO.loginUserId})-->
<!--            </if>-->
<!--            <if test="powerDTO.hasStore()">-->
<!--                and a.store_id in-->
<!--                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach> -->
<!--            </if>-->
<!--             <if test="powerDTO.hasDepartment()">-->
<!--                and (a.sales_department_id in-->
<!--                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                or a.department_id in-->
<!--                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">-->
<!--                and 1=2-->
<!--            </if>  -->
<!--        </if>-->
        order by a.create_date desc
    </select>

    <select id="saleReport" resultType="com.hishop.blw.model.vo.report.SaleReportVO">
        select o.id,
               o.order_no,
               o.order_type,
               o.order_status,
               o.order_source_id,
               o.pay_date as "orderDate",
               o.user_id,
               u.name userName,
               u.phone,
               o.recommended_user_id,
               o.recommended_department_id,
               o.sales_user_id,
               o.sales_department_id,
               o.assistant_user_id,
               o.assistant_department_id,
               o.adviser_user_ids,
               o.remark,
               o.points_amount,
               o.eat_numbers
        from
        <if test="queryPO.orderDateBegin != null">
            (select * from blw_orders where id in(select id from blw_orders where pay_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
                                                                            union
                                                  select order_id as "id" from blw_refund where `status`=3 and handler_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}))o
        </if>
        <if test="queryPO.orderDateBegin == null">
            blw_orders o
        </if>
            left join blw_users u on o.user_id=u.id
        where o.pay_date is not null
            <if test="queryPO.notInProductId != null">
                and  o.id not in (select it.order_id FROM blw_order_items it WHERE it.product_id = #{queryPO.notInProductId})
            </if>
            and exists(select p.id from blw_payments p join
                (select a.id as "pay_method_id",a.* from blw_pay_methods a
                    union all
                    select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
                )b
                on p.pay_method_id=b.pay_method_id
                where p.order_id=o.id
                and b.iz_sales_report=true
                <if test="queryPO.payMethodId != null">
                  and b.pay_method_id=#{queryPO.payMethodId}
                </if>
                <if test="queryPO.orderCategory != null">
                    <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                        and b.iz_walkin = 0
                    </if>
                    <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                        and b.iz_walkin = 1
                    </if>
                    <if test="queryPO.orderCategory == 5">
                        and b.iz_walkin = 2
                    </if>
                </if>
            )
            <if test="queryPO.orderItemExtendSelect != null">
                and exists(select 1 from (select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,ie.product_type,ie.project_department_id,ie.pay_date
                    from blw_order_items_extend ie
                    left join blw_order_items oi on ie.order_item_id = oi.id
                    union all
                    select oi.order_id,oi.id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id,oi.pay_date
                    from blw_order_items oi)i
                    where i.order_id=o.id
                    <if test="queryPO.orderDateBegin != null">
                        and i.pay_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
                    </if>
                    <if test="queryPO.productName != null and queryPO.productName != ''">
                      and i.product_name like concat('%',#{queryPO.productName},'%')
                    </if>
                    <if test="queryPO.productType != null">
                      and i.product_type=#{queryPO.productType}
                    </if>
                    <if test="queryPO.orderCategory != null">
                        <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                            and i.product_type = 2
                        </if>
                        <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                            and i.product_type = 4
                        </if>
                    </if>
                    <if test="queryPO.projectDepartmentId != null">
                        and i.project_department_id = #{queryPO.projectDepartmentId}
                    </if>

                )
            </if>
          <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
             and o.order_no=#{queryPO.orderNo}
          </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>

        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
             <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        order by o.create_date desc
    </select>

    <select id="saleReportTotal" resultType="com.hishop.blw.repository.dto.OrderAmountDTO">
        select o.id,o.actual_pay_amount,o.points_amount,o.refund_total_amount,p.id as "payId",p.amount,o.pay_date as "orderDate"
        <if test="queryPO.orderItemExtendSelect != null">
            ,i.actual_amount as "itemActualAmount",i.actual_points_amount as "itemActualPointAmount",i.refund_amount as "itemRefundAmount",i.order_item_id
        </if>
        from
        <if test="queryPO.orderDateBegin != null">
            (select * from blw_orders where id in(select id from blw_orders where pay_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
            union
            select order_id as "id" from blw_refund where `status`=3 and handler_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}))o
        </if>
        <if test="queryPO.orderDateBegin == null">
            blw_orders o
        </if>
            left join blw_users u on o.user_id=u.id
            join blw_payments p on o.id=p.order_id
            join (select a.id as "pay_method_id",a.* from blw_pay_methods a
                union all
                select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id)b
                on p.pay_method_id=b.pay_method_id
                <if test="queryPO.izSalesReport == null">
                    and b.iz_sales_report=true
                </if>
                <if test="queryPO.payMethodId != null">
                    and b.pay_method_id=#{queryPO.payMethodId}
                </if>
                <if test="queryPO.orderCategory != null">
                    <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                        and b.iz_walkin = 0
                    </if>
                    <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                        and b.iz_walkin = 1
                    </if>
                    <if test="queryPO.orderCategory == 5">
                        and b.iz_walkin = 2
                    </if>
                </if>
        <if test="queryPO.orderItemExtendSelect != null">
            join (select ie.order_id,ie.id as "order_item_id",ie.product_id,ie.product_name,ie.product_type,ie.project_department_id,ie.amount as "actual_amount"
                ,ie.point_amount as "actual_points_amount",ie.refund_amount
            from blw_order_items_extend ie
            union all
            select oi.order_id,oi.id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id,oi.actual_amount
                ,oi.actual_points_amount,oi.refund_amount
            from blw_order_items oi)i on i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name like concat('%',#{queryPO.productName},'%')
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>
        </if>
        where o.order_status not in(1,3)
        <if test="queryPO.notInProductId != null">
            and  o.id not in (select it.order_id FROM blw_order_items it WHERE it.product_id = #{queryPO.notInProductId})
        </if>
        <if test="queryPO.izSalesReport != null">
            and exists(select pas.id from blw_payments pas join
            (select aa.id as "pay_method_id",aa.* from blw_pay_methods aa
            union all
            select cc.id as "pay_method_id",mm.* from blw_cards cc join blw_pay_methods mm on cc.pay_method_id=mm.id
            )bb
            on pas.pay_method_id=bb.pay_method_id
            where pas.order_id=o.id
            and bb.iz_sales_report=true)
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and o.order_no=#{queryPO.orderNo}
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and p.payment_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>

        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
             <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
    </select>
    <select id="saleRefundReportTotal" resultType="com.hishop.blw.repository.dto.ReportRefundAmountDTO">
        select re.id,re.refund_amount,re.refund_points_amount
        from
        (
        select id,order_id,refund_amount,refund_points_amount,product_name,product_type,project_department_id from blw_refund_items where 1=1
        <if test="queryPO.productName != null and queryPO.productName != ''">
            and product_name like concat('%',#{queryPO.productName},'%')
        </if>
        <if test="queryPO.productType != null">
            and product_type=#{queryPO.productType}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                and product_type = 2
            </if>
            <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                and product_type = 4
            </if>
        </if>
        <if test="queryPO.projectDepartmentId != null">
            and project_department_id = #{queryPO.projectDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and update_date  BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        union all
        select id,order_id,refund_amount,refund_points_amount,product_name,product_type,project_department_id from blw_refund_items_extend where 1=1
        <if test="queryPO.productName != null and queryPO.productName != ''">
            and product_name like concat('%',#{queryPO.productName},'%')
        </if>
        <if test="queryPO.productType != null">
            and product_type=#{queryPO.productType}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                and product_type = 2
            </if>
            <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                and product_type = 4
            </if>
        </if>
        <if test="queryPO.projectDepartmentId != null">
            and project_department_id = #{queryPO.projectDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and update_date  BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        )re join
        <if test="queryPO.orderDateBegin != null">
            (select * from blw_orders where id in(select id from blw_orders where order_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
            union
            select order_id as "id" from blw_refund where `status`=3 and handler_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}))o on re.order_id=o.id
        </if>
        <if test="queryPO.orderDateBegin == null">
            blw_orders o on re.order_id=o.id
        </if>
            left join blw_users u on o.user_id=u.id
            join blw_payments p on o.id=p.order_id
            join (select a.id as "pay_method_id",a.* from blw_pay_methods a
                union all
                select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id)b
                on p.pay_method_id=b.pay_method_id
                <if test="queryPO.izSalesReport == null">
                    and b.iz_sales_report=true
                </if>
                <if test="queryPO.payMethodId != null">
                    and b.pay_method_id=#{queryPO.payMethodId}
                </if>
                <if test="queryPO.orderCategory != null">
                    <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                        and b.iz_walkin = 0
                    </if>
                    <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                        and b.iz_walkin = 1
                    </if>
                    <if test="queryPO.orderCategory == 5">
                        and b.iz_walkin = 2
                    </if>
                </if>
        <!--
        <if test="queryPO.orderItemExtendSelect != null">
            join (select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,ie.product_type,ie.project_department_id,ie.actual_amount,ie.actual_points_amount,ie.refund_amount
            from blw_order_items_extend ie
            union all
            select oi.order_id,oi.id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id,oi.actual_amount,oi.actual_points_amount,oi.refund_amount
            from blw_order_items oi)i on i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name=#{queryPO.productName}
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>
        </if>
        -->
        where o.order_status not in(1,3)
        <if test="queryPO.notInProductId != null">
            and  o.id not in (select it.order_id FROM blw_order_items it WHERE it.product_id = #{queryPO.notInProductId})
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and o.order_no=#{queryPO.orderNo}
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and p.payment_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>

        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
             <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
    </select>

    <select id="listDepartmentList" resultType="java.lang.Long">
        select o.department_id
        from blw_orders o
        where o.pay_date BETWEEN #{queryPO.payDateBegin} AND #{queryPO.payDateEnd}
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.departmentId != null">
            and o.department_id=#{queryPO.departmentId}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                and 1=2
            </if>
              <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        group by o.department_id
    </select>
    <select id="financeSaleReport" resultType="com.hishop.blw.repository.dto.FinanceSaleReportDTO">
        select
        p.order_id,
        p.pay_method_id,
        p.pay_method,
        o.department_id,
        o.store_id,
        o.pay_date,
        b.iz_walkin,
        (
        case when (	SELECT count(1) from blw_pay_methods where id = 1 and iz_finance_sales_report = 1) = 1 then ifnull(o.points_amount,0) + ifnull(p.amount,0) else ifnull(p.amount,0) end
        ) as amount
        from blw_orders o join blw_payments p on o.id=p.order_id
        join (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.card_type = m.card_type )b
        on p.pay_method_id=b.pay_method_id and b.iz_finance_sales_report=true
        where o.pay_date BETWEEN #{queryPO.payDateBegin} AND #{queryPO.payDateEnd}
            and o.id not in (
                SELECT its.order_id from blw_order_items its where its.product_id = 0
            )
            <if test="queryPO.storeId != null">
                and o.store_id=#{queryPO.storeId}
            </if>
            and o.department_id in
            <foreach collection="queryPO.departmentIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>
    </select>

    <select id="getNoConsumeUserCount" resultType="java.lang.Integer">
        SELECT COUNT(u.id) AS userCount
        FROM blw_users u
                 LEFT JOIN (SELECT user_id, MAX(create_date) AS last_order_date
                            FROM blw_orders
                            GROUP BY user_id) o ON u.id = o.user_id
        WHERE u.user_type = 2
          AND (
                (DATEDIFF(CURRENT_DATE, o.last_order_date) > #{day})
                OR
                (o.last_order_date IS NULL AND DATEDIFF(CURRENT_DATE, u.create_date) > #{day})
            )
    </select>

    <select id="getStoreReservationServices" resultType="com.hishop.blw.repository.dto.StoreOrderDTO">
        SELECT
            o.store_id as storeId,count(s.id) as preOrderCount
        FROM blw_reservation_services s
                 left join blw_orders o on s.order_id = o.id
        where  s.`status` in (1,2)
        and o.order_status in(2,4)
        <if test="orderIds != null and orderIds.size() > 0">
            and o.store_id in
            <foreach collection="orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY o.store_id
    </select>

    <select id="getAchievementReport" resultType="com.hishop.blw.model.vo.report.AchievementReportVO">
        select bb.orderId, bb.orderNo, bb.reportType from
        /*销售单数据*/
        (SELECT aa.departmentId, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, aa.extendId, aa.productName, aa.izGift, aa.productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 1 as reportType FROM
        (SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        oie.id as extendId,
        COALESCE(oie.product_name, p.product_name) AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.product_price, oi.product_price) AS productPrice,
        COALESCE(oie.quantity, oi.quantity) AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.pay_date AS createDate,
        CASE
        WHEN p.product_type IN (1,3) THEN '商品销售'
        WHEN p.product_type IN (2,4,5) THEN '服务销售'
        ELSE '其他'
        END AS recordType,
        sales.name AS salesName
        FROM blw_orders o
        INNER JOIN blw_order_items oi ON o.id = oi.order_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.pay_date is not null AND o.order_type != 3
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=1
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.pay_date >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.pay_date &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
         <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
              <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        union all

        /*销售单数据 查询退款向*/
        SELECT
        o.department_id AS departmentId,
        o.order_id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        oie.id as extendId,
        COALESCE(oie.product_name, p.product_name) AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.product_price, p.price) AS productPrice,
        COALESCE(oie.refund_quantity, oi.refund_quantity)*-1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.handler_time AS createDate,
        CASE
        WHEN p.product_type IN (1,3) THEN '商品销售'
        WHEN p.product_type IN (2,4,5) THEN '服务销售'
        ELSE '其他'
        END AS recordType,
        sales.name AS salesName
        FROM (select t.id, t1.sales_department_id, t.order_id, t.order_no, t.create_date, t.`status`, t1.user_id, t1.assistant_user_id, t1.sales_user_id, t1.proxy_user_id, t1.order_type, t.handler_time, t1.department_id, t1.store_id from blw_refund t INNER JOIN blw_orders t1 ON t.order_id = t1.id) o
        INNER JOIN (select t.refund_id, t.refund_quantity, t.refund_amount, t1.product_id, t.order_id, t1.id, t.id as rid from blw_refund_items t left join blw_order_items t1 ON t.order_item_id = t1.id) oi ON o.id = oi.refund_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, us.refund_quantity from blw_order_items_extend t
        INNER JOIN blw_products p1 ON t.product_id = p1.id
        INNER JOIN blw_user_services us ON us.order_id = t.order_id
        AND us.order_item_id = t.order_item_id
        AND us.product_id = t.product_id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.`status` = 3 and o.order_type != 3
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.order_id and pm.iz_performance_report=1
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.handler_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.handler_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>

        </if>
        ) aa group by aa.orderNo, aa.itemId, aa.extendId having sum(aa.quantity) != 0

        UNION ALL

        /*消耗单，项目和疗程下的项目*/
        SELECT aa.departmentId, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, aa.extendId, aa.productName, aa.izGift, aa.productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 2 as reportType FROM (
        SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        oi.id as itemId,
        oie.id as extendId,
        o.store_id,
        o.department_id,
        rs.reservation_no AS orderNo,
        p.product_name AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.product_price, oi.product_price) AS productPrice,
        1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        ver.verify_time AS createDate,
        CASE
        WHEN p.product_type IN (1,3) THEN '商品销售'
        WHEN p.product_type IN (2,4,5) THEN '服务销售'
        ELSE '其他'
        END AS recordType,
        sales.name AS salesName
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        INNER JOIN blw_order_items oi ON rs.order_item_id = oi.id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN blw_order_items_extend oie ON rs.order_id = oie.order_id AND rs.order_item_id = oie.order_item_id AND rs.product_id = oie.product_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON rs.employee_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE 1=1
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and ver.verify_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and ver.verify_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and rs.reservation_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
              <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        UNION ALL

        /*消耗单，项目和疗程下的项目 退还反向*/
        SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        oi.id as itemId,
        oie.id as extendId,
        o.store_id,
        o.department_id,
        rs.reservation_no AS orderNo,
        p.product_name AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.product_price, oi.product_price) AS productPrice,
        -1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        rs.refund_time AS createDate,
        CASE
        WHEN p.product_type IN (1,3) THEN '商品销售'
        WHEN p.product_type IN (2,4,5) THEN '服务销售'
        ELSE '其他'
        END AS recordType,
        sales.name AS salesName
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        INNER JOIN blw_order_items oi ON rs.order_item_id = oi.id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN blw_order_items_extend oie ON rs.order_id = oie.order_id AND rs.order_item_id = oie.order_item_id AND rs.product_id = oie.product_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON rs.employee_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE rs.`status` = 4
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and rs.refund_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and rs.refund_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and rs.reservation_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
<if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
             <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        ) aa group by aa.orderNo, aa.itemId, aa.extendId having sum(aa.quantity) != 0

        UNION ALL

        /*消耗单，商品和菜品*/
        SELECT aa.departmentId, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, null as extendId, aa.productName, aa.izGift, aa.productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 2 as reportType FROM
        (SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        p.product_name AS productName,
        '否' AS izGift,
        oi.product_price AS productPrice,
        oi.quantity AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.pay_date AS createDate,
        '商品消耗' AS recordType,
        sales.name AS salesName
        FROM blw_orders o
        INNER JOIN blw_order_items oi ON o.id = oi.order_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, t.product_type from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.pay_date is not null AND o.order_type != 3 AND (oi.product_type in (1, 3) OR oie.product_type in (1, 3))
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.pay_date >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.pay_date &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
<if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
             <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        union all

        /*消耗单，商品和菜品，退款反向*/
        SELECT
        o.department_id AS departmentId,
        o.order_id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        p.product_name AS productName,
        '否' AS izGift,
        p.price AS productPrice,
        oi.refund_quantity*-1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.handler_time AS createDate,
        '商品消耗' AS recordType,
        sales.name AS salesName
        FROM (select t.id, t1.sales_department_id, t.order_id, t.order_no, t.create_date, t.`status`, t1.user_id, t1.assistant_user_id, t1.sales_user_id, t1.proxy_user_id, t1.order_type, t.handler_time, t1.department_id, t1.store_id from blw_refund t INNER JOIN blw_orders t1 ON t.order_id = t1.id) o
        INNER JOIN (select t1.product_type, t.refund_id, t.refund_quantity, t.refund_amount, t1.product_id, t.order_id, t1.id, t.id as rid from blw_refund_items t left join blw_order_items t1 ON t.order_item_id = t1.id) oi ON o.id = oi.refund_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, t.product_type from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.`status` = 3 and o.order_type != 3 AND (oi.product_type in (1, 3) OR oie.product_type in (1, 3))
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.order_id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.handler_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.handler_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (o.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
             <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        ) aa group by aa.orderNo, aa.itemId having sum(aa.quantity) != 0) bb
        WHERE 1=1
        <if test="param.reportType != null">
            and bb.reportType = #{param.reportType}
        </if>
        group by bb.orderNo, bb.reportType
        order by bb.reportType ASC, bb.createDate DESC, bb.orderNo ASC
    </select>
    <select id="getAchievementReportDetail" resultType="com.hishop.blw.model.vo.report.AchievementReportDeatilVO">
        select bb.* from
        (SELECT aa.departmentId, 0 as writeOffAmount, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, aa.extendId, aa.productName, aa.izGift, sum(aa.productPrice) as productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 1 as reportType FROM
        (SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        oie.id as extendId,
        COALESCE(oie.product_name, p.product_name) AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.amount, oi.pay_amount) AS productPrice,
        COALESCE(oie.quantity, oi.quantity) AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.pay_date AS createDate,
        0 recordType,
        sales.name AS salesName
        FROM blw_orders o
        INNER JOIN blw_order_items oi ON o.id = oi.order_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, t.amount from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.pay_date is not null AND o.order_type != 3
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.pay_date >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.pay_date &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
         <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        union all

        SELECT
        o.department_id AS departmentId,
        o.order_id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        oie.id as extendId,
        COALESCE(oie.product_name, p.product_name) AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(oie.amount, oi.refund_amount)*-1 AS productPrice,
        COALESCE(oie.refund_quantity, oi.refund_quantity)*-1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.handler_time AS createDate,
        0 AS recordType,
        sales.name AS salesName
        FROM (select t.id, t1.sales_department_id, t.order_id, t.order_no, t.create_date, t.`status`, t1.user_id, t1.assistant_user_id, t1.sales_user_id, t1.proxy_user_id, t1.order_type, t.handler_time, t1.department_id, t1.store_id from blw_refund t INNER JOIN blw_orders t1 ON t.order_id = t1.id) o
        INNER JOIN (select t.refund_id, t.refund_quantity, t.refund_amount, t1.product_id, t.order_id, t1.id, t.id as rid from blw_refund_items t left join blw_order_items t1 ON t.order_item_id = t1.id) oi ON o.id = oi.refund_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, us.refund_quantity, t.amount from blw_order_items_extend t
        INNER JOIN blw_products p1 ON t.product_id = p1.id
        INNER JOIN blw_user_services us ON us.order_id = t.order_id
        AND us.order_item_id = t.order_item_id
        AND us.product_id = t.product_id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.`status` = 3 and o.order_type != 3
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.order_id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.handler_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.handler_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
         <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        ) aa group by aa.orderNo, aa.itemId, aa.extendId having sum(aa.quantity) != 0

        UNION ALL

        SELECT aa.departmentId, aa.writeOffAmount, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, aa.extendId, aa.productName, aa.izGift, aa.productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 2 as reportType FROM (
        SELECT
        o.department_id AS departmentId,
        rs.amount as writeOffAmount,
        o.id as orderId,
        oi.id as itemId,
        oie.id as extendId,
        o.store_id,
        o.department_id,
        rs.reservation_no AS orderNo,
        p.product_name AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        COALESCE(rs.amount, oi.pay_amount) AS productPrice,
        1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        ver.verify_time AS createDate,
        1 AS recordType,
        sales.name AS salesName
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        INNER JOIN blw_order_items oi ON rs.order_item_id = oi.id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN blw_order_items_extend oie ON rs.order_id = oie.order_id AND rs.order_item_id = oie.order_item_id AND rs.product_id = oie.product_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON rs.employee_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE 1=1
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and ver.verify_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and ver.verify_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            AND rs.reservation_no LIKE CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
           <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        UNION ALL

        SELECT
        o.department_id AS departmentId,
        rs.amount as writeOffAmount,
        o.id as orderId,
        oi.id as itemId,
        oie.id as extendId,
        o.store_id,
        o.department_id,
        rs.reservation_no AS orderNo,
        p.product_name AS productName,
        CASE
        WHEN oie.iz_gift = 1 THEN '是'
        ELSE '否'
        END AS izGift,
        rs.amount AS productPrice,
        -1 AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        rs.refund_time AS createDate,
        1 AS recordType,
        sales.name AS salesName
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        INNER JOIN blw_order_items oi ON rs.order_item_id = oi.id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN blw_order_items_extend oie ON rs.order_id = oie.order_id AND rs.order_item_id = oie.order_item_id AND rs.product_id = oie.product_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON rs.employee_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE rs.`status` = 4
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and rs.refund_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and rs.refund_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            AND rs.reservation_no LIKE CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and (oie.product_name like CONCAT('%',#{param.productName},'%') or p.product_name like CONCAT('%',#{param.productName},'%'))
        </if>
   <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        ) aa group by aa.orderNo, aa.itemId, aa.extendId having sum(aa.quantity) != 0

        UNION ALL

        SELECT aa.departmentId, 0 as writeOffAmount, aa.orderId, aa.store_id, aa.department_id, aa.orderNo, aa.itemId, null as extendId, aa.productName, aa.izGift, sum(aa.productPrice) as productPrice, sum(aa.quantity) as quantity, aa.userName, aa.userPhone, aa.departmentName, aa.adviserName, aa.empName, aa.createDate, aa.recordType, aa.salesName, 2 as reportType FROM
        (SELECT
        o.department_id AS departmentId,
        o.id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        ifnull(oie.product_name, p.product_name) AS productName,
        '否' AS izGift,
        oi.actual_amount AS productPrice,
        oi.quantity AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.pay_date AS createDate,
        2 AS recordType,
        sales.name AS salesName,
        concat(oi.id, '|', ifnull(oie.id, '')) AS unit_id
        FROM blw_orders o
        INNER JOIN blw_order_items oi ON o.id = oi.order_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, t.product_type, t.amount from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.pay_date is not null AND o.order_type != 3 AND (oi.product_type in (1, 3) OR oie.product_type in (1, 3))
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.pay_date >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.pay_date &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
           <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        union all

        SELECT
        o.department_id AS departmentId,
        o.order_id as orderId,
        o.store_id,
        o.department_id,
        o.order_no AS orderNo,
        oi.id as itemId,
        ifnull(oie.product_name, p.product_name) AS productName,
        '否' AS izGift,
        -oi.refund_amount AS productPrice,
        -oi.refund_quantity AS quantity,
        u.name AS userName,
        u.phone AS userPhone,
        d.department_name AS departmentName,
        adviser.name AS adviserName,
        emp.name AS empName,
        o.handler_time AS createDate,
        2 AS recordType,
        sales.name AS salesName,
        concat(oi.id, '|', ifnull(oie.id, '')) AS unit_id
        FROM (select t.id, t1.sales_department_id, t.order_id, t.order_no, t.create_date, t.`status`, t1.user_id, t1.assistant_user_id, t1.sales_user_id, t1.proxy_user_id, t1.order_type, t.handler_time, t1.department_id, t1.store_id from blw_refund t INNER JOIN blw_orders t1 ON t.order_id = t1.id) o
        INNER JOIN (select t1.product_type, t.refund_id, t.refund_quantity, t.refund_amount, t1.product_id, t.order_id, t1.id, t.id as rid from blw_refund_items t left join blw_order_items t1 ON t.order_item_id = t1.id) oi ON o.id = oi.refund_id
        INNER JOIN blw_products p ON oi.product_id = p.id
        LEFT JOIN
        (select t.id, t.order_item_id, t.order_id, p1.product_name, t.iz_gift, t.product_price, t.quantity, t.product_type from blw_order_items_extend t LEFT JOIN blw_products p1 ON t.product_id = p1.id) oie ON oi.id = oie.order_item_id
        LEFT JOIN blw_users u ON o.user_id = u.id
        LEFT JOIN blw_department d ON o.sales_department_id = d.id
        LEFT JOIN blw_users adviser ON FIND_IN_SET(adviser.id, u.adviser_user_ids)
        LEFT JOIN blw_users emp ON o.assistant_user_id = emp.id
        LEFT JOIN blw_users sales ON o.sales_user_id = sales.id
        WHERE o.`status` = 3 and o.order_type != 3 AND (oi.product_type in (1, 3) OR oie.product_type in (1, 3))
        and exists (select 1 from blw_payments p inner join (select a.id as "pay_method_id", a.iz_performance_report from blw_pay_methods a union all select c.id as "pay_method_id", m.iz_performance_report from blw_cards c join blw_pay_methods m on c.card_type=m.card_type) pm on p.pay_method_id=pm.pay_method_id
        where p.order_id=o.order_id and pm.iz_performance_report=true
        <if test="param.payMethodId != null">
            and  p.pay_method_id = #{param.payMethodId}
        </if>
        )
        <if test="param.startTime != null and param.startTime != ''">
            and o.handler_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and o.handler_time &lt; #{param.endTime}
        </if>
        <if test="param.storeId != null">
            and o.store_id = #{param.storeId}
        </if>
        <if test="param.departmentId != null">
            and o.department_id = #{param.departmentId}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and o.order_no like CONCAT('%',#{param.orderNo},'%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and u.nickname like CONCAT('%',#{param.nickname},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like CONCAT('%',#{param.phone},'%')
        </if>
        <if test="param.productName != null and param.productName != ''">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
           <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        ) aa group by aa.orderNo, aa.unit_id  having sum(aa.quantity) != 0) bb
        WHERE 1=1
        <if test="param.orderIds != null and param.orderIds.size() > 0">
            and bb.orderId in
            <foreach collection="param.orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.reportType != null">
            and bb.reportType = #{param.reportType}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasDepartment()">
                AND bb.departmentId IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="!powerDTO.hasDepartment()">
                AND 1=2
            </if>
        </if>
        order by bb.reportType ASC, bb.createDate DESC, bb.orderNo ASC
    </select>

    <select id="listManageReport" resultType="com.hishop.blw.model.vo.report.ManageReportVO">
        SELECT
            <include refid="commonManageReportSelect"/>
        FROM
            <include refid="commonManageReportTableJoin"/>
        where
            <include refid="commonManageReportCondition"/>
        GROUP BY
            <if test="queryPO.izTotal != null and queryPO.izTotal != true">
                categoryId
            </if>
            <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory != true">
                <!-- 总计是不需要id分组的 -->
                <if test="queryPO.izTotal != null and queryPO.izTotal != true">
                    ,
                </if>
                month
            </if>
        <!--CASE
            WHEN orders.product_type IN (2, 4) AND o.order_status IN (2,4,5,6,7)
            THEN DATE_FORMAT(ver.create_date, '%Y-%m')
            ELSE DATE_FORMAT(o.pay_date, '%Y-%m')
        END-->
    </select>

    <sql id="commonManageReportSelect">
        <if test="queryPO.izTotal != null and queryPO.izTotal != true">
            <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory != true">
                one.id categoryId
            </if>
            <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory == true">
                (CASE WHEN one.parent_category_id = 0 THEN one.id ELSE one.parent_category_id END) AS categoryId
            </if>
        </if>
        <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory != true">
            <!-- 如果不是过滤查id，就是需要正式月份数据，但是总计无需id，和name -->
            <if test="queryPO.izTotal != null and queryPO.izTotal != true">
                ,
                one.category_name categoryName,
            </if>
            CASE
                WHEN orders.product_type IN (2, 4) AND o.order_status IN (2,4,5,6,7)
                THEN DATE_FORMAT(ver.create_date, '%Y-%m')
                ELSE DATE_FORMAT(o.pay_date, '%Y-%m')
            END as month,
            SUM(
                CASE WHEN orders.product_type IN (2, 4) THEN (
                    SELECT COALESCE(SUM(rs.amount), 0)
                    FROM blw_reservation_services rs
                    WHERE rs.order_item_id = orders.id  AND rs.status = 2
                )
                    ELSE orders.actual_amount
                END
            ) AS amount
        </if>
    </sql>

    <sql id="commonManageReportTableJoin">
        blw_orders  o
        left join blw_order_items orders on orders.order_id = o.id
        left join blw_products pro  on pro.id = orders.product_id
        <if test="queryPO.izDetail != null and queryPO.izDetail == true">
            left join blw_product_categories two on  pro.category_id = two.id
            left join blw_product_categories one on one.id = two.parent_category_id
        </if>
        <if test="queryPO.izDetail != null and queryPO.izDetail != true">
            <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory != true">
                left join blw_product_categories one on (pro.category_id = one.id or one.parent_category_id = pro.category_id)
            </if>
            <if test="queryPO.izFilterCategory != null and queryPO.izFilterCategory == true">
                left join blw_product_categories one on pro.category_id = one.id
            </if>
        </if>
        LEFT JOIN blw_reservation_services rs  ON rs.order_item_id = orders.id AND rs.status = 2
        LEFT JOIN blw_verify ver ON ver.reservation_id = rs.id
    </sql>

    <sql id="commonManageReportCondition">
        o.pay_date is not null
        and one.id is not null
        AND (
        CASE
            WHEN orders.product_type IN (2, 4) AND o.order_status IN (2, 4, 5, 6, 7)
                THEN DATE_FORMAT(ver.create_date, '%Y-%m')
            ELSE DATE_FORMAT(o.pay_date, '%Y-%m')
            END
        ) IS NOT NULL
        and exists
        (
--         SELECT 1 from blw_payments payments
--         right join blw_pay_methods methods on methods.id = (case when payments.card_number is not null then (
--         select mds.id from  blw_pay_methods mds
--         left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = payments.pay_method_id
--         ) else payments.pay_method_id end)
--         where ( methods.iz_consume_report = 1  or methods.iz_operating_report = 1 ) and payments.order_id = o.id
        select p.id from blw_payments p join
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
        )b
        on p.pay_method_id=b.pay_method_id
        where p.order_id=o.id
        and b.iz_operating_report = true
        )
        AND (
        (orders.product_type in (2,4) AND o.order_status in (2,4,5,6,7))

        <!-- 服务/疗程，支付完成,切产生核销记录 -->
        or
        (
        orders.product_type in (1,3,5) and o.order_status in (2,4,5)
        <!-- 菜品，订单支付或完成 -->
        )
        )
        and YEAR(o.create_date) = #{queryPO.year}

        <if test="queryPO.storeId != null and queryPO.storeId != 0">
            <!-- and EXISTS ( SELECT pc.id FROM blw_product_categories as pc
                 left join blw_store_service_areas as  ssa on pc.department_id = ssa.department_id
                 where ssa.store_id = #{queryPO.storeId} and pc.id = pro.category_id
              ) -->
            and o.store_id = #{queryPO.storeId}
        </if>
        <if test="queryPO.departId != null and queryPO.departId != 0">
            and one.department_id = #{queryPO.departId}
        </if>



        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND one.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="queryPO.listCategoryId != null and queryPO.listCategoryId.size() > 0">
            AND one.id IN
            <foreach collection="queryPO.listCategoryId" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
    </sql>

    <select id="listManageRefundReport" resultType="com.hishop.blw.model.vo.report.ManageReportVO">
        SELECT
            <include refid="commonManageRefundReportSelect"/>
        from
            <include refid="commonManageRefundReportTableJoin"/>
        where
            <include refid="commonManageRefundReportCondition"/>
        GROUP BY
            <if test="queryPO.izTotal != null and queryPO.izTotal != true">
                p.category_id,
            </if>
            DATE_FORMAT(refund.create_date, '%Y-%m')
    </select>

    <sql id="commonManageRefundReportSelect">
        DATE_FORMAT( refund.create_date, '%Y-%m' ) AS MONTH,
        sum( ifnull(refund.refund_amount,0) ) amount
        <if test="queryPO.izTotal != null and queryPO.izTotal != true">
            ,ifnull(one.id,0) categoryId
        </if>
    </sql>

    <sql id="commonManageRefundReportTableJoin">
        blw_refund refund
        left join blw_refund_items items on items.refund_id = refund.id
        left join blw_order_items oi on oi.id = items.order_item_id
        left join blw_products p on p.id = oi.product_id
        left join blw_product_categories two on  p.category_id = two.id
        left join blw_product_categories one on one.id = two.parent_category_id
        <!--            left join blw_product_categories cate on cate.id = p.category_id-->
        LEFT JOIN blw_orders o on o.id = refund.order_id
    </sql>

    <sql id="commonManageRefundReportCondition">
        YEAR(refund.create_date) = #{queryPO.year}

        <if test="queryPO.storeId != null and queryPO.storeId != 0">
            AND EXISTS (
            SELECT pc.id FROM blw_product_categories AS pc
            LEFT JOIN blw_store_service_areas AS ssa ON pc.department_id = ssa.department_id
            WHERE ssa.store_id = #{queryPO.storeId} AND pc.id = p.category_id
            )
        </if>

        <if test="queryPO.departId != null and queryPO.departId != 0">
            AND two.department_id = #{queryPO.departId}
        </if>

        <if test="queryPO.izManager != null and queryPO.izManager == false and queryPO.listDepartId != null and queryPO.listDepartId.size() > 0">
            AND two.department_id IN
            <foreach collection="queryPO.listDepartId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="powerDTO.needFilter()">
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} OR o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        <if test="queryPO.listCategoryId != null and queryPO.listCategoryId.size() > 0">
            AND one.id IN
            <foreach collection="queryPO.listCategoryId" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
    </sql>


    <select id="listManageReportDetail" resultType="com.hishop.blw.model.vo.report.ManageReportDetailVO">
        SELECT
            <include refid="commonManageReportSelect"/>,
            two.id AS secondCategoryId,
            two.category_name AS secondCategoryName,
            pro.id AS productId,
            pro.product_name,
            SUM(IF(orders.product_type IN (2, 4), IFNULL(ver.quantity, 0) + IFNULL(ver.point_amount, 0), IFNULL(orders.quantity, 0))) AS quantity
        FROM
            <include refid="commonManageReportTableJoin"/>
        where
            <include refid="commonManageReportCondition"/>
            <if test="queryPO.month != null and queryPO.month > 0">
                AND CASE
                        WHEN orders.product_type IN (2, 4) AND o.order_status IN (2,4,5,6,7)
                        THEN DATE_FORMAT(ver.create_date, '%Y-%m')
                        ELSE DATE_FORMAT(o.pay_date, '%Y-%m')
                    END = CONCAT(#{queryPO.year}, '-', IF(#{queryPO.month} &lt; 10, CONCAT('0', #{queryPO.month}), #{queryPO.month}))
            </if>
            <if test="queryPO.categoryId != null">
                AND one.id = #{queryPO.categoryId}
            </if>
            <if test="queryPO.secondCategoryId != null">
                AND two.id = #{queryPO.secondCategoryId}
            </if>
        GROUP BY
            <!-- 详情是查商品 -->
            pro.id,
            month
    </select>

    <select id="listManageRefundReportDetail" resultType="com.hishop.blw.model.vo.report.ManageReportDetailVO">
        SELECT
            <include refid="commonManageRefundReportSelect"/>,
            one.category_name,
            two.id AS secondCategoryId,
            two.category_name AS secondCategoryName,
            p.id AS productId,
            p.product_name,
            SUM(IFNULL(items.refund_quantity, 0)) AS quantity
        from
            <include refid="commonManageRefundReportTableJoin"/>
        where
            <include refid="commonManageRefundReportCondition"/>
            <if test = "queryPO.month != null and queryPO.month > 0">
                AND DATE_FORMAT(refund.create_date, '%Y-%m') = CONCAT(#{queryPO.year}, '-', IF(#{queryPO.month} &lt; 10, CONCAT('0', #{queryPO.month}), #{queryPO.month}))
            </if>
            <if test="queryPO.productIds != null and queryPO.productIds.size() > 0">
                AND oi.product_id IN
                <foreach collection="queryPO.productIds" item="productId" separator="," open="(" close=")">
                    #{productId}
                </foreach>
            </if>
        GROUP BY
            p.id,
            MONTH
    </select>

    <select id="listManageReportOrder" resultType="com.hishop.blw.model.vo.report.ManageReportOrderVO">
        SELECT
            o.id,
            o.order_no,
            o.order_total_amount,
            o.refund_total_amount,
            u.id AS userId,
            u.`name` AS userName,
            u.phone
        FROM (
                 SELECT * FROM blw_orders WHERE id IN (
                     SELECT id FROM blw_orders
                     WHERE DATE_FORMAT(order_date, '%Y-%m') = CONCAT(#{queryPO.year}, '-', IF(#{queryPO.month} &lt; 10, CONCAT('0', #{queryPO.month}), #{queryPO.month}))
                     UNION
                     SELECT order_id FROM blw_refund
                     WHERE `status` = 3
                       AND DATE_FORMAT(handler_time, '%Y-%m') = CONCAT(#{queryPO.year}, '-', IF(#{queryPO.month} &lt; 10, CONCAT('0', #{queryPO.month}), #{queryPO.month}))
                 )
             ) AS o
                 LEFT JOIN blw_users u ON o.user_id = u.id
        WHERE EXISTS (
            SELECT 1
            FROM blw_order_items oi
                     JOIN blw_products p ON p.id = oi.product_id
            WHERE oi.order_id = o.id
              AND p.category_id = #{queryPO.secondCategoryId}
              AND oi.product_id = #{queryPO.productId}
        )
        AND o.pay_date IS NOT NULL
        <if test="queryPO.orderNo != null and queryPO.orderNo.length() > 0">
            AND o.order_no LIKE CONCAT('%', #{queryPO.orderNo}, '%')
        </if>
        <if test="queryPO.phone != null and queryPO.phone.length() > 0">
            AND u.phone LIKE CONCAT('%', #{queryPO.phone}, '%')
        </if>
    </select>

    <sql id="consumerQuery">
        select consume_order.*, u.nickname, u.phone from (
            select * from (
                select
                    o.id 'order_id',
                    o.order_no 'no',
                    o.order_no,
                    o.order_source_id,
                    o.order_status,
                    o.store_id,
                    o.department_id,
                    o.sales_department_id,
                    o.sales_user_id,
                    o.remark,
                    o.user_id,
                    oi.id as 'order_item_id',
                    oi.product_id,
                    oi.product_name,
                    oi.actual_amount 'payAmount',
                    oi.actual_points 'payPoint',
                    oi.actual_points_amount 'payPointAmount',
                    oi.quantity 'product_quantity',
                    oi.product_type,
                    0 'iz_gift',
                    o.pay_date 'verify_date',
                    '' as 'reservation_date',
                    u.name	as 'operator',
                    ifnull(
                    (
                        select sum(ri.refund_quantity)
                        from blw_refund_items ri
                        left join blw_refund r on ri.refund_id = r.id
                        where r.`status` =3
                            and ri.order_item_id = oi.id
                            and DATE_FORMAT(r.handler_time, "%Y-%m" ) = DATE_FORMAT(o.pay_date, "%Y-%m" )
                    )
                    , 0) as refund_quantity,
                    ifnull(
                    (
                        select sum(ri.refund_amount)
                        from blw_refund_items ri
                        left join blw_refund r on ri.refund_id = r.id
                        where r.`status` =3
                            and ri.order_item_id = oi.id
                            and DATE_FORMAT(r.handler_time, "%Y-%m" ) = DATE_FORMAT(o.pay_date, "%Y-%m" )
                    )
                    , 0) as refund_amount,
                    0 as employee_id,
                    0 as  room_id,
                    0 as device_id,
                    o.eat_numbers
                from blw_order_items oi
                    left join blw_orders o on oi.order_id = o.id
                    left join blw_users u on o.create_by = u.id
                where
                    oi.product_type in (1, 3) and o.order_status in (2,4)

                union all

                select
                    o.id 'order_id',
                    rs.reservation_no 'no',
                    o.order_no,
                    o.order_source_id,
                    o.order_status,
                    o.store_id,
                    o.department_id,
                    o.sales_department_id,
                    o.sales_user_id,
                    v.remark,
                    o.user_id,
                    oi.id as 'order_item_id',
                    oi.product_id,
                    rs.product_name,
                    v.amount,
                    v.point,
                    v.point_amount 'payPointAmount',
                    v.quantity / rs.reservation_number 'product_quantity',
                    oi.product_type,
                    us.iz_gift,
                    v.create_date 'verify_date',
                    CONCAT(DATE_FORMAT(rs.reservation_date, "%Y-%m-%d"), ' ', DATE_FORMAT(rs.service_start_time, "%H:%i"), '-', DATE_FORMAT(rs.service_end_time, "%H:%i")) 'reservation_date' ,
                    v.verifier 'operator',
                    0,
                    0,
                    rs.employee_id,
                    rs.room_id,
                    rs.device_id,
                    rs.reservation_number 'eat_numbers'
                from blw_order_items oi
                    left join blw_orders o on oi.order_id = o.id
                    left join blw_reservation_services rs on oi.id = rs.order_item_id
                    left join blw_user_services us on rs.user_service_id = us.id
                    left join blw_verify v on rs.id = v.reservation_id
                where
                    oi.product_type in (2,4) and v.id is not null
                    and (
                        (rs.`status` = 2)
                        or
                        (rs.`status` = 4 and DATE_FORMAT(v.create_date, "%Y-%m" ) != DATE_FORMAT(rs.refund_time, "%Y-%m" ))
                    )
                    <if test="powerDTO.needFilter()">
                        <if test="!powerDTO.isDepartmentManager()">
                            AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
                        </if>
                    </if>

                union all

                select
                    o.id 'order_id',
                    o.order_no 'no',
                    o.order_no,
                    o.order_source_id,
                    o.order_status,
                    o.store_id,
                    o.department_id,
                    o.sales_department_id,
                    o.sales_user_id,
                    o.remark,
                    o.user_id,
                    oi.id as 'order_item_id',
                    oie.product_id,
                    oie.product_name,
                    oie.amount,
                    oie.point,
                    oie.point_amount 'payPointAmount',
                    oie.quantity,
                    oi.product_type,
                    oie.iz_gift,
                    o.pay_date 'verify_date',
                    '' as 'reservation_date',
                    u.name	as 'operator',
                    ifnull(
                    (
                        select sum(rie.refund_quantity)
                        from blw_refund_items ri
                            left join blw_refund r on ri.refund_id = r.id
                            left join blw_refund_items_extend rie on ri.id = rie.refund_item_id
                        where r.`status` =3
                            and ri.order_item_id = oi.id
                            and rie.order_item_extent_id = oie.id
                            and DATE_FORMAT(r.handler_time, "%Y-%m" ) = DATE_FORMAT(o.pay_date, "%Y-%m" )
                    )
                    , 0) as refund_quantity,
                    ifnull(
                    (
                        select sum(rie.refund_amount)
                        from blw_refund_items ri
                            left join blw_refund r on ri.refund_id = r.id
                            left join blw_refund_items_extend rie on ri.id = rie.refund_item_id
                        where r.`status` =3
                            and ri.order_item_id = oi.id
                            and rie.order_item_extent_id = oie.id
                            and DATE_FORMAT(r.handler_time, "%Y-%m" ) = DATE_FORMAT(o.pay_date, "%Y-%m" )
                    )
                    , 0) as refund_amount,
                    0 as employee_id,
                    0 as  room_id,
                    0 as device_id,
                    o.eat_numbers
                from blw_order_items oi
                    left join blw_orders o on oi.order_id = o.id
                    left join blw_order_items_extend oie on oi.id = oie.order_item_id
                    left join blw_users u on o.create_by = u.id
                where
                    o.pay_date is not null
                    and oie.product_type in (1,3)
                    <if test="powerDTO.needFilter()">
                        <if test="!powerDTO.isDepartmentManager()">
                            AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
                        </if>
                    </if>
            ) bate
            where product_quantity > bate.refund_quantity

            union all

            select
                o.id 'order_id',
                r.refund_no 'no',
                o.order_no,
                o.order_source_id,
                o.order_status,
                o.store_id,
                o.department_id,
                o.sales_department_id,
                o.sales_user_id,
                o.remark,
                o.user_id,
                oi.id as 'order_item_id',
                oi.product_id,
                oi.product_name,
                -r.refund_amount,
                -ri.refund_points,
                -ri.refund_points_amount,
                -ri.refund_quantity,
                oi.product_type, 0 'is_girt',
                r.handler_time,
                '' as 'reservation_date',
                u.name as 'operator',
                null,
                null,
                0 as employee_id,
                0 as room_id,
                0 as device_id,
                (
                   case when o.eat_numbers is not null then  o.eat_numbers
                        when rs.reservation_number > 0 and oi.product_type = 2  then rs.reservation_number
                    else o.eat_numbers end
                ) 'eat_numbers'
            from blw_refund r
                left join blw_refund_items ri on r.id = ri.refund_id
                left join blw_orders o on r.order_id = o.id
                left join blw_order_items oi on ri.order_item_id = oi.id
                left join blw_order_items_extend oie on oi.id = oie.order_item_id
                left join blw_user_services us on oi.id = us.order_item_id
                left join blw_reservation_services rs on us.id = rs.user_service_id
                left join blw_verify v on rs.id = v.reservation_id
                left join blw_users u on r.handler_id = u.id
            where r.`status` = 3
                and o.pay_date is not null
                and oi.product_type in (1, 3) and DATE_FORMAT(o.pay_date, '%Y-%m') != DATE_FORMAT(r.handler_time, '%Y-%m')
                <if test="powerDTO.needFilter() and !powerDTO.isDepartmentManager()">
                    AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
                </if>


            union all


            select
                o.id 'order_id',
                rs.reservation_no 'no',
                o.order_no,
                o.order_source_id,
                o.order_status,
                o.store_id,
                o.department_id,
                o.sales_department_id,
                o.sales_user_id,
                v.remark,
                o.user_id,
                oi.id as 'order_item_id',
                oi.product_id,
                rs.product_name,
                -v.amount,
                -v.point,
                -v.point_amount 'payPointAmount',
                -(v.quantity / rs.reservation_number) 'product_quantity',
                oi.product_type,
                us.iz_gift,
                v.create_date 'verify_date',
                CONCAT(DATE_FORMAT(rs.reservation_date, "%Y-%m-%d"), ' ', DATE_FORMAT(rs.service_start_time, "%H:%i"), '-', DATE_FORMAT(rs.service_end_time, "%H:%i")) 'reservation_date' ,
                v.verifier 'operator',
                0,
                0,
                rs.employee_id,
                rs.room_id,
                rs.device_id,
                rs.reservation_number 'eat_numbers'
            from blw_order_items oi
                left join blw_orders o on oi.order_id = o.id
                left join blw_reservation_services rs on oi.id = rs.order_item_id
                left join blw_user_services us on rs.user_service_id = us.id
                left join blw_verify v on rs.id = v.reservation_id
            where
                oi.product_type in (2,4) and v.id is not null
                and rs.`status` = 4
                and DATE_FORMAT(v.create_date, "%Y-%m" ) != DATE_FORMAT(rs.refund_time, "%Y-%m" )
                <if test="powerDTO.needFilter() and !powerDTO.isDepartmentManager()">
                    AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
                </if>

        ) as consume_order
        left join blw_users u on consume_order.user_id = u.id
        where 1=1
            and exists (
                select pm.id
                from blw_payments p
                    left join blw_pay_methods pm on if(p.card_number = '' or p.card_number is null, p.pay_method_id, ( (
        select mds.id from  blw_pay_methods mds
        left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id ))) = pm.id
                where consume_order.order_id = p.order_id
                  and p.refund_id is null
                  and pm.iz_consume_report = 1
            )
        <if test="queryPO.start != null">
            and consume_order.verify_date >= #{queryPO.start}
        </if>
        <if test="queryPO.end != null">
            and consume_order.verify_date &lt;= #{queryPO.end}
        </if>
        <if test="queryPO.storeId != null">
            and consume_order.store_id = #{queryPO.storeId}
        </if>
        <if test="queryPO.departmentId!= null">
            and consume_order.sales_department_id = #{queryPO.departmentId}
        </if>
        <if test="queryPO.salesUserId!= null">
            and consume_order.sales_user_id = #{queryPO.salesUserId}
        </if>
        <if test="queryPO.orderCategory != null">
            and exists(
                select p.id
                from blw_payments p
                    left join blw_pay_methods pm on if(p.card_number = '' or p.card_number is null, p.pay_method_id, (
                        select pmm.id
                        from blw_pay_methods pmm
                        left join blw_cards c on pmm.card_type = c.card_type
                        where c.id = p.pay_method_id
                    )
                ) = pm.id
                where p.order_id = consume_order.order_id
                    and p.refund_id is null
                    <if test="queryPO.orderCategory == 1">
                        and pm.iz_walkin = 0 and consume_order.product_type != 4
                    </if>
                    <if test="queryPO.orderCategory == 2">
                        and pm.iz_walkin = 0 and consume_order.product_type = 4
                    </if>
                    <if test="queryPO.orderCategory == 3">
                        and pm.iz_walkin = 1 and consume_order.product_type != 4
                    </if>
                    <if test="queryPO.orderCategory == 4">
                        and pm.iz_walkin = 1 and consume_order.product_type = 4
                    </if>
                    <if test="queryPO.orderCategory == 5">
                        and pm.iz_walkin  =2
                    </if>
            )
        </if>
        <if test="queryPO.orderNo!= null and queryPO.orderNo!=''">
            and consume_order.order_no = #{queryPO.orderNo}
        </if>
        <if test="queryPO.no!= null and queryPO.no!=''">
            and consume_order.`no` = #{queryPO.no}
        </if>
        <if test="queryPO.nickname!= null and queryPO.nickname!=''">
            and u.`nickname` like CONCAT('%', #{queryPO.nickname}, '%')
        </if>
        <if test="queryPO.phone!= null and queryPO.phone!=''">
            and u.`phone` like CONCAT('%', #{queryPO.phone}, '%')
        </if>
        <if test="queryPO.productName!= null and queryPO.productName!=''">
            and consume_order.product_name like CONCAT('%', #{queryPO.productName}, '%')
        </if>
        <if test="queryPO.orderSourceId!= null">
            and consume_order.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.userId!= null">
            and consume_order.user_id = #{queryPO.userId}
        </if>

        <if test="queryPO.employeeId != null and queryPO.employeeId > 0">
            and consume_order.employee_id = #{queryPO.employeeId}
        </if>

        <if test="queryPO.roomId != null and queryPO.roomId > 0">
            and consume_order.room_Id = #{queryPO.roomId}
        </if>

        <if test="queryPO.deviceId != null and queryPO.deviceId > 0">
            and consume_order.device_Id = #{queryPO.deviceId}
        </if>

        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND consume_order.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND (consume_order.sales_department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or consume_order.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                AND 1=2
            </if>
        </if>

    </sql>

    <select id="consumerReport" resultType="com.hishop.blw.repository.dto.ConsumeReportDTO">
        <include refid="consumerQuery"/>
        order by consume_order.verify_date desc, consume_order.order_no, consume_order.verify_date
    </select>


    <select id="consumerStatistics" resultType="com.hishop.blw.repository.dto.ConsumerStatisticsDTO">
        select
            sum(consumer_order_detail.product_quantity - consumer_order_detail.refund_quantity) 'quantity',
            sum(consumer_order_detail.payAmount + consumer_order_detail.payPointAmount - consumer_order_detail.refund_amount) 'amount',
            sum(consumer_order_detail.payAmount - consumer_order_detail.refund_amount) 'amountDetail',
            sum(consumer_order_detail.payPoint) 'point'
        from (
            <include refid="consumerQuery"/>
        ) as consumer_order_detail

    </select>

    <select id="countByConsumerReport" resultType="java.lang.Integer">
        select
            count(*)
        from (
            <include refid="consumerQuery"/>
        ) as consumer_order_detail

    </select>


    <select id="orderExportList" resultType="com.hishop.blw.repository.dto.OrderExportDTO">
        SELECT
            a.id,
            a.order_no orderNo,
            a.order_type orderType,
            a.order_status orderStatus,
            a.order_source_id orderSourceId,
            a.order_date orderDate,
            a.pay_date payDate,
            a.store_id storeId,
            a.department_id departmentId,
            a.user_id userId,
            a.proxy_user_id proxyUserId,
            a.recommended_user_id recommendedUserId,
            a.assistant_user_id assistantUserId,
            a.sales_user_id salesUserId,
            a.recommended_department_id recommendedDepartmentId,
            a.assistant_department_id assistantDepartmentId,
            a.sales_department_id salesDepartmentId,
            a.product_total_amount productTotalAmount,
            a.refund_total_amount orderTotalAmount,
            a.points,
            a.points_amount pointsAmount,
            a.actual_pay_amount actualPayAmount,
            a.points_amount pointsAmount,
            a.board_id boardId,
            a.eat_numbers eatNumbers,
            a.remark,
            a.adviser_user_ids adviserUserIds,
            it.order_id orderId,
            it.product_id productId,
            it.product_name productName,
            it.product_type productType,
            it.product_price productPrice,
            it.modify_amount modifyAmount,
            it.modify_discount modifyDiscount,
            it.pay_amount payAmount,
            it.actual_amount actualAmount,
            it.actual_points actualPoints,
            it.quantity,
            it.valid_to validTo,
            it.flag,
            it.refund_amount refundAmount,
            it.department_adviser_user_id departmentAdviserUserId,
            it.project_department_id projectDepartmentId,
            b.`name` userName,
            b.phone userPhone
        FROM blw_order_items it
                 LEFT JOIN blw_orders a on a.id = it.order_id
                 LEFT JOIN blw_users AS b ON a.user_id = b.id
        <where>
            <if test="queryPO.orderType != null">
                and a.order_type = #{queryPO.orderType}
            </if>
            <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
                and a.order_no = #{queryPO.orderNo}
            </if>
            <if test="queryPO.userId != null">
                and a.user_id = #{queryPO.userId}
            </if>
            <if test="queryPO.proxyUserId != null">
                and a.proxy_user_id = #{queryPO.proxyUserId}
            </if>

            <if test="queryPO.orderStatus !=null  and queryPO.orderStatus > 0">
                and a.order_status = #{queryPO.orderStatus}
            </if>
            <if test="queryPO.startDate != null and queryPO.endDate != null">
                AND a.create_date BETWEEN #{queryPO.startDate} AND #{queryPO.endDate}
            </if>
              <if test="queryPO.payStartDate != null and queryPO.payEndDate != null">
                        AND a.pay_date BETWEEN #{queryPO.payStartDate} AND #{queryPO.payEndDate}
                    </if>
            <if test="queryPO.keyWords != null and queryPO.keyWords != ''">
                AND (
                a.order_no = #{queryPO.keyWords}
                <if test="queryPO.orderType == 2">
                    OR EXISTS (SELECT 1 FROM blw_board WHERE id = b.board_id AND board_number = #{queryPO.keyWords})
                </if>
                OR b.`name` LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.phone LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR EXISTS (SELECT 1 FROM blw_order_items AS ai WHERE ai.order_no = a.order_no AND ai.product_name LIKE CONCAT('%', #{queryPO.keyWords}, '%'))
                )
            </if>
            <if test="queryPO.productCategoryId != null">
                and a.id in (
                SELECT orderItem.order_id from blw_order_items as orderItem
                left JOIN blw_products product  on orderItem.product_id = product.id
                where product.category_id = #{queryPO.productCategoryId})
            </if>
            <if test="queryPO.departmentId != null">
                and a.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.paymentId != null">
                and a.id in (
                select pament.order_id from blw_payments payment where pament.id = #{queryPO.paymentId}
                )
            </if>
            <if test="queryPO.orderSourceId != null">
                and a.order_source_id = #{queryPO.orderSourceId}
            </if>
            <if test="queryPO.salesUserId != null">
                and a.sales_user_id = #{queryPO.salesUserId}
            </if>
            <if test="queryPO.userNick != null and queryPO.userNick != ''">
                and b.`nickname` LIKE CONCAT('%', #{queryPO.userNick}, '%')
            </if>
            <if test="queryPO.userPhone != null and queryPO.userPhone != ''">
                and b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
            </if>
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and a.id in (
                select item.order_id from blw_order_items item
                left join blw_products product on item.product_id = product.id
                where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
                )
            </if>
        </where>
        order by a.create_date desc
    </select>



    <select id="financeConsumeReport" resultType="com.hishop.blw.repository.dto.FinanceConsumeReportDTO">
        select tarde.*,
            tarde.pay_amount - (
                select IFNULL(sum(r.refund_amount), 0)
                from blw_refund r
                    left join blw_refund_items ri on r.id = ri.refund_id
                    where ri.order_item_id = tarde.order_item_id
                    and DATE_FORMAT(r.handler_time, "%Y-%m" ) = DATE_FORMAT(now(), "%Y-%m" )
                    and r.`status` = 3
            ) 'amount'
        from (
            select o.id, o.store_id, o.department_id, o.pay_date 'date',
                oi.id 'order_item_id', oi.product_id, oi.product_name, oi.product_type,
                (case when oi.modify_discount is not null then oi.product_price * oi.modify_discount
                when oi.modify_amount is not null then oi.modify_amount
                else oi.actual_amount end) 'pay_amount'
            from blw_order_items oi
                left join blw_orders o on oi.order_id = o.id
            where oi.product_type in (1,3)
                and o.order_status in (2, 4,6,7)
                and EXISTS (
                    SELECT 1 from blw_payments p
                    LEFT JOIN blw_pay_methods pm ON IF (
                        p.card_number = '' OR p.card_number IS NULL,
                        p.pay_method_id, (
                        select mds.id from  blw_pay_methods mds
                        left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                    ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
                )

            <if test="queryPO.storeId != null">
                and o.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null">
                and o.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
                and o.department_id in
                <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="queryPO.payDateBegin != null">
                and o.pay_date >= #{queryPO.payDateBegin}
            </if>
            <if test="queryPO.payDateEnd != null">
                and o.pay_date &lt;= #{queryPO.payDateEnd}
            </if>
             <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>


            union all

            select o.id, o.store_id, o.department_id, v.create_date 'date',
                oi.id 'order_item_id', oi.product_id, oi.product_name, oi.product_type,
                rs.amount
            from blw_order_items oi
                left join blw_orders o on oi.order_id = o.id
                left join blw_user_services us on oi.id = us.order_item_id
                left join blw_reservation_services rs on us.id = rs.user_service_id
                left join blw_verify v on rs.id = v.reservation_id
            where oi.product_type in (2)
                and o.order_status in (2, 4,6,7)
                and (
                    (rs.`status` = 2)
                    or
                    (rs.`status` = 4 and DATE_FORMAT(v.create_date, "%Y-%m" ) != DATE_FORMAT(rs.refund_time, "%Y-%m" ))
                )
                and EXISTS (
                    SELECT 1 from blw_payments p
                    LEFT JOIN blw_pay_methods pm ON IF (
                        p.card_number = '' OR p.card_number IS NULL,
                        p.pay_method_id, (
                        select mds.id from  blw_pay_methods mds
                        left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                    ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
                )
            <if test="queryPO.storeId != null">
                and o.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null">
                and o.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
                and o.department_id in
                <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="queryPO.payDateBegin != null">
                and v.create_date >= #{queryPO.payDateBegin}
            </if>
            <if test="queryPO.payDateEnd != null">
                and v.create_date &lt;= #{queryPO.payDateEnd}
            </if>
             <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>

            union all


            select o.id, o.store_id, o.department_id, o.pay_date 'date',
                oi.id 'order_item_id', oie.product_id, oie.product_name, oi.product_type,
                oie.amount
            from blw_order_items oi
                left join blw_orders o on oi.order_id = o.id
                left join blw_order_items_extend oie on oi.id = oie.order_item_id
            where oi.product_type in (4, 5)
                and o.order_status in (2, 4,6,7)
                and oie.product_type in (1,3)
                and EXISTS (
                    SELECT 1 from blw_payments p
                    LEFT JOIN blw_pay_methods pm ON IF (
                        p.card_number = '' OR p.card_number IS NULL,
                        p.pay_method_id, (
                        select mds.id from  blw_pay_methods mds
                        left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                    ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
                )
            <if test="queryPO.storeId != null">
                and o.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null">
                and o.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
                and o.department_id in
                <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="queryPO.payDateBegin != null">
                and o.pay_date >= #{queryPO.payDateBegin}
            </if>
            <if test="queryPO.payDateEnd != null">
                and o.pay_date &lt;= #{queryPO.payDateEnd}
            </if>
             <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>

            union all

            select o.id, o.store_id, o.department_id, v.create_date 'date',
                oi.id 'order_item_id', oie.product_id, oie.product_name, oi.product_type,
                rs.amount
            from blw_order_items oi
                left join blw_orders o on oi.order_id = o.id
                left join blw_order_items_extend oie on oi.id = oie.order_item_id
                left join blw_user_services us on oi.id = us.order_item_id
                left join blw_reservation_services rs on us.id = rs.user_service_id
                left join blw_verify v on rs.id = v.reservation_id
            where oi.product_type in (4)
                and o.order_status in (2, 4,6,7)
                and oie.product_type = 2
                and (
                    (rs.`status` = 2)
                    or
                    (rs.`status` = 4 and DATE_FORMAT(v.create_date, "%Y-%m" ) != DATE_FORMAT(rs.refund_time, "%Y-%m" ))
                )
                and EXISTS (
                    SELECT 1 from blw_payments p
                    LEFT JOIN blw_pay_methods pm ON IF (
                        p.card_number = '' OR p.card_number IS NULL,
                        p.pay_method_id, (
                        select mds.id from  blw_pay_methods mds
                        left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                    ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
                )
            <if test="queryPO.storeId != null">
                and o.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null">
                and o.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
                and o.department_id in
                <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="queryPO.payDateBegin != null">
                and v.create_date >= #{queryPO.payDateBegin}
            </if>
            <if test="queryPO.payDateEnd != null">
                and v.create_date &lt;= #{queryPO.payDateEnd}
            </if>
             <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>

        ) as tarde

        union all
        -- 【跨月售后】商品/菜品/套餐，计算负金额
        select o.id, o.store_id, o.department_id, r.handler_time 'date',
            oi.id 'order_item_id', oi.product_id, oi.product_name, oi.product_type,
            r.refund_amount 'pay_amount',
            -r.refund_amount 'amount'
        from blw_refund r
            left join blw_refund_items ri on r.id = ri.refund_id
            left join blw_orders o on r.order_id = o.id
            left join blw_order_items oi on ri.order_item_id = oi.id
        where r.`status` = 3
            and o.order_status in (2, 4,6,7)
            and oi.product_type in (1,3,5)
            and DATE_FORMAT(r.handler_time, "%Y-%m" ) != DATE_FORMAT(o.pay_date, "%Y-%m" )
            and EXISTS (
                SELECT 1 from blw_payments p
                LEFT JOIN blw_pay_methods pm ON IF (
                    p.card_number = '' OR p.card_number IS NULL,
                    p.pay_method_id, (
                    select mds.id from  blw_pay_methods mds
                    left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
            )
        <if test="queryPO.storeId != null">
            and o.store_id = #{queryPO.storeId}
        </if>
        <if test="queryPO.departmentId != null">
            and o.department_id = #{queryPO.departmentId}
        </if>
        <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
            and o.department_id in
            <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryPO.payDateBegin != null">
            and r.handler_time >= #{queryPO.payDateBegin}
        </if>
        <if test="queryPO.payDateEnd != null">
            and r.handler_time &lt;= #{queryPO.payDateEnd}
        </if>
         <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>

        union all
        -- 【跨月退还】项目退还，计算负金额
        select o.id, o.store_id, o.department_id, v.create_date 'date',
            rs.id 'order_item_id', rs.product_id, rs.product_name, oi.product_type,
            rs.amount 'pay_amount',
            -rs.amount 'amount'
        from blw_reservation_services rs
            left join blw_verify v on rs.id = v.reservation_id
            left join blw_order_items oi on rs.order_item_id = oi.id
            left join blw_orders o on oi.order_id = o.id
        where rs.`status` = 4
            and DATE_FORMAT(v.create_date, "%Y-%m" ) != DATE_FORMAT(rs.refund_time, "%Y-%m" )
            and EXISTS (
                SELECT 1 from blw_payments p
                LEFT JOIN blw_pay_methods pm ON IF (
                    p.card_number = '' OR p.card_number IS NULL,
                    p.pay_method_id, (
                    select mds.id from  blw_pay_methods mds
                    left join blw_cards cd on mds.card_type = cd.card_type WHERE cd.id = p.pay_method_id )
                ) = pm.id  where pm.iz_finance_consume_report = 1 and p.order_id = o.id
            )
        <if test="queryPO.storeId != null">
            and o.store_id = #{queryPO.storeId}
        </if>
        <if test="queryPO.departmentId != null">
            and o.department_id = #{queryPO.departmentId}
        </if>
        <if test="queryPO.departmentIdList != null and queryPO.departmentIdList.size() > 0">
            and o.department_id in
            <foreach collection="queryPO.departmentIdList" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryPO.payDateBegin != null">
            and v.create_date >= #{queryPO.payDateBegin}
        </if>
        <if test="queryPO.payDateEnd != null">
            and v.create_date &lt;= #{queryPO.payDateEnd}
        </if>
         <if test="powerDTO.needFilter()">
<!--             <if test="powerDTO.isDepartmentManager()">-->
<!--                    and (o.proxy_user_id = #{powerDTO.loginUserId} or o.sales_user_id = #{powerDTO.loginUserId})-->
<!--                </if>-->
             <if test="powerDTO.hasStore()">
                    and o.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and o.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    and 1=2
                </if>
            </if>

    </select>




    <select id="orderSettlementPageList" resultMap="orderFootListMap">
        SELECT
            a.*
        FROM
        blw_orders AS a
        LEFT JOIN blw_users AS b ON a.user_id = b.id
        WHERE a.order_type = 1
            <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
                AND a.order_no LIKE CONCAT('%', #{queryPO.orderNo}, '%')
            </if>
            <if test="queryPO.userNick != null">
                AND (b.nickname LIKE CONCAT('%', #{queryPO.userNick}, '%') or  b.`name` LIKE CONCAT('%', #{queryPO.userNick}, '%'))
            </if>
            <if test="queryPO.userPhone != null">
                and  b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
            </if>
            <if test="queryPO.productName != null">
                and a.id in (
                select item.order_id from blw_order_items item
                left join blw_products product on item.product_id = product.id
                where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
                )
            </if>
            <if test="queryPO.productType != null">
                and a.id in (
                    select distinct item.order_id from blw_order_items item
                    where item.product_type = #{queryPO.productType}
                )
            </if>
            <if test="queryPO.departmentId != null">
                and a.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.storeId != null and  queryPO.storeId > 0">
                and a.store_id = #{queryPO.storeId}
            </if>
        <if test="queryPO.paymentId != null">
            <if test="queryPO.paymentId == 1">
                and a.id in (
                select payment.order_id from blw_payments payment where payment.card_number is not null
                )
            </if>
            <if test="queryPO.paymentId == 2">
                and a.points > 0
            </if>
            <if test="queryPO.paymentId != 1 and queryPO.paymentId != 2">
                and a.id in (
                select payment.order_id from blw_payments payment where payment.pay_method_id = #{queryPO.paymentId}
                )
            </if>
        </if>
        <if test="queryPO.orderSourceId != null">
            and a.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.startDate != null and queryPO.endDate != null">
                AND a.create_date BETWEEN #{queryPO.startDate} AND #{queryPO.endDate}
        </if>
        <if test="queryPO.payStartDate != null and queryPO.payEndDate">
            AND a.pay_date BETWEEN #{queryPO.payStartDate} AND #{queryPO.payEndDate}
        </if>
        <if test="queryPO.userNick != null and queryPO.userNick != ''">
            and (
            b.`nickname` LIKE CONCAT('%', #{queryPO.userNick}, '%')
            or b.`name` LIKE CONCAT('%', #{queryPO.userNick}, '%')
            )
        </if>
        <if test="queryPO.userPhone != null and queryPO.userPhone != ''">
            and b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
        </if>
        <if test="queryPO.productName != null and queryPO.productName != ''">
            and a.id in (
            select item.order_id from blw_order_items item
            left join blw_products product on item.product_id = product.id
            where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
            )
        </if>
        <if test="queryPO.keyWords != null and queryPO.keyWords != ''">
                AND (
                a.order_no LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.`name` LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.phone LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR EXISTS (SELECT 1 FROM blw_order_items AS ai WHERE ai.order_no = a.order_no AND ai.product_name LIKE CONCAT('%', #{queryPO.keyWords}, '%'))
                )
            </if>
            <if test="queryPO.orderStatus == null or queryPO.orderStatus == 0">

            </if>

            <if test="queryPO.orderStatus == 1 ">
                AND a.order_status = 1
            </if>
            <if test="queryPO.orderStatus == 2 ">
                AND a.order_status in(2,4,6,7)
            </if>
            <if test="queryPO.orderStatus == 3 ">
                AND a.order_status = 3
            </if>
            <if test="queryPO.orderStatus == 4 ">
                AND a.order_status = 5
            </if>
            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (a.sales_user_id = #{powerDTO.loginUserId} or a.proxy_user_id = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND a.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND (a.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or a.sales_department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>
        order by a.create_date desc
    </select>

    <select id="orderFootPageList" resultMap="orderFootListMap">
        SELECT
        a.*
        FROM
        blw_orders AS a
        LEFT JOIN blw_users AS b ON a.user_id = b.id
        <where>
              and a.order_type = 2
            <if test="queryPO.startDate != null and queryPO.endDate != null">
                AND a.order_date BETWEEN #{queryPO.startDate} AND #{queryPO.endDate}
            </if>
            <if test="queryPO.payStartDate != null and queryPO.payEndDate != null">
                AND a.pay_date BETWEEN #{queryPO.payStartDate} AND #{queryPO.payEndDate}
            </if>
            <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
                and a.order_no = #{queryPO.orderNo}
            </if>
            <if test="queryPO.keyWords != null and queryPO.keyWords != ''">
                AND (
                a.order_no LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.`name` LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.phone LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR EXISTS (SELECT 1 FROM blw_order_items AS ai WHERE ai.order_no = a.order_no AND ai.product_name LIKE CONCAT('%', #{queryPO.keyWords}, '%'))
                )
            </if>
            <if test="queryPO.userNick != null and queryPO.userNick !=''">
                AND (
                b.`nickname` LIKE CONCAT('%', #{queryPO.userNick}, '%')
                or b.`name` LIKE CONCAT('%', #{queryPO.userNick}, '%')
                )
            </if>
            <if test="queryPO.userPhone != null and queryPO.userPhone != ''">
                and b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
            </if>
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and a.id in (
                select item.order_id from blw_order_items item
                left join blw_products product on item.product_id = product.id
                where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
                )
            </if>
            <if test ="queryPO.userId != null and queryPO.userId > 0">
                and b.id = #{queryPO.userId}
            </if>
            <if test="queryPO.storeId != null and queryPO.storeId > 0">
                AND a.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null and queryPO.departmentId > 0">
                AND a.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.paymentId != null and  queryPO.paymentId > 0">
                <if test="queryPO.paymentId == 1">
                    and a.id in (
                    select payment.order_id from blw_payments payment where payment.card_number is not null
                    )
                </if>
                <if test="queryPO.paymentId == 2">
                    and a.points > 0
                </if>
                <if test="queryPO.paymentId != 1 and queryPO.paymentId != 2">
                    and a.id in (
                    select payment.order_id from blw_payments payment where payment.pay_method_id = #{queryPO.paymentId}
                    )
                </if>
            </if>
            <if test="queryPO.orderSourceId != null">
                and a.order_source_id = #{queryPO.orderSourceId}
            </if>
            <if test="queryPO.salesUserId != null">
                and a.sales_user_id = #{queryPO.salesUserId}
            </if>
            <if test="queryPO.orderStatus == null or queryPO.orderStatus == 0">

            </if>

            <if test="queryPO.orderStatus == 1 ">
                AND a.order_status = 1
            </if>
            <if test="queryPO.orderStatus == 2 ">
                AND a.order_status = 2
            </if>
            <if test="queryPO.orderStatus == 3 ">
                AND a.order_status = 3
            </if>
            <if test="queryPO.orderStatus == 4 ">
                AND a.order_status = 5
            </if>
            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (a.sales_user_id = #{powerDTO.loginUserId} or a.proxy_user_id = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND a.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND (a.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or a.sales_department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>

        </where>

        order by a.create_date desc
    </select>

    <select id="orderStaffPageList" resultMap="orderFootListMap">
        SELECT
        a.*
        FROM
        blw_orders AS a
        LEFT JOIN blw_users AS b ON a.user_id = b.id
        <where>
            <if test ="queryPO.userId != null and queryPO.userId > 0">
                and b.id = #{queryPO.userId}
            </if>
            <if test="queryPO.storeId != null and queryPO.storeId > 0">
                AND a.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.startDate != null and queryPO.endDate != null">
                AND a.order_date BETWEEN #{queryPO.startDate} AND #{queryPO.endDate}
            </if>
            <if test="queryPO.payStartDate != null and queryPO.payEndDate != null">
                AND a.pay_date BETWEEN #{queryPO.payStartDate} AND #{queryPO.payEndDate}
            </if>
            <if test="queryPO.productType != null and queryPO.productType > 0">
                AND EXISTS(
                    SELECT 1 FROM blw_order_items item WHERE item.order_id = a.id AND item.product_type = #{queryPO.productType}
                )
            </if>
            <if test="queryPO.departmentId != null and queryPO.departmentId > 0">
                AND a.department_id = #{queryPO.departmentId}
            </if>
            <if test="queryPO.paymentId != null and  queryPO.paymentId > 0">
                <if test="queryPO.paymentId == 1">
                    and a.id in (
                        select payment.order_id from blw_payments payment where payment.card_number is not null
                    )
                </if>
                <if test="queryPO.paymentId == 2">
                    and a.points > 0
                </if>
                <if test="queryPO.paymentId != 1 and queryPO.paymentId != 2">
                    and a.id in (
                        select payment.order_id from blw_payments payment where payment.pay_method_id = #{queryPO.paymentId}
                    )
                </if>
            </if>
            <if test="queryPO.orderSourceId != null">
                and a.order_source_id = #{queryPO.orderSourceId}
            </if>
            <if test="queryPO.salesUserId != null">
                and a.sales_user_id = #{queryPO.salesUserId}
            </if>
            <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
                and a.order_no = #{queryPO.orderNo}
            </if>
            <if test="queryPO.userNick != null and queryPO.userNick != ''">
                and (
                b.`nickname` LIKE CONCAT('%', #{queryPO.userNick}, '%')
                    or b.`name` LIKE CONCAT('%', #{queryPO.userNick}, '%')
                )
            </if>
            <if test="queryPO.userPhone != null and queryPO.userPhone != ''">
                and b.phone LIKE CONCAT('%', #{queryPO.userPhone}, '%')
            </if>
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and a.id in (
                select item.order_id from blw_order_items item
                left join blw_products product on item.product_id = product.id
                where product.product_name LIKE CONCAT('%', #{queryPO.productName}, '%')
                )
            </if>
            <if test="queryPO.keyWords != null and queryPO.keyWords != ''">
                AND (
                a.order_no LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.`name` LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR b.phone LIKE CONCAT('%', #{queryPO.keyWords}, '%')
                OR EXISTS (SELECT 1 FROM blw_order_items AS ai WHERE ai.order_no = a.order_no AND ai.product_name LIKE CONCAT('%', #{queryPO.keyWords}, '%'))
                )
            </if>
            <if test="queryPO.orderStatus == null or queryPO.orderStatus == 0">
                AND (
                    (a.order_type = 1 and a.order_status != 1)
                    or ( a.order_type = 2)
                )
            </if>

            <if test="queryPO.orderStatus == 1 ">
                AND (
                    a.order_status in (2,7) and a.order_type = 1 and EXISTS (
                        SELECT 1 FROM blw_order_items where product_type in (2,4) and order_id = a.id
                    )
                )
                AND NOT EXISTS ( SELECT 1 FROM blw_reservation_services WHERE `status` IN ( 1, 2 ) AND order_id = a.id )
            </if>
            <if test="queryPO.orderStatus == 2 ">
                AND (
                    (a.order_status = 6 and a.order_type = 1)
                    or (a.order_status = 1 and a.order_type = 2)
                )
            </if>
            <if test="queryPO.orderStatus == 3 ">
                AND (
                    (a.order_status = 4 and a.order_type = 1)
                    or (a.order_type = 1 AND a.order_status = 2 and  EXISTS (
                        SELECT 1 FROM blw_order_items where product_type in (1) and order_id = a.id
                    ))
                    or (a.order_status = 2 and a.order_type = 2)
                )
            </if>
            <if test="queryPO.orderStatus == 4 ">
                AND a.order_status = 5
            </if>
            <if test="queryPO.orderStatus == 5 ">
                AND a.order_status = 3
            </if>
            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (a.sales_user_id = #{powerDTO.loginUserId} or a.proxy_user_id = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND a.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND (a.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or a.sales_department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>
        </where>
        order by a.create_date desc
    </select>

    <select id="getAchievementReport2" resultType="com.hishop.blw.model.vo.report.SaleReportDto">
        SELECT
        bb.id,
        bb.order_no,
        bb.order_type,
        bb.order_status,
        bb.order_source_id,
        bb.order_date,
        bb.user_id,
        bb.userName,
        bb.phone,
        bb.recommended_user_id,
        bb.recommended_department_id,
        bb.sales_user_id,
        bb.sales_department_id,
        bb.assistant_user_id,
        bb.assistant_department_id,
        bb.adviser_user_ids,
        bb.remark,
        IFNULL(bb.points_amount, 0) AS pointsAmount,
        bb.eat_numbers,
        bb.orderId,
        bb.orderItemId,
        bb.productId,
        bb.quantity,
        bb.writeOffAmount,
        bb.rsProductName,
        bb.employeeId,
        bb.userServiceId,
        bb.reportType,
        bb.recordType
        FROM (

        /*销售单，复用销售查询SQL*/
        select o.id,
        o.order_no,
        o.order_type,
        o.order_status,
        o.order_source_id,
        o.order_date,
        o.user_id,
        u.name userName,
        u.phone,
        o.recommended_user_id,
        o.recommended_department_id,
        o.sales_user_id,
        o.sales_department_id,
        o.assistant_user_id,
        o.assistant_department_id,
        o.adviser_user_ids,
        o.remark,
        o.points_amount,
        o.eat_numbers,
        o.id as orderId,
        0 as orderItemId,
        0 as productId,
        0 as quantity,
        0 as writeOffAmount,
        '' as rsProductName,
        0 as employeeId,
        0 as userServiceId,
        1 as reportType,
        0 as recordType
        from
        <if test="queryPO.orderDateBegin != null">
            (select * from blw_orders where id in(select id from blw_orders where order_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
            union
            select order_id as "id" from blw_refund where `status`=3 and handler_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}))o
        </if>
        <if test="queryPO.orderDateBegin == null">
            blw_orders o
        </if>
        left join blw_users u on o.user_id=u.id
        where o.pay_date is not null
        <if test="queryPO.notInProductId != null">
            and  o.id not in (select it.order_id FROM blw_order_items it WHERE it.product_id = #{queryPO.notInProductId})
        </if>

        and exists(select p.id from blw_payments p join
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.card_type=m.card_type
        )b
        on p.pay_method_id=b.pay_method_id
        where p.order_id=o.id
        and b.iz_performance_report=true
        <if test="queryPO.payMethodId != null">
            and b.pay_method_id=#{queryPO.payMethodId}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                and b.iz_walkin = 0
            </if>
            <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                and b.iz_walkin = 1
            </if>
            <if test="queryPO.orderCategory == 5">
                and b.iz_walkin = 2
            </if>
        </if>
        )
        <if test="queryPO.orderItemExtendSelect != null">
            and exists(select 1 from (select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,oi.product_type,ie.project_department_id
            from blw_order_items_extend ie
            left join blw_order_items oi on ie.order_item_id = oi.id
            union all
            select oi.order_id,oi.id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id
            from blw_order_items oi)i
            where i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name=#{queryPO.productName}
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>

            )
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and o.order_no=#{queryPO.orderNo}
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>

        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        UNION ALL

        /*消耗单，项目和疗程下的项目*/
        select rs.id,
        rs.reservation_no as order_no,
        o.order_type,
        o.order_status,
        o.order_source_id,
        ver.verify_time as order_date,
        o.user_id,
        u.name userName,
        u.phone,
        o.recommended_user_id,
        o.recommended_department_id,
        o.sales_user_id,
        o.sales_department_id,
        o.assistant_user_id,
        o.assistant_department_id,
        o.adviser_user_ids,
        o.remark,
        o.points_amount,
        o.eat_numbers,
        o.id as orderId,
        rs.order_item_id as orderItemId,
        rs.product_id as productId,
        ver.quantity,
        rs.amount as writeOffAmount,
        rs.product_name as rsProductName,
        rs.employee_id as employeeId,
        rs.user_service_id as userServiceId,
        2 as reportType,
        1 as recordType
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        LEFT JOIN blw_users u ON o.user_id = u.id
        WHERE
        o.pay_date is not null
        <if test="queryPO.notInProductId != null">
            and o.id not in ( select it.order_id FROM blw_order_items it WHERE
            it.product_id = #{queryPO.notInProductId})
        </if>
        and exists(
        select p.id from blw_payments p join
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.card_type=m.card_type
        ) b on p.pay_method_id=b.pay_method_id
        where p.order_id=o.id
        and b.iz_performance_report=true
        <if test="queryPO.payMethodId != null">
            and b.pay_method_id=#{queryPO.payMethodId}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                and b.iz_walkin = 0
            </if>
            <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                and b.iz_walkin = 1
            </if>
            <if test="queryPO.orderCategory == 5">
                and b.iz_walkin = 2
            </if>
        </if>
        )
        <if test="queryPO.orderItemExtendSelect != null">
            and exists(select 1 from (
            select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,oi.product_type,ie.project_department_id
            from blw_order_items_extend ie
            left join blw_order_items oi on ie.order_item_id = oi.id
            union all
            select oi.order_id,oi
            .id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id
            from blw_order_items oi)i
            where i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name=#{queryPO.productName}
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>
            )
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and (o.order_no = #{queryPO.orderNo} or rs.reservation_no = #{queryPO.orderNo})
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and ver.verify_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>
        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        UNION ALL

        /*消耗单，项目和疗程下的项目 退还反向*/
        select rs.id,
        rs.reservation_no as order_no,
        o.order_type,
        o.order_status,
        o.order_source_id,
        rs.refund_time as order_date,
        o.user_id,
        u.name userName,
        u.phone,
        o.recommended_user_id,
        o.recommended_department_id,
        o.sales_user_id,
        o.sales_department_id,
        o.assistant_user_id,
        o.assistant_department_id,
        o.adviser_user_ids,
        o.remark,
        o.points_amount,
        o.eat_numbers,
        o.id as orderId,
        rs.order_item_id as orderItemId,
        rs.product_id as productId,
        ver.quantity * -1 as quantity,
        rs.amount * -1 as writeOffAmount,
        rs.product_name as rsProductName,
        rs.employee_id as employeeId,
        rs.user_service_id as userServiceId,
        2 as reportType,
        1 as recordType
        FROM blw_reservation_services rs
        INNER JOIN blw_verify ver ON rs.id = ver.reservation_id
        INNER JOIN blw_orders o ON rs.order_id = o.id
        LEFT JOIN blw_users u ON o.user_id = u.id
        WHERE
        rs.`status` = 4
        AND o.pay_date is not null
        <if test="queryPO.notInProductId != null">
            and o.id not in ( select it.order_id FROM blw_order_items it WHERE
            it.product_id = #{queryPO.notInProductId})
        </if>
        and exists(
        select p.id from blw_payments p join
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.card_type=m.card_type
        ) b on p.pay_method_id=b.pay_method_id
        where p.order_id=o.id
        and b.iz_performance_report=true
        <if test="queryPO.payMethodId != null">
            and b.pay_method_id=#{queryPO.payMethodId}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                and b.iz_walkin = 0
            </if>
            <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                and b.iz_walkin = 1
            </if>
            <if test="queryPO.orderCategory == 5">
                and b.iz_walkin = 2
            </if>
        </if>
        )
        <if test="queryPO.orderItemExtendSelect != null">
            and exists(select 1 from (
            select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,oi.product_type,ie.project_department_id
            from blw_order_items_extend ie
            left join blw_order_items oi on ie.order_item_id = oi.id
            union all
            select oi.order_id,oi
            .id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id
            from blw_order_items oi)i
            where i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name=#{queryPO.productName}
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>
            )
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and (o.order_no = #{queryPO.orderNo} or rs.reservation_no = #{queryPO.orderNo})
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.orderDateBegin != null">
            and rs.refund_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>
        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        UNION ALL

        /** 消耗 商品 菜品*/
        select o.id,
        o.order_no,
        o.order_type,
        o.order_status,
        o.order_source_id,
        o.order_date,
        o.user_id,
        u.name userName,
        u.phone,
        o.recommended_user_id,
        o.recommended_department_id,
        o.sales_user_id,
        o.sales_department_id,
        o.assistant_user_id,
        o.assistant_department_id,
        o.adviser_user_ids,
        o.remark,
        o.points_amount,
        o.eat_numbers,
        o.id as orderId,
        0 as orderItemId,
        0 as productId,
        0 as quantity,
        0 as writeOffAmount,
        '' as rsProductName,
        0 as employeeId,
        0 as userServiceId,
        2 as reportType,
        2 as recordType
        from
        <if test="queryPO.orderDateBegin != null">
            (select * from blw_orders where id in(select id from blw_orders where order_date BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}
            union
            select order_id as "id" from blw_refund where `status`=3 and handler_time BETWEEN #{queryPO.orderDateBegin} AND #{queryPO.orderDateEnd}))o
        </if>
        <if test="queryPO.orderDateBegin == null">
            blw_orders o
        </if>
        left join blw_users u on o.user_id=u.id
        where o.pay_date is not null
        <if test="queryPO.notInProductId != null">
            and  o.id not in (select it.order_id FROM blw_order_items it WHERE it.product_id = #{queryPO.notInProductId})
        </if>
        and (
        exists(select oi.id from blw_order_items oi where oi.order_id=o.id and oi.product_type in (1, 3))
        or
        exists(select oie.id from blw_order_items_extend oie where oie.order_id=o.id and oie.product_type in (1, 3))
        )
        and exists(select p.id from blw_payments p join
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.card_type=m.card_type
        )b
        on p.pay_method_id=b.pay_method_id
        where p.order_id=o.id
        and b.iz_performance_report=true
        <if test="queryPO.payMethodId != null">
            and b.pay_method_id=#{queryPO.payMethodId}
        </if>
        <if test="queryPO.orderCategory != null">
            <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 2">
                and b.iz_walkin = 0
            </if>
            <if test="queryPO.orderCategory == 3 or queryPO.orderCategory == 4">
                and b.iz_walkin = 1
            </if>
            <if test="queryPO.orderCategory == 5">
                and b.iz_walkin = 2
            </if>
        </if>
        )
        <if test="queryPO.orderItemExtendSelect != null">
            and exists(select 1 from (select ie.order_id,ie.order_item_id,ie.product_id,ie.product_name,oi.product_type,ie.project_department_id
            from blw_order_items_extend ie
            left join blw_order_items oi on ie.order_item_id = oi.id
            union all
            select oi.order_id,oi.id as "order_item_id",oi.product_id,oi.product_name,oi.product_type,oi.project_department_id
            from blw_order_items oi)i
            where i.order_id=o.id
            <if test="queryPO.productName != null and queryPO.productName != ''">
                and i.product_name=#{queryPO.productName}
            </if>
            <if test="queryPO.productType != null">
                and i.product_type=#{queryPO.productType}
            </if>
            <if test="queryPO.orderCategory != null">
                <if test="queryPO.orderCategory == 1 or queryPO.orderCategory == 3">
                    and i.product_type = 2
                </if>
                <if test="queryPO.orderCategory == 2 or queryPO.orderCategory == 4">
                    and i.product_type = 4
                </if>
            </if>
            <if test="queryPO.projectDepartmentId != null">
                and i.project_department_id = #{queryPO.projectDepartmentId}
            </if>

            )
        </if>
        <if test="queryPO.orderNo != null and queryPO.orderNo != ''">
            and o.order_no=#{queryPO.orderNo}
        </if>
        <if test="queryPO.orderSourceId != null">
            and o.order_source_id = #{queryPO.orderSourceId}
        </if>
        <if test="queryPO.storeId != null">
            and o.store_id=#{queryPO.storeId}
        </if>
        <if test="queryPO.salesDepartmentId != null">
            and o.sales_department_id=#{queryPO.salesDepartmentId}
        </if>
        <if test="queryPO.recommendedUserId != null">
            and o.recommended_user_id=#{queryPO.recommendedUserId}
        </if>
        <if test="queryPO.assistantUserId != null">
            and o.assistant_user_id=#{queryPO.assistantUserId}
        </if>
        <if test="queryPO.salesUserId != null">
            and o.sales_user_id=#{queryPO.salesUserId}
        </if>

        <if test="queryPO.nickname != null and queryPO.nickname != ''">
            and u.nickname=#{queryPO.nickname}
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
            and u.phone=#{queryPO.phone}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>

        /*结束*/
        ) bb WHERE 1=1
        <if test="queryPO.reportType != null">
            AND bb.reportType = #{queryPO.reportType}
        </if>
        ORDER BY bb.reportType ASC, bb.order_date DESC, bb.order_no ASC
    </select>

    <!-- 根据商品分类统计非项目类商品的支付金额（经营报表按月统计）-->
    <select id="listManageByCategoryIds" resultType="com.hishop.blw.model.vo.report.ManagerOrderAmountVO">
        select (case when pc.parent_category_id=0 then pc.id else pc.parent_category_id end) as "oneCategoryId"
             ,DATE_FORMAT(oi.create_date,'%Y-%m') as "month",sum(oi.split_pay_amount) as "splitPayAmount"
        from blw_order_products_payment oi join blw_products p on oi.product_id=p.id join blw_product_categories pc on p.category_id=pc.id
        join blw_orders o on oi.order_id=o.id
        where oi.product_type in(1,3)
        <if test="storeId != null">
            and o.store_id=#{storeId}
        </if>
        <if test="departId != null">
            and o.department_id=#{departId}
        </if>
        and (pc.id in
        <foreach collection="categoryIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        or pc.parent_category_id in
        <foreach collection="categoryIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        )
        and exists(select 1 from
            (select a.id as "pay_method_id",a.* from blw_pay_methods a
            union all
            select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
            )b
            where oi.pay_method_id=b.pay_method_id
            and b.iz_operating_report=true)
        and YEAR(oi.create_date) = #{year}
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        group by oneCategoryId,month
    </select>

    <select id="listManageReportDetailNew" resultType="com.hishop.blw.model.vo.report.ManageReportDetailVO">
        select pc.id as "secondCategoryId",pc.parent_category_id as "categoryId",pc.category_name as "secondCategoryName",oi.product_id,oi.product_type,oi.product_name,
               ROUND(sum(ifnull(case when oi.product_type=2 then (ifnull(bv.amount,0) + ifnull(bv.point_amount,0))* oi.split_pay_amount / us.old_service_total_amount else ifnull(oi.split_pay_amount,0) end,0)),2) as "amount",
               sum(case when oi.product_type=2 then bv.quantity else 0 end) as "quantity"
        from blw_order_products_payment oi join blw_orders o on oi.order_id=o.id
                 left join (select id as "order_item_id",product_id,quantity from blw_order_items where DATE_FORMAT(create_date,'%Y-%m')= #{queryPO.monthStr}
                       union all
                       select order_item_id,product_id,quantity from blw_order_items_extend where DATE_FORMAT(create_date,'%Y-%m')= #{queryPO.monthStr})a on oi.order_item_id=a.order_item_id and oi.product_id=a.product_id
                 join blw_products p on oi.product_id=p.id
                 join blw_product_categories pc on p.category_id=pc.id
                 left join blw_reservation_services rs on oi.order_item_id=rs.order_item_id and oi.product_id=rs.product_id and rs.`status`=2
                 left join blw_user_services us ON rs.user_service_id = us.id
                 left join blw_verify bv on rs.id=bv.reservation_id
        where (oi.product_type in(1,3) or (oi.product_type=2 and bv.id is not null))
        <if test="queryPO.categoryId != null">
          and (pc.id=#{queryPO.categoryId} or pc.parent_category_id=#{queryPO.categoryId})
        </if>
        <if test="queryPO.secondCategoryId != null">
          and pc.id= #{queryPO.secondCategoryId}
        </if>
          and exists(select 1 from
            (select a.id as "pay_method_id",a.* from blw_pay_methods a
             union all
             select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
            )b where oi.pay_method_id=b.pay_method_id and b.iz_operating_report=true)
          and (case when oi.product_type=2 then DATE_FORMAT(bv.create_date,'%Y-%m') else DATE_FORMAT(oi.create_date,'%Y-%m') end)= #{queryPO.monthStr}
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        group by oi.product_id
    </select>
    <select id="listManageReportOrderNew" resultType="com.hishop.blw.model.vo.report.ManageReportOrderViewNewVO">
        select o.order_no,(case when rs.reservation_no is not null then rs.reservation_no else o.order_no end) as "reservationNo",u.`name` as "userName",u.phone,oi.product_id,oi.product_type,oi.product_name,
        ROUND(sum(ifnull(case when oi.product_type=2 then (ifnull(bv.amount,0) + ifnull(bv.point_amount,0))* oi.split_pay_amount / us.old_service_total_amount else ifnull(oi.split_pay_amount,0) end,0)),2) as "amount",
        sum(case when oi.product_type=2 then bv.quantity else 0 end) as "quantity"
        from blw_order_products_payment oi
        join blw_orders o on oi.order_id=o.id
        left join blw_users u on o.user_id=u.id
        join blw_products p on oi.product_id=p.id
        join blw_product_categories pc on p.category_id=pc.id
        left join blw_reservation_services rs on oi.order_item_id=rs.order_item_id and oi.product_id=rs.product_id and rs.`status`=2
        left join blw_user_services us ON rs.user_service_id = us.id
        left join blw_verify bv on rs.id=bv.reservation_id
        where (oi.product_type in(1,3) or (oi.product_type=2 and bv.id is not null))
        <if test="queryPO.categoryId != null">
            and (pc.id=#{queryPO.categoryId} or pc.parent_category_id=#{queryPO.categoryId})
        </if>
        <if test="queryPO.secondCategoryId != null">
            and pc.id= #{queryPO.secondCategoryId}
        </if>
        and exists(select 1 from
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
        )b where oi.pay_method_id=b.pay_method_id and b.iz_operating_report=true)
        and (case when oi.product_type=2 then DATE_FORMAT(bv.create_date,'%Y-%m') else DATE_FORMAT(oi.create_date,'%Y-%m') end)= #{queryPO.monthStr}
        and oi.product_id=#{queryPO.productId}
        <if test="queryPO.reservationNo != null and queryPO.reservationNo != ''">
            and (o.order_no LIKE CONCAT('%', #{queryPO.reservationNo}, '%') or rs.reservation_no LIKE CONCAT('%', #{queryPO.reservationNo}, '%'))
        </if>
        <if test="queryPO.phone != null and queryPO.phone != ''">
          and u.phone LIKE CONCAT('%', #{queryPO.phone}, '%')
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        group by reservationNo
    </select>

    <select id="listQuantityByProductIds" resultType="com.hishop.blw.repository.dto.OrderQuantityDTO">
        select a.product_id,sum(ifnull(a.quantity,0)) as "totalQuantity"
        from (select oi.product_id,oi.quantity
              from blw_order_items oi join blw_orders o on oi.order_id=o.id
              where o.order_status in(2,4,5,6,7) and oi.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
               and DATE_FORMAT(o.order_date,'%Y-%m')=#{month}
              union all
              select ie.product_id,ie.quantity
              from blw_order_items_extend ie join blw_orders o on ie.order_id=o.id
              where o.order_status in(2,4,5,6,7) and ie.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
                and DATE_FORMAT(o.order_date,'%Y-%m')=#{month})a
        group by a.product_id
    </select>
    <select id="listOrderQuantityByProductIds" resultType="com.hishop.blw.repository.dto.OrderQuantityDTO">
        select a.order_no,a.product_id,sum(ifnull(a.quantity,0)) as "totalQuantity"
        from (select o.order_no,oi.product_id,oi.quantity
              from blw_order_items oi join blw_orders o on oi.order_id=o.id
              where o.order_status in(2,4,5,6,7) and oi.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
               and DATE_FORMAT(o.order_date,'%Y-%m')=#{month}
              union all
              select o.order_no,ie.product_id,ie.quantity
              from blw_order_items_extend ie join blw_orders o on ie.order_id=o.id
              where o.order_status in(2,4,5,6,7) and ie.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
                and DATE_FORMAT(o.order_date,'%Y-%m')=#{month})a
        group by a.order_no,a.product_id
    </select>

</mapper>