<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.UserPointsMapper">

    <update id="adjustPoints">
        update blw_user_points
        set available_points = available_points + #{adjustPoints}
        where id = #{id} and available_points + #{adjustPoints} >= 0
    </update>

    <update id="subPoints">
        update blw_user_points
        set available_points = available_points - #{subPoints}
        where user_id = #{userId} and store_id = #{storeId} and available_points >= #{subPoints}
    </update>

      <update id="addPoints">
        update blw_user_points
        set available_points = available_points + #{addPoints}
        where user_id = #{userId} and store_id = #{storeId}
    </update>

    <update id="updateByStore">
        update blw_user_points
        set available_points = available_points*#{pointsTimes}
        where store_id = #{storeId}
    </update>
</mapper>