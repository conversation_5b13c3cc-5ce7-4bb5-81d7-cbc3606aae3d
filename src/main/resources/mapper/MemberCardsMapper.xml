<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.MemberCardsMapper">

    <update id="upAmount">
        update blw_member_cards as mc
        join (
            select template_card_id from blw_member_cards where id = #{id}
        ) as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount + #{upAmount}
        where mc.available_amount + #{upAmount} &gt;= 0
    </update>

    <update id="recharge">
        update blw_member_cards as mc
        join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
        ) as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount + #{rechargeAmount},
            mc.total_amount = mc.total_amount + #{rechargeAmount}
        where mc.available_amount + #{rechargeAmount} &gt;= 0
    </update>
    <update id="freeze">
        update blw_member_cards mc
        join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
        )  as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount - #{freezeAmount},
            mc.frozen_amount = mc.frozen_amount + #{freezeAmount}
        where mc.available_amount &gt;= #{freezeAmount}
    </update>

    <update id="subAmount">
        update blw_member_cards mc
        join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
        )  as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount - #{subAmount}
        where mc.available_amount &gt;= #{subAmount}
    </update>

    <update id="addAmount">
        update blw_member_cards mc
        join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
        )  as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount + #{addAmount}
    </update>
    <select id="listByUserIds" resultType="com.hishop.blw.repository.dto.UserCardDTO">
        SELECT
            mc.id,
            mc.store_id AS 'storeId',
            mc.user_id AS 'userId',
            tc.card_id AS 'cardId',
            c.card_type AS 'cardType',
            c.card_name AS 'cardName',
            sum( mc.available_amount ) AS 'availableAmount',
            mc.card_number AS 'cardNumber'
        FROM
            blw_member_cards mc join blw_template_cards tc on mc.template_card_id = tc.id
            LEFT JOIN blw_cards c ON tc.card_id = c.id
        WHERE
            mc.status = 0
            and mc.user_id IN
            <foreach collection="userIds" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
        GROUP BY
            mc.user_id,
            tc.card_id,
            c.card_name

    </select>


    <select id="listByUserIdsGroupStore" resultType="com.hishop.blw.repository.dto.UserCardDTO">
        SELECT
            mc.id,
            mc.store_id AS 'storeId',
            mc.user_id AS 'userId',
            tc.card_id AS 'cardId',
            c.card_type AS 'cardType',
            c.card_name AS 'cardName',
            sum( mc.available_amount ) AS 'availableAmount',
            mc.card_number AS 'cardNumber'
        FROM
            blw_member_cards mc join blw_template_cards tc on mc.template_card_id = tc.id
            LEFT JOIN blw_cards c ON tc.card_id = c.id
        WHERE
            mc.status = 0
            and mc.user_id IN
            <foreach collection="userIds" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
        GROUP BY
            mc.user_id,
            tc.card_id,
            c.card_name,
            mc.store_id
    </select>

    <select id="listByCardIds" resultType="com.hishop.blw.repository.dto.MemberCardsDTO">
        select tc.card_id "cardId",c.card_name "cardName",mc.id,mc.user_id "userId",mc.template_card_id "templateCardId",
               mc.card_type "cardType",mc.card_number "cardNumber",mc.store_id "storeId",mc.available_amount "availableAmount",c.pay_method_id "payMethodId"
            from blw_cards c join blw_template_cards tc on c.id=tc.card_id join blw_member_cards mc on tc.id=mc.template_card_id
        where
        mc.status = 0
           and c.id in
        <foreach collection="cardIds" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and mc.user_id=#{userId}
    </select>

    <select id="listByUserId" resultType="com.hishop.blw.repository.entity.MemberCards">
        SELECT 
            mc.*,
            c.card_name as cardName
        FROM 
            blw_member_cards mc
            LEFT JOIN blw_cards c ON mc.card_id = c.id
        WHERE
            mc.status = 0
            and  mc.user_id = #{userId}
    </select>

    <update id="cancelRecharge">
        update blw_member_cards mc
            join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
            )  as subquery on mc.template_card_id = subquery.template_card_id
            set mc.available_amount = mc.available_amount - #{cancelAmount},
                mc.total_amount = mc.total_amount - #{cancelAmount}
        where mc.available_amount &gt;= #{cancelAmount}
    </update>

    <update id="unfreeze">
        update blw_member_cards mc
        join (
            SELECT template_card_id FROM blw_member_cards WHERE id = #{id}
        ) as subquery on mc.template_card_id = subquery.template_card_id
        set mc.available_amount = mc.available_amount + #{unfreezeAmount},
            mc.frozen_amount = mc.frozen_amount - #{unfreezeAmount}
        where  mc.frozen_amount &gt;= #{unfreezeAmount}
    </update>

    <delete id="deleteUserShareCards">
        DELETE FROM blw_member_cards
        WHERE user_id = #{userId}
        AND card_type = 3
        AND store_id = #{storeId}
        AND template_card_id in
        (
        SELECT id FROM blw_template_cards tc WHERE blw_member_cards.template_card_id = tc.id AND tc.user_id != #{userId}
        )
    </delete>
    <select id="listStoreId" resultType="Long">
        SELECT DISTINCT bcs.store_id from blw_cards_store bcs
        WHERE bcs.card_id in (
            SELECT DISTINCT tc.card_id FROM blw_member_cards as cd
                                                left JOIN blw_template_cards as tc on cd.template_card_id = tc.id
                                                LEFT JOIN blw_cards_store as cs on cs.card_id = tc.card_id
            WHERE cd.user_id = #{userId} AND cd.store_id = #{storeId})
    </select>


    <select id="listByUserStoreCard" resultType="com.hishop.blw.repository.entity.MemberCards">
        select t.* from blw_member_cards t inner join blw_template_cards t1  on t.template_card_id = t1.id
        where t.status = 0 AND t1.card_id = #{cardId} and t1.user_id = #{userId} and t1.store_id = #{storeId}
        and t.store_id = #{storeId} and t.user_id = #{userId}
    </select>

    <update id="updateCardTypeByCardId">
        update blw_member_cards
        set card_type = #{cardType}
        where template_card_id in (
            SELECT id FROM blw_template_cards WHERE card_id = #{cardId}
        )
    </update>
</mapper>