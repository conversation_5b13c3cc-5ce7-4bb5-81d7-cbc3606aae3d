<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.DepartmentMapper">


    <select id="listByStoreId" resultType="com.hishop.blw.repository.entity.Department">
        select * from blw_department
        where id in (
            select department_id from blw_store_service_areas where store_id = #{storeId}
        )
    </select>

    <select id="listByStoreIds" resultType="com.hishop.blw.repository.entity.Department">
        select * from blw_department
        where id in (
            select department_id from blw_store_service_areas where store_id in
            <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        )
    </select>
    <select id="pageByRelationUser" resultType="com.hishop.blw.repository.entity.Department">
        select bd.*
        from blw_department bd
                 join blw_user_stores bus on bd.id=bus.department_id
                 join blw_users bu on bu.id=bus.user_id and bu.user_type=1
        where bu.id=#{userId} and bu.manager=0
        order by bd.sort asc

    </select>
    <select id="pageByPlat" resultType="com.hishop.blw.repository.entity.Department">
        select *
        FROM blw_department
        order by sort asc
    </select>

    <select id="listPage" resultType="com.hishop.blw.repository.entity.Department">
        SELECT DISTINCT d.* FROM blw_store_service_areas  st
        LEFT join blw_department d  on st.department_id = d.id
        <where>
            <if test="queryPO.storeId != null and queryPO.storeId > 0">
                AND st.store_id = #{queryPO.storeId}
            </if>
            <if test="queryPO.departmentId != null and queryPO.departmentId > 0">
                AND d.id = #{queryPO.departmentId}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasDepartment()">
                    AND d.id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </where>

        ORDER BY d.sort,d.id asc
    </select>
</mapper>