<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.OrderItemsMapper">

    <select id="getByOrderId" resultType="com.hishop.blw.model.vo.order.OrderItemsVO">
        SELECT item.*,product.department_id as departId,product.category_id as categoryId FROM blw_order_items item
        LEFT JOIN blw_products  product on item.product_id = product.id
        WHERE item.order_id = #{orderId} and item.flag != 1
    </select>

    <select id="listById" resultType="com.hishop.blw.model.vo.order.OrderItemsVO">
        SELECT item.*,product.department_id as departId,product.category_id as categoryId FROM blw_order_items item
                                                                                                   LEFT JOIN blw_products  product on item.product_id = product.id
        WHERE item.id = #{id} and item.flag != 1
    </select>

    <select id="listByOrderIdsAndDate" resultType="com.hishop.blw.repository.entity.OrderItems">
        select b.* from
        <if test="startTime == null">
            blw_order_items b
        </if>
        <if test="startTime != null">
            (select * from blw_order_items where id in(
                select id from blw_order_items where pay_date between #{startTime} and #{endTime}
                union
                select ri.order_item_id as "id" from blw_refund_items ri join blw_refund r on ri.refund_id = r.id where r.handler_time between #{startTime} and #{endTime}
            ))b
        </if>
        where b.order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="productName != null and productName != ''">
            and b.id in(
            select id from blw_order_items where product_name like concat('%',#{productName},'%')
            union
            select order_item_id as "id" from blw_order_items_extend where product_name like concat('%',#{productName},'%')
            )
        </if>
        <if test="projectDepartmentId != null">
            and b.id in(
            select id from blw_order_items where project_department_id = #{projectDepartmentId}
            union
            select order_item_id as "id" from blw_order_items_extend where project_department_id = #{projectDepartmentId}
            )
        </if>
        and b.flag != 1
    </select>

    <select id="listAllItemByOrderId" resultType="com.hishop.blw.model.vo.order.OrderItemsVO">
        select ie.order_id,null as "order_no",ie.order_item_id as "id",ie.id as "extendId",ie.product_id,ie.product_name,ie.product_type,ie.product_price as "actual_amount",ie.quantity,
        ie.project_department_id,ie.create_date
        from blw_order_items_extend ie
        where ie.order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="productName != null and productName != ''">
            and ie.product_name like concat('%',#{productName},'%')
        </if>
        <if test="projectDepartmentId != null">
            and ie.project_department_id = #{projectDepartmentId}
        </if>
        union all
        select oi.order_id,oi.order_no,oi.id,null as "extendId",oi.product_id,oi.product_name,oi.product_type,oi.actual_amount,oi.quantity,
               oi.project_department_id,oi.create_date
        from blw_order_items oi
        where
        oi.product_type  IN ( 1,2,3 ) AND oi.order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="productName != null and productName != ''">
            and oi.product_name like concat('%',#{productName},'%')
        </if>
        <if test="projectDepartmentId != null">
            and oi.project_department_id = #{projectDepartmentId}
        </if>
        and oi.flag != 1
    </select>
</mapper>