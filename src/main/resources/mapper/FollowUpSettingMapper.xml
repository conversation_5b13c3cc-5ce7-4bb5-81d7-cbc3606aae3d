<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.FollowUpSettingMapper">

    <resultMap type="com.hishop.blw.repository.entity.FollowUpSetting" id="FollowUpSettingMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="dayNum" column="day_num" jdbcType="INTEGER"/>
        <result property="smsCode" column="sms_code" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

