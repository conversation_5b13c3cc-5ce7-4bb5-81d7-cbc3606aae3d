<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.CardsMapper">

    <select id="getByTemplateCardId" resultType="com.hishop.blw.repository.dto.CardDTO">
        select c.id as "cardId", c.card_type as "cardType", t.id as "templateCardId",c.card_name as "cardName"
        from blw_cards c join blw_template_cards t on c.id=t.card_id
        where t.id in
        <foreach collection="templateCardIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="pageList" resultType="com.hishop.blw.repository.entity.Cards">
        select t.*
        from blw_cards t
        <where>
            <if test="cardName != null">
                t.card_name like concat('%', #{cardName}, '%')
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasStore()">
                    t.id in (select card_id from blw_cards_store where store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="!powerDTO.hasStore()">
                    1=2
                </if>
            </if>
        </where>
    </select>
    

    <select id="listByStoreId" resultType="com.hishop.blw.repository.entity.Cards">
        select t.*
        from blw_cards t join blw_cards_store s on t.id=s.card_id
        where s.store_id=#{storeId} and t.`status`=1
    </select>

    <select id="listByUserDataPower" resultType="com.hishop.blw.repository.entity.Cards">
        select t.*
        from blw_cards t
         <if test="powerDTO.needFilter() and powerDTO.hasStore()">
            join blw_cards_store s on t.id=s.card_id                                   
        </if>
         <where>
         <if test="powerDTO.needFilter() and powerDTO.hasStore()">            
             s.store_id IN
             <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                #{item}
             </foreach>   
            </if>
        </where>
    </select>

    <select id="listShareCardByStoreId" resultType="com.hishop.blw.repository.entity.Cards">
        SELECT b.* FROM blw_cards_store as a
                            LEFT JOIN blw_cards as b on a.card_id = b.id
        WHERE a.store_id =#{storeId} AND b.`status` = 1 AND b.card_type = 3
    </select>
    <select id="listShareCardStore" resultType="com.hishop.blw.repository.dto.CardsStoreDTO">
        SELECT bs.id,bcs.store_id,bs.card_type FROM blw_cards_store bcs
                                                        LEFT JOIN blw_cards bs on bcs.card_id = bs.id
        WHERE bcs.card_id in
              (
                  SELECT card_id FROM blw_cards_store cs
                                          LEFT JOIN blw_cards c on cs.card_id = c.id
                  WHERE cs.store_id = #{storeId} AND c.card_type =3 AND c.`status` = 1
              )
    </select>
    <select id="listByCardId" resultType="com.hishop.blw.repository.dto.CardsStoreDTO">
        SELECT bs.id,bcs.store_id,bs.card_type FROM blw_cards_store bcs
        LEFT JOIN blw_cards bs on bcs.card_id = bs.id
        WHERE bs.id = #{cardId}
    </select>
</mapper>