<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.RefundItemsMapper">


    <select id="listByOrderItemId" resultType="com.hishop.blw.repository.entity.RefundItems">
        SELECT
            ri.*
        FROM
            blw_refund r
            LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
        WHERE
            r.`status` IN ( 1, 3 )
          AND ri.order_item_id = #{orderItemId}
    </select>


    <select id="listByOrderId" resultType="com.hishop.blw.repository.entity.RefundItems">
        SELECT
            ri.*
        FROM
            blw_refund r
            LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
        WHERE
            r.`status` = ${refundStatus} and r.order_id = ${orderId}
    </select>



    <select id="listByOrderItemIds" resultType="com.hishop.blw.repository.dto.RefundItemsDTO">
        SELECT
            r.status,
            ri.*
        FROM
            blw_refund r
                LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
        WHERE
            r.`status` IN ( 1, 3 )
            AND ri.order_item_id in
            <foreach collection="orderItemIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>


</mapper>