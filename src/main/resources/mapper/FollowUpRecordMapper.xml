<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.FollowUpRecordMapper">

    <resultMap type="com.hishop.blw.repository.entity.FollowUpRecord" id="FollowUpRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="userHomeId" column="user_home_id" jdbcType="INTEGER"/>
        <result property="intentionalLevel" column="intentional_level" jdbcType="VARCHAR"/>
        <result property="followUpMethod" column="follow_up_method" jdbcType="VARCHAR"/>
        <result property="followUpDate" column="follow_up_date" jdbcType="TIMESTAMP"/>
        <result property="followUpContent" column="follow_up_content" jdbcType="VARCHAR"/>
        <result property="followUpLabel" column="follow_up_label" jdbcType="VARCHAR"/>
        <result property="nextDate" column="next_date" jdbcType="TIMESTAMP"/>
        <result property="followUpPlan" column="follow_up_plan" jdbcType="VARCHAR"/>
        <result property="imgs" column="imgs" jdbcType="VARCHAR"/>
        <result property="izSms" column="iz_sms" jdbcType="BOOLEAN"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getLatestFollowUpRecordListByUserHomeIds" resultMap="FollowUpRecordMap">
        SELECT t1.*
        FROM blw_follow_up_record t1
                 JOIN (
            SELECT user_id, MAX(id) AS max_id
            FROM blw_follow_up_record
            WHERE user_id IN (
                <foreach collection="userHomeIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
            GROUP BY user_id
        ) t2 ON t1.id = t2.max_id
    </select>

</mapper>

