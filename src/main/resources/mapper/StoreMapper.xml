<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.StoreMapper">


    <sql id="query">
        SELECT * FROM blw_store
        <where>
            <if test="dto.departmentId != null and dto.departmentId != ''">
                AND id IN (
                SELECT store_id FROM blw_store_service_areas WHERE department_id = #{dto.departmentId}
                )
            </if>
            <if test="dto.storeId != null and dto.storeId != ''">
                AND id = #{dto.storeId}
            </if>
            <if test="dto.storeName != null and dto.storeName != ''">
                AND store_name LIKE CONCAT('%', #{dto.storeName}, '%')
            </if>
            <if test="dto.contactPhone != null and dto.contactPhone != ''">
                AND contact_phone LIKE CONCAT('%', #{dto.contactPhone}, '%')
            </if>
            <if test="dto.izALl != null and !dto.izAll">
                <if test="powerDTO.needFilter()">
                    <if test="powerDTO.hasStore()">
                        AND id IN
                        <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="!powerDTO.hasStore()">
                        AND 1=2
                    </if>
                </if>
            </if>
        </where>
        ORDER BY id DESC
    </sql>

    <select id="list" resultType="com.hishop.blw.repository.entity.Store">
        <include refid="query"/>
    </select>

    <select id="pageList" resultType="com.hishop.blw.repository.entity.Store">
        <include refid="query"/>
    </select>
    <select id="pageByRelationUser" resultType="com.hishop.blw.repository.entity.Store">
        select bs.*
        from blw_store bs
                 join blw_user_stores bus on bs.id=bus.store_id and bs.status=1
                 join blw_users bu on bu.id=bus.user_id and bu.user_type=1
        where bu.id=#{userId} and bu.manager=0
        order by bs.store_name asc

    </select>

    <select id="pageByPlat" resultType="com.hishop.blw.repository.entity.Store">
        select *
        FROM blw_store
        where `status` = 1
        order by store_name asc
    </select>
    
    <select id="listByUserIds" resultType="com.hishop.blw.model.vo.store.StoreUserVO">
        select bs.*,bus.user_id
        from blw_store bs
            inner join blw_user_stores bus on bs.id=bus.store_id
            inner join blw_users bu on bu.id=bus.user_id
        where bu.id IN
        <foreach collection="userIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND bs.`status` = 1
        order by bs.id asc
    </select>


</mapper>