<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.ProductsMapper">


    <select id="listByCourseIds" resultType="com.hishop.blw.repository.entity.Products">
        SELECT
            *
        FROM
            blw_products
        WHERE
            id IN (
                SELECT
                    relation_product_id
                FROM blw_combine_products
                WHERE product_id = #{productId} AND relation_type = 2
            )
    </select>


    <select id="listProjectByFree" resultType="com.hishop.blw.repository.entity.Products">
        SELECT
            *
        FROM
            blw_products
        WHERE
            product_type = 2
            <if test="storeId != null">
                AND store_id = #{storeId}
            </if>
            <if test="departmentId != null">
                AND department_id = #{departmentId}
            </if>
            AND relate_employee_id IN
            <foreach collection="employeeIds" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
            AND (
                (week_day IS NULL OR week_day = '')
                    OR
                (week_day = #{weekDay} OR week_day LIKE CONCAT(#{weekDay}, ',%') OR week_day LIKE CONCAT('%,', #{weekDay}, ',%') OR week_day LIKE CONCAT('%,', #{weekDay}))
            )

            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
                <if test="powerDTO.hasStore()">
                    AND store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>




    </select>

    <select id="listSubsetProject" resultType="com.hishop.blw.repository.dto.ProjectTileDTO">
        SELECT
            p.*,
            cp.quantity
        FROM
            blw_products p
            LEFT JOIN blw_combine_products cp ON p.id = cp.relation_product_id
        WHERE
            cp.product_id = #{parentId}
            AND cp.relation_type = 2
    </select>
    
    <select id="getByOrderItemId" resultType="com.hishop.blw.repository.entity.Products">
        SELECT
            p.*
        FROM
            blw_order_items oi
                LEFT JOIN blw_products p oN oi.product_id = p.id
        WHERE
            oi.id = #{orderItemId}
    </select>

    <update id="addStock">
        UPDATE
            blw_products
        SET
            stock = stock + #{addStock}
        WHERE
            id = #{id} and stock is not null
    </update>

    <update id="deductStock">
        UPDATE
            blw_products
        SET
            stock = stock - #{stock}
        WHERE
            id = #{id} and  stock is not null and stock - #{stock} >= 0
    </update>
    <select id="listByStoreAndType" resultType="com.hishop.blw.repository.entity.Products">
        SELECT
            t.*
        FROM
            blw_products t left join blw_department t1 on t.department_id = t1.id
        WHERE
            t1.id is not null
            and t.store_id = #{storeId}
            and t.department_id > 0 and t.iz_delete = false
            AND t.product_type IN
            <foreach collection="types" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="productName != null">
                AND t.product_name = #{productName}
            </if>
    </select>
</mapper>