<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.UserServicesMapper">

    <resultMap id="OrderDetailResultMap" type="com.hishop.blw.repository.entity.UserServices">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="storeId" column="store_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="orderDate" column="order_date"/>
        <result property="validTo" column="valid_to"/>
        <result property="availableQuantity" column="available_quantity"/>
        <result property="usedQuantity" column="used_quantity"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <update id="deductionQuantity">
        UPDATE
            blw_user_services
        SET available_quantity = available_quantity - #{quantity},
            used_quantity = used_quantity + #{quantity}
        WHERE id = #{id}
          AND available_quantity >= #{quantity}
    </update>


    <update id="addQuantity">
        UPDATE
            blw_user_services
        SET available_quantity = available_quantity + #{quantity},
            used_quantity = used_quantity - #{quantity}
        WHERE id = #{id}
          AND used_quantity > 0
    </update>

      <update id="subByQuantity">
        UPDATE
            blw_user_services
        SET available_quantity = available_quantity - #{quantity},
            used_quantity = used_quantity + #{quantity}
        WHERE id = #{id}
          AND available_quantity >= #{quantity}
    </update>

    <update id="addRefundQuantity">
        UPDATE
            blw_user_services
        SET refund_quantity = refund_quantity + #{refundQuantity},
            available_quantity = available_quantity - #{refundQuantity}
        WHERE
            available_quantity >= #{refundQuantity}
            AND order_id = #{orderId}
            AND order_item_id = #{orderItemId}
            AND product_id = #{productId}

    </update>

    <update id="subRefundQuantity">
        UPDATE
            blw_user_services
        SET refund_quantity = refund_quantity - #{refundQuantity},
            available_quantity = available_quantity + #{refundQuantity}
        WHERE
            refund_quantity >= #{refundQuantity}
            AND order_id = #{orderId}
            AND order_item_id = #{orderItemId}
            AND product_id = #{productId}
    </update>

    <select id="pageList" resultType="com.hishop.blw.model.vo.order.UserServicesVO">
        select bus.*,boi.product_name sourceName,bo.order_no,bp.category_id
        from blw_user_services bus
        left join blw_orders bo on bo.id = bus.order_id
        left join blw_order_items boi on boi.id = bus.order_item_id
        left join blw_products bp on bp.id = bus.product_id
        where bus.available_quantity > 0
            <if test="queryDTO.productName != null and queryDTO.productName != ''">
                and bus.product_name like CONCAT('%', #{queryDTO.productName}, '%')
            </if>
            <if test="queryDTO.userId != null">
                and bus.user_id = #{queryDTO.userId}
            </if>
            <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
                and bo.order_no = #{queryDTO.orderNo}
            </if>
            <if test="queryDTO.storeId != null">
                and bus.store_id = #{queryDTO.storeId}
            </if>
            <if test="queryDTO.departmentId != null">
                and bus.department_id=#{queryDTO.departmentId}
            </if>
            <if test="queryDTO.validToStart != null">
                and bus.valid_to BETWEEN #{queryDTO.validToStart} AND #{queryDTO.validToEnd}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (bo.sales_user_id = #{powerDTO.loginUserId} or bo.proxy_user_id = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND bo.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>              
                <if test="powerDTO.hasDepartment()">
                    AND bo.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    AND 1=2
                </if>                
            </if>
            ORDER BY bus.create_date DESC
    </select>
    <update id="updateBatchUserServices">
        UPDATE blw_user_services
        SET valid_to = DATE_ADD(valid_to ,INTERVAL #{hours} HOUR)
        WHERE product_id = #{productId}
    </update>

    <update id="updateUserServices">
        UPDATE blw_user_services
        SET used_service_amount = used_service_amount + #{usedServiceAmount},
            available_quantity = available_quantity - #{quantity},
            used_quantity = used_quantity + #{quantity}
        WHERE order_item_id = #{orderItemId}
    </update>
</mapper>