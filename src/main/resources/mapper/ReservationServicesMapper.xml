<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.ReservationServicesMapper">


    <select id="getBoard" resultType="com.hishop.blw.repository.entity.ReservationServices">
        SELECT
            rs.*
        FROM
            blw_reservation_services rs
                LEFT JOIN blw_orders o ON rs.order_id = o.id
        WHERE
            rs.`status` in (1,2)
            AND DATE_FORMAT(rs.reservation_date, '%Y-%m-%d') = DATE_FORMAT(#{reservationDate}, '%Y-%m-%d')
            AND rs.employee_id IN
            <foreach collection="employeeIds" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
            <if test="storeId != null">
                AND o.store_id = #{storeId}
            </if>
            <if test="departmentId != null">
                AND o.department_id = #{departmentId}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasStore()">
                    AND o.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasStore()">
                    AND 1=2
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND o.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment()">
                    AND 1=2
                </if>
             
                <if test="!powerDTO.isDepartmentManager()">
                    AND rs.employee_id = #{powerDTO.loginUserId} 
                </if> 
            </if>

    </select>


    <select id="pageList" resultType="com.hishop.blw.repository.dto.ReservationServicesPageResultDTO">
        SELECT
            rs.*,
            o.order_no AS orderNo,
            o.order_date AS orderDate,
            IF(DAYOFWEEK(rs.reservation_date) = 1, 7,  DAYOFWEEK(rs.reservation_date) - 1) AS weekDayStr,
            o.store_id AS storeId,
            o.department_id AS departmentId,
            v.verify_user_id AS verifyUserId,
            v.verifier AS verifier,
            v.verify_time AS verifyTime,
            v.image_url as verifyImageUrl,
            v.remark as verifyRemark,
            cp.iz_gift AS izGift,
            rs.reservation_number AS reservationNumber,
            CONCAT(
                DATE_FORMAT(rs.reservation_date, '%Y-%m-%d'),
                ' ',
                DATE_FORMAT(rs.service_start_time, '%H:%i:%s')
            ) AS reservationStartTime
        FROM
            blw_reservation_services rs
            LEFT JOIN blw_orders o ON rs.order_id = o.id
            LEFT JOIN blw_order_items oi ON rs.order_item_id = oi.id
            LEFT JOIN blw_users u ON rs.user_id = u.id
            left join blw_verify v on rs.id = v.reservation_id and v.amount >= 0
            left join blw_combine_products cp on cp.relation_product_id = rs.product_id and cp.product_id = oi.product_id

        <where>
            <if test="query.reservationDateStart != null">
                AND rs.reservation_date >= #{query.reservationDateStart}
            </if>
            <if test="query.reservationDateEnd != null">
                AND rs.reservation_date &lt;= #{query.reservationDateEnd}
            </if>
            <if test="query.serviceStartTime != null">
                AND rs.service_start_time >= #{query.serviceStartTime}
            </if>
            <if test="query.serviceEndTime != null">
                AND rs.service_end_time &lt;= #{query.serviceEndTime}
            </if>
            <if test="query.status != null">
                AND rs.status = #{query.status}
            </if>
            <if test="query.storeId != null and query.storeId > 0">
                AND o.store_id = #{query.storeId}
            </if>
            <if test="query.departmentId != null">
                AND o.department_id = #{query.departmentId}
            </if>
            <if test="query.salesUserId != null">
                AND o.sales_user_id = #{query.salesUserId}
            </if>
            <if test="query.pcEmployeeId != null">
                AND rs.employee_id = #{query.pcEmployeeId}
            </if>         
            <if test="query.id != null">
                AND rs.id = #{query.id}
            </if>
            <if test="query.no != null and query.no != ''">
                AND rs.reservation_no = #{query.no}
            </if>
            <if test="query.ids != null and query.getIds().size() > 0">
                AND rs.id in
                <foreach collection="query.getIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                AND o.order_no = #{query.orderNo}
            </if>
            <if test="query.nickname != null and query.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND u.phone = #{query.phone}
            </if>
            <if test="query.userId != null">
                AND rs.user_id = #{query.userId}
            </if>
            <if test="query.keyWords != null and query.keyWords != ''">
                AND (
                    rs.product_name like CONCAT('%', #{query.keyWords}, '%')
                    or
                    u.nickname LIKE CONCAT('%', #{query.keyWords}, '%')
                    or
                    u.phone LIKE CONCAT('%', #{query.keyWords}, '%')
                    or o.order_no LIKE CONCAT('%', #{query.keyWords}, '%')
                    or u.name LIKE CONCAT('%', #{query.keyWords}, '%')
                    or rs.reservation_no LIKE CONCAT('%', #{query.keyWords}, '%')
                )
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasStore()">
                    AND o.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                 <if test="powerDTO.hasDepartment()">
                    AND o.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment() or !powerDTO.hasStore()">
                    AND 1=2
                </if>            
                <if test="!powerDTO.isDepartmentManager() and query.createBy != null">
                    AND rs.create_by = #{powerDTO.loginUserId}
                </if> 
                 <if test="!powerDTO.isDepartmentManager() and query.createBy == null">
                    AND rs.employee_id = #{powerDTO.loginUserId}
                </if> 
            </if>
        </where>
        ORDER BY v.create_date DESC, reservationStartTime DESC
    </select>

    <select id="listByCategoryIds" resultType="com.hishop.blw.model.vo.report.ManagerVerifyAmountVO">
        select (case when pc.parent_category_id=0 then pc.id else pc.parent_category_id end) as "oneCategoryId"
        ,DATE_FORMAT(v.create_date,'%Y-%m') as "month"
        ,ROUND(sum((ifnull(v.amount,0)+ifnull(v.point_amount,0))*op.split_pay_amount/us.service_total_amount), 2) as "splitPayAmount"
        from blw_reservation_services rs
        join blw_orders o on rs.order_id=o.id
        join blw_verify v on rs.id=v.reservation_id
        join blw_products p on rs.product_id=p.id
        join blw_product_categories pc on p.category_id=pc.id
        join blw_user_services us on rs.user_service_id=us.id
        join blw_order_products_payment op on rs.order_item_id=op.order_item_id and rs.product_id=op.product_id
        where  rs.`status`=2
        <if test="storeId != null">
            and v.store_id=#{storeId}
        </if>
        <if test="departId != null">
            and v.department_id=#{departId}
        </if>
        and (pc.id in
        <foreach collection="categoryIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        or pc.parent_category_id in
        <foreach collection="categoryIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        )
        and exists(select 1 from
        (select a.id as "pay_method_id",a.* from blw_pay_methods a
        union all
        select c.id as "pay_method_id",m.* from blw_cards c join blw_pay_methods m on c.pay_method_id=m.id
        )b
        where op.pay_method_id=b.pay_method_id
        and b.iz_operating_report=true)
        and YEAR(v.create_date) = #{year}
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and (o.sales_department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
            </if>
        </if>
        group by oneCategoryId,month
    </select>


</mapper>