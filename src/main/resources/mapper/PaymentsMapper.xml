<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.PaymentsMapper">
    <select id="listSumByOrderIdList" resultType="com.hishop.blw.model.vo.order.PaymentsSumVO">
        select sum(t.amount) as amount, t.order_id, t.user_id, t.pay_method_id, t.pay_method from blw_payments t where 1=1
        <if test="orderIds != null and orderIds.size() > 0">
            and t.order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        <if test="startTime != null">
            and t.payment_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and t.payment_time &lt;= #{endTime}
        </if>
        GROUP BY t.order_id, t.user_id, t.pay_method_id
    </select>


    <select id="listOrderPaymentAmount" resultType="com.hishop.blw.repository.dto.OrderPaymentAmountDTO">
        select
            p.order_id, p.refund_id, p.pay_method_id, p.pay_method, sum(p.amount) 'amount', pm.iz_walkin, pm.iz_consume_report
        from blw_payments p
            left join blw_pay_methods pm on if(p.card_number = '' or p.card_number is null, p.pay_method_id, (
                select pmm.id
                from blw_pay_methods pmm
                    left join blw_cards c on pmm.card_type = c.card_type
                where c.id = p.pay_method_id
                )
            ) = pm.id
        where p.refund_id is null
        <if test="orderIds!= null and orderIds.size() > 0">
            and p.order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        group by p.order_id, p.refund_id, p.pay_method_id, p.pay_method, pm.iz_consume_report
    </select>

    <select id="listByOrderIdList" resultType="com.hishop.blw.repository.entity.Payments">
        select p.*
        from blw_payments p
        <if test="payMethodId != null or orderCategory != null">
            join (
            SELECT
            a.id AS "pay_method_id",
            a.*
            FROM
            blw_pay_methods a UNION ALL
            SELECT
            c.id AS "pay_method_id",
            m.*
            FROM
            blw_cards c
            JOIN blw_pay_methods m ON c.pay_method_id = m.id
            ) b ON p.pay_method_id = b.pay_method_id
            AND b.iz_sales_report = true
            <if test="orderCategory != null and orderCategory == 5">
                and b.iz_walkin = 2
            </if>
            <if test="orderCategory != null and (orderCategory == 1 or orderCategory == 2)">
                and b.iz_walkin = 0
            </if>
            <if test="orderCategory != null and (orderCategory == 3 or orderCategory == 4)">
                and b.iz_walkin = 1
            </if>
            <if test="payMethodId != null">
                and b.pay_method_id = #{payMethodId}
            </if>
        </if>
        where p.order_id in
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        <if test="startTime != null">
            and p.payment_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>

</mapper>