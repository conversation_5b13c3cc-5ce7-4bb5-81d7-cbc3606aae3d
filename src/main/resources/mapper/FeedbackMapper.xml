<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.FeedbackMapper">

    <select id="pageList" resultType="com.hishop.blw.model.vo.common.FeedbackVO">
        select fb.id,
        fb.create_date createDate,
        fb.create_by createBy,
        fb.content,
        fb.image,
        fb.store_id storeId,
        fb.user_id as 'userId',
        o.order_no orderNo
        from blw_feedback fb
        left join blw_orders o on fb.order_id=o.id
        <where>
            <if test="queryDTO.userId != null">
                and fb.user_id=#{queryDTO.userId}
            </if>
            <if test="queryDTO.storeId != null">
                and fb.store_id = #{queryDTO.storeId}
            </if>
            <if test="queryDTO.orderNo != null">
                and o.order_no LIKE CONCAT('%',#{queryDTO.orderNo},'%')
            </if>
            <if test="queryDTO.createDateBegin != null">
                and fb.create_date BETWEEN #{queryDTO.createDateBegin} AND #{queryDTO.createDateEnd}
            </if>
        </where>
        order by fb.create_date desc
    </select>
</mapper>