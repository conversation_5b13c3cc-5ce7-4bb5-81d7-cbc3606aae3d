<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.BlwReportTargetCompleteInfoMapper">
    <insert id="generateReport">
INSERT INTO blw_report_target_complete_info ( store_id, department_id, sales_amount_target_month, sales_amount_target_year, real_sales_amount, recharge_amount_target_month, recharge_amount_target_year, real_recharge_amount, consume_amount_target_month, consume_amount_target_year, real_consume_amount, belong_date ) (
	SELECT
		bs.id store_id,
		ssa.department_id,
		IFNULL( mbtt.sales_amount, 0 ) sales_amount_target_month,
		IFNULL( ybtt.sales_amount, 0 ) sales_amount_target_year,
		IFNULL( bo.real_sales_amount, 0 ) real_sales_amount,
		IFNULL( mbtt.recharge_amount, 0 ) recharge_amount_target_month,
		IFNULL( ybtt.recharge_amount, 0 ) recharge_amount_target_year,
		ifnull( bcr.real_recharge_amount, 0 ) real_recharge_amount,
		IFNULL( mbtt.consume_amount, 0 ) consume_amount_target_month,
		IFNULL( ybtt.consume_amount, 0 ) consume_amount_target_year,
		IFNULL( bv.real_consume_amount, 0 ) real_consume_amount,
		#{yearMonth} belong_date 
	FROM
		blw_store bs
		JOIN blw_store_service_areas ssa ON bs.id = ssa.store_id
		LEFT JOIN blw_task_target mbtt ON bs.id = mbtt.store_id 
		AND ssa.department_id = mbtt.department_id 
		AND mbtt.target_type = 2 
		AND mbtt.target_scope = #{yearMonth}
		LEFT JOIN blw_task_target ybtt ON bs.id = ybtt.store_id 
		AND ssa.department_id = ybtt.department_id 
		AND ybtt.target_type = 1 
		AND ybtt.target_scope =  #{year}
		LEFT JOIN (
		SELECT
			store_id,
			department_id,
			sum( actual_pay_amount - refund_total_amount ) real_sales_amount 
		FROM
			blw_orders 
		WHERE
			order_status NOT IN ( 1, 3, 5 ) 
			AND DATE_FORMAT( order_date, "%Y-%m" ) = #{yearMonth} 
		GROUP BY
			store_id,
			department_id 
		) bo ON bs.id = bo.store_id 
		AND ssa.department_id = bo.department_id
		LEFT JOIN (
		SELECT
			store_id,
			department_id,
			sum( amount ) real_recharge_amount 
		FROM
			blw_card_records 
		WHERE
			type IN ( 1, 2, 9 ) 
			AND DATE_FORMAT( create_date, "%Y-%m" ) = #{yearMonth} 
		GROUP BY
			store_id,
			department_id 
		) bcr ON bs.id = bcr.store_id 
		AND ssa.department_id = bcr.department_id
		LEFT JOIN ( select store_id,department_id,sum(real_consume_amount) real_consume_amount from (
		SELECT
			v.store_id,
			v.department_id,
			sum( IFNULL(v.amount,0)+IFNULL(v.point_amount,0) ) real_consume_amount 
		FROM
			blw_verify v
			LEFT JOIN blw_reservation_services r ON v.reservation_id = r.id 
		WHERE
			r.STATUS = 2 
			AND DATE_FORMAT( v.verify_time, "%Y-%m" ) = #{yearMonth} UNION
		SELECT
			o.store_id,
			o.department_id,
			sum( IFNULL(oi.actual_amount,0)+IFNULL(oi.actual_points_amount,0)-IFNULL(oi.refund_amount,0) ) real_consume_amount 
		FROM
			blw_order_items oi
			JOIN blw_orders o ON oi.order_id = o.id 
		WHERE
			product_type IN ( 1, 3 ) 
			AND o.order_status IN ( 2, 4,6,7 )
			AND DATE_FORMAT( o.pay_date, "%Y-%m" ) = #{yearMonth}
		GROUP BY
			o.store_id,
			o.department_id UNION
		SELECT
			o.store_id,
			o.department_id,
			sum( IFNULL(oie.amount,0)+IFNULL(oie.point_amount,0)-IFNULL(oie.refund_amount,0) ) real_consume_amount 
		FROM
			blw_order_items_extend oie
			JOIN blw_orders o ON oie.order_id = o.id 
		WHERE
			product_type IN ( 1, 3 ) 
			AND o.order_status IN ( 2, 4, 6,7 )
			AND DATE_FORMAT( o.pay_date, "%Y-%m" ) = #{yearMonth}
		GROUP BY
			o.store_id,
			o.department_id) vv group by store_id,department_id
		) bv ON bs.id = bv.store_id 
		AND ssa.department_id = bv.department_id 
	) UNION
	(
	SELECT
		bs.id store_id,
		0 department_id,
		0 sales_amount_target_month,
		0 sales_amount_target_year,
		0 real_sales_amount,
		0 recharge_amount_target_month,
		0 recharge_amount_target_year,
		ifnull( bcr.real_recharge_amount, 0 ) real_recharge_amount,
		0 consume_amount_target_month,
		0 consume_amount_target_year,
		0 real_consume_amount,
		#{yearMonth} belong_date 
	FROM
		blw_store bs
		JOIN (
		SELECT
			store_id,
			0,
			sum( amount ) real_recharge_amount 
		FROM
			blw_card_records 
		WHERE
			type IN ( 1, 2, 9 )  
			AND DATE_FORMAT( create_date, "%Y-%m" ) = #{yearMonth} 
			AND department_id = 0 
		GROUP BY
			store_id 
		) bcr ON bs.id = bcr.store_id 
	)
    </insert>


    <delete id="deleteByBelongDate">
        delete
        from blw_report_target_complete_info
        where belong_date = #{belongDate}
    </delete>

    <select id="pageListWithYear" resultType="com.hishop.blw.repository.dto.TargetCompleteDTO">
        select h.*, bs.store_name storeName
        from (
        select a.store_id storeId,
        <if test="departmentIds!=null and departmentIds.size()>0">
			a.department_id departmentId,
        </if>
        a.belongYear,
		a.sales_amount_target_year as salesAmountTargetYear,
        SUM(a.real_sales_amount) realSalesAmount,
		a.recharge_amount_target_year as rechargeAmountTargetYear,
        SUM(a.real_recharge_amount) realRechargeAmount,
		a.consume_amount_target_year as consumeAmountTargetYear,
        SUM(a.real_consume_amount) realConsumeAmount
        from (select store_id,department_id,LEFT(belong_date, 4) as "belongYear",sales_amount_target_year,recharge_amount_target_year,consume_amount_target_year,
                  SUM( real_sales_amount ) as "real_sales_amount",SUM( real_recharge_amount ) as "real_recharge_amount",SUM( real_consume_amount ) as "real_consume_amount"
		from blw_report_target_complete_info
		group by store_id,department_id,belongYear) a
		<if test="departmentType!=null">
			left join blw_department bd on a.department_id = bd.id
		</if>
        where 1=1
        <if test="year!=null">
            and a.belong_date like concat(#{year}, '%')
        </if>
        <if test="storeIds!=null and storeIds.size()>0">
            and a.store_id in
            <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="departmentType!=null">
            and bd.type = #{departmentType}
        </if>
        <if test="departmentIds!=null and departmentIds.size()>0">
            and a.department_id in
            <foreach collection="departmentIds" item="departmentId" separator="," open="(" close=")">
                #{departmentId}
            </foreach>
        </if>

		<choose>
         <when test="departmentIds!=null and departmentIds.size()>0">
              GROUP BY a.store_id,a.department_id,a.belongYear
        </when>
        <otherwise>
            GROUP BY a.store_id,a.belongYear
        </otherwise>
		</choose>
        ) h
        join blw_store bs on h.storeId = bs.id
		<where>
		 <if test="userPower.needFilter()">
                <if test="userPower.hasStore()">
                    bs.id IN
                    <foreach collection="userPower.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!userPower.hasStore()">
                    1=2
                </if>
            </if>
		</where>
        order by h.belongYear desc, store_name asc
    </select>

    <select id="pageListWithMonth" resultType="com.hishop.blw.repository.dto.TargetCompleteDTO">
        SELECT
        h.*,
        bs.store_name storeName
        FROM
        (
        SELECT
        store_id storeId,
        <if test="departmentIds!=null and departmentIds.size()>0">
            department_id departmentId,
        </if>
        belong_date belongDate,
        SUM(sales_amount_target_month) salesAmountTargetMonth,
        SUM(real_sales_amount) realSalesAmount,
        SUM(recharge_amount_target_month) rechargeAmountTargetMonth,
        SUM(real_recharge_amount) realRechargeAmount,
        SUM(consume_amount_target_month) consumeAmountTargetMonth,
        SUM(real_consume_amount) realConsumeAmount
        FROM
        blw_report_target_complete_info
		<if test="departmentType!=null">
			left join blw_department bd on department_id = bd.id
		</if>
        WHERE 1=1
        <if test="yearMonth!=null">
            and belong_date =#{yearMonth}
        </if>
        <if test="storeIds!=null and storeIds.size()>0">
            and store_id in
            <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
         <if test="departmentIds!=null and departmentIds.size()>0">
            and department_id in
            <foreach collection="departmentIds" item="departmentId" separator="," open="(" close=")">
                #{departmentId}
            </foreach>
        </if>
		 <if test="departmentType!=null">
            and bd.type = #{departmentType}
        </if>
		group by storeId,belongDate 
        ) h
        JOIN blw_store bs ON h.storeId = bs.id
		<where>
		 <if test="userPower.needFilter()">
                <if test="userPower.hasStore()">
                    bs.id IN
                    <foreach collection="userPower.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!userPower.hasStore()">
                    1=2
                </if>
            </if>
		</where>
        order by h.belongDate desc, store_name asc
    </select>

	<update id="updateByYear">
		update blw_report_target_complete_info
		set sales_amount_target_year = #{salesAmount},
		recharge_amount_target_year = #{rechargeAmount},
		consume_amount_target_year = #{consumeAmount}
		where store_id = #{storeId}
		and department_id = #{departmentId}
		and LEFT( belong_date, 4) = #{targetScope}
	</update>
</mapper>