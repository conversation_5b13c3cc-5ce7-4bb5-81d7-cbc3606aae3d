<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.RefundMapper">

    <select id="getRefundDetailById" resultType="com.hishop.blw.repository.dto.RefundResultDTO">
        SELECT r.id                       AS id,
               r.refund_no                AS refundNo,
               o.order_no                 AS orderNo,
               u.nickname                 AS userNickname,
               u.phone                    AS userPhone,
               c.nickname                 AS creatorNickname,
               oi.product_name            AS productName,
               rr.refund_reason           AS refundReason,
               r.additional_evidence_urls AS additionalEvidenceUrls,
               pm.pay_method              AS payMethod,
               o.store_id                 AS storeId,
               o.department_id            AS departmentId,
               r.apply_amount             AS applyAmount,
               r.`status`                 AS `status`,
               r.handler_time             AS handlerTime,
               r.refund_amount            AS refundAmount,
               r.refund_pay_method        AS refundPayMethod,
               r.remarks                  AS remarks,
               cpm.pay_method             AS commissionPayMethod,
               rpm.pay_method             AS refundPayMethod
        FROM blw_refund r
                 LEFT JOIN blw_users u ON r.user_id = u.id
                 LEFT JOIN blw_users c ON r.create_by = c.id
                 LEFT JOIN blw_orders o ON r.order_id = o.id
                 LEFT JOIN blw_payments p ON o.id = p.order_id
                 LEFT JOIN blw_pay_methods pm ON p.pay_method = pm.id
                 LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
                 LEFT JOIN blw_order_items oi ON ri.order_item_id = oi.id
                 LEFT JOIN blw_refund_reasons rr ON r.refund_reason = rr.id
                 LEFT JOIN blw_pay_methods cpm ON r.commission_pay_method = cpm.id
                 LEFT JOIN blw_pay_methods rpm ON r.refund_pay_method = rpm.id
        WHERE r.id = #{id}
    </select>

    <select id="pageList" resultType="com.hishop.blw.repository.dto.RefundResultDTO">
        SELECT
            r.id AS id,
            r.refund_no AS refundNo,
            o.id AS orderId,
            o.order_no AS orderNo,
            u.id as userId,
            u.nickname AS userNickname,
            u.name AS userName,
            u.phone AS userPhone,
            c.nickname AS creatorNickname,
            c.name AS creatorName,
            oi.product_name AS productName,
            oi.id as orderItemId,
            bp.product_main_image AS productMainImage,
            ri.id as refundItemId,
            ri.refund_quantity as refundQuantity,
            ri.refundable_points as refundablePoints,
            ri.product_type as productType,
            rr.refund_reason AS refundReason,
            r.additional_evidence_urls AS additionalEvidenceUrls,
            (
                SELECT GROUP_CONCAT(pm.pay_method)
                    FROM blw_payments p
                    LEFT JOIN blw_pay_methods pm ON p.pay_method_id = pm.id
                WHERE p.order_id = o.id AND p.refund_id IS NULL
            ) AS payMethod,
            o.store_id AS storeId,
            o.department_id AS departmentId,
            o.points AS points,
            oi.actual_points_amount AS pointsAmount,
            r.apply_amount AS applyAmount,
            r.`status` AS `status`,
            h.nickname AS handlerNickname,
            r.commission AS commission,
            cpm.pay_method AS commissionPayMethod,
            r.refund_amount AS refundAmount,
            rpm.pay_method AS refundPayMethod,
            r.refund_points AS refundPoints,
            r.additional_description AS remarks,
            r.handler_remark AS handlerRemark,
            r.apply_time AS applyTime,
            r.handler_time AS handlerTime
        FROM blw_refund r
            LEFT JOIN blw_users u ON r.user_id = u.id
            LEFT JOIN blw_users h ON r.handler_id = h.id
            LEFT JOIN blw_users c ON r.create_by = c.id
            LEFT JOIN blw_orders o ON r.order_id = o.id
            LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
            LEFT JOIN blw_order_items oi ON ri.order_item_id = oi.id
            LEFT JOIN blw_refund_reasons rr ON r.refund_reason = rr.id
            LEFT JOIN blw_pay_methods cpm ON r.commission_pay_method = cpm.id
            LEFT JOIN blw_pay_methods rpm ON r.refund_pay_method = rpm.id
            left join blw_products bp on bp.id = oi.product_id
        <where>
            <if test="query.id != null">
                and r.id = #{query.id}
            </if>
            <if test="query.applyTimeStart != null">
                AND r.apply_time &gt;= #{query.applyTimeStart}
            </if>
            <if test="query.applyTimeEnd != null">
                AND r.apply_time &lt;= #{query.applyTimeEnd}
            </if>
            <if test="query.status != null">
                AND r.`status` = #{query.status}
            </if>
            <if test="query.storeId != null">
                AND o.store_id = #{query.storeId}
            </if>
            <if test="query.departmentId != null">
                AND o.department_id = #{query.departmentId}
            </if>
            <if test="query.type != null">
                AND r.type = #{query.type}
            </if>
            <if test="query.salesUserId != null">
                AND o.sales_user_id = #{query.salesUserId}
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                AND o.order_no = #{query.orderNo}
            </if>
            <if test="query.refundNo != null and query.refundNo != ''">
                AND r.refund_no = #{query.refundNo}
            </if>
            <if test="query.nickname != null and query.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND u.phone = #{query.phone}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND oi.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.userId != null">
                AND r.user_id = #{query.userId}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="!powerDTO.isDepartmentManager()">
                    AND (o.sales_user_id = #{powerDTO.loginUserId} or o.proxy_user_id = #{powerDTO.loginUserId})
                </if>   
                <if test="powerDTO.hasStore()">
                    AND o.store_id IN
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND o.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>
        </where>
        order by r.create_date desc
    </select>


    <select id="countExistsRefundByOrderItemId" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            blw_refund r
                LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
        WHERE
            ri.order_item_id = #{orderItemId}
          AND r.STATUS IN ( 1, 3 )
    </select>

    <select id="getOrderItemRefundAmount" resultType="com.hishop.blw.repository.dto.ItemRefundAmountDTO">
        SELECT order_item_id,sum(ifnull(ri.refund_amount,0)+ifnull(ri.refund_points_amount,0)) as amount
        FROM
        <if test="(productName == null or productName == '') and projectDepartmentId == null">
            blw_refund_items ri
        </if>
        <if test="(productName != null and productName != '') or projectDepartmentId != null">
            (select i.refund_id,i.order_item_id,i.product_name,i.project_department_id,i.refund_amount,i.refund_points_amount from blw_refund_items i
            union all
            select o.refund_id,o.order_item_id,o.product_name,o.project_department_id,o.refund_amount,o.refund_points_amount from blw_refund_items_extend o
            )ri
        </if>
        LEFT JOIN blw_refund r on ri.refund_id = r.id
        WHERE ri.order_item_id in
        <foreach collection="orderItemIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach> and r.`status` = 3
        <if test="startTime != null">
            AND r.handler_time between #{startTime} and #{endTime}
        </if>
        <if test="productName != null and productName != ''">
            and ri.product_name like concat('%',#{productName},'%')
        </if>
        <if test="projectDepartmentId != null">
            and ri.project_department_id = #{projectDepartmentId}
        </if>
        group by ri.order_item_id
    </select>

    <select id="getOrderItemRefundCount" resultType="com.hishop.blw.repository.dto.ItemRefundCountDTO">
        SELECT order_item_id,ifnull(sum(ri.refund_quantity),0) as "num"
        FROM blw_refund_items ri LEFT JOIN blw_refund r on ri.refund_id = r.id
        WHERE ri.order_item_id in
        <foreach collection="orderItemIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
            and r.`status` = 3
        group by ri.order_item_id
    </select>
    <select id="listOrderItemRefundCount" resultType="com.hishop.blw.repository.dto.RefundQuantityDTO">
        SELECT order_item_id,ifnull(sum(ri.refund_quantity),0) as "refundQuantity"
        FROM blw_refund_items ri LEFT JOIN blw_refund r on ri.refund_id = r.id
        WHERE r.`status` = 3
            and ri.order_item_id in
        <foreach collection="orderItemIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by ri.order_item_id
    </select>

    <select id="listByOrderItemId" resultType="com.hishop.blw.repository.dto.RefundDTO">
        SELECT
            it.*,
            r.`status`
        FROM blw_refund_items it
                 LEFT JOIN blw_refund r on r.id =it.refund_id
        WHERE order_item_id = #{orderItemId} and r.`status` in (1,3)
    </select>

    <select id="getOrderItemRefundAmount2" resultType="com.hishop.blw.model.vo.refund.OrderItemRefundVO">
        SELECT ifnull(sum(ri.refund_amount),0) as refundAmount,
               ifnull(sum(ri.refund_points_amount),0) as refundPointsAmount,
               ifnull(sum(ri.refund_quantity),0) as refundQuantity
        FROM blw_refund_items ri
        LEFT JOIN blw_refund r on ri.refund_id = r.id
        WHERE ri.order_item_id = #{orderItemId} and r.`status` = 3
        <if test="startTime != null">
            AND r.handler_time between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="listByProductIds" resultType="com.hishop.blw.repository.dto.RefundQuantityDTO">
        SELECT ri.product_id,sum(ifnull(ri.refund_quantity,0)) as "refundQuantity"
        FROM blw_refund r join (select refund_id,product_id,refund_quantity from blw_refund_items
                  union all
              select refund_id,product_id,refund_quantity from blw_refund_items_extend) ri
              on ri.refund_id = r.id
        WHERE r.`status` = 3 and DATE_FORMAT(r.handler_time,'%Y-%m')= #{month}
        and ri.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        group by ri.product_id
    </select>
    <select id="listOrderRefundQuantity" resultType="com.hishop.blw.repository.dto.RefundQuantityDTO">
        SELECT r.order_no,ri.product_id,sum(ifnull(ri.refund_quantity,0)) as "refundQuantity"
        FROM blw_refund r join (select refund_id,product_id,refund_quantity from blw_refund_items
        union all
        select refund_id,product_id,refund_quantity from blw_refund_items_extend) ri
        on ri.refund_id = r.id
        WHERE r.`status` = 3 and DATE_FORMAT(r.handler_time,'%Y-%m')= #{month}
        and ri.product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        group by r.order_no,ri.product_id
    </select>

    <select id="getOrderItemRefundDate" resultType="com.hishop.blw.repository.dto.RefundItemsRefundDateDTO">
        SELECT
            ri.order_item_id,
            ri.order_id,
            r.handler_time as handlerDate
        FROM
            blw_refund r
                LEFT JOIN blw_refund_items ri ON r.id = ri.refund_id
        WHERE
            r.order_id in
            <foreach collection="orderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND r.STATUS=3
            AND r.handler_time between #{startTime} and #{endTime}
    </select>

</mapper>