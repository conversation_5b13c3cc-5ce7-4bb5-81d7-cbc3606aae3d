<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.PayMethodsMapper">

    <select id="listAllByPayMethodIds" resultType="com.hishop.blw.model.vo.order.PayMethodsVO">
        select a.id as "pay_method_id",a.pay_method as "pay_method_name", a.*
        from blw_pay_methods a
        where a.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        select c.id as "pay_method_id",c.card_name as "pay_method_name",m.*
        from blw_cards c join blw_pay_methods m on c.card_type=m.card_type
        where c.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="statisticsOrderAmount" resultType="com.hishop.blw.repository.dto.OrderAmountStatisticsDTO">
        select
            p.order_id,
            sum(if(pm.iz_walkin = 0, p.amount, 0)) 'member',
            sum(if(pm.iz_walkin = 1, p.amount, 0)) 'individual',
            sum(if(pm.iz_walkin = 2, p.amount, 0)) 'other',
            sum(p.amount) 'total'
        from blw_payments p
            left join blw_pay_methods pm on if(p.card_number is null or p.card_number = '', p.pay_method_id, (
                select pmm.id
                from blw_pay_methods pmm
                left join blw_cards c on pmm.card_type = c.card_type
                where c.id = p.pay_method_id
                )
            ) = pm.id
        where p.order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by p.order_id
    </select>


</mapper>