<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.UsersMapper">

    <select id="pageList" resultType="com.hishop.blw.repository.dto.UsersResultDTO">
        select u.*,ul.level_name,mc.available_amount,mc.total_amount,up.available_points
        <if test="queryDTO.userType!= null and queryDTO.userType == 1">
            ,ur.role_id,r.name as 'role_name'
        </if>
        from blw_users u left join (select sum(available_points) as "available_points",user_id from blw_user_points
        group by user_id) up on u.id=up.user_id
        left join blw_user_levels ul on u.member_level_id = ul.id
        left join blw_user_stores us on u.id = us.user_id left join blw_store s on us.store_id = s.id
        <if test="(queryDTO.userType!= null and queryDTO.userType == 1) or queryDTO.roleId!=null">
            left join blw_user_role ur on u.id=ur.user_id
            left join blw_role r on ur.role_id=r.id
        </if>
        left join (select user_id,sum(total_amount) as 'total_amount',sum(available_amount) as 'available_amount' from
        blw_member_cards group by user_id) mc on u.id=mc.user_id
        <if test="queryDTO.orderAmountStart!= null or queryDTO.orderAmountEnd != null">
            join (select o.user_id,sum(o.order_total_amount) as "order_total_amount" from blw_orders o where o.`order_status`
            in(2,4) group by o.user_id) tp_o
            on u.id=tp_o.user_id
        </if>
        <where>
            1= 1
            <if test="queryDTO.isContainDeleted != null and queryDTO.isContainDeleted == false">
                and u.status != 5
            </if>
            <if test="queryDTO.id != null">
                and u.id = #{queryDTO.id}
            </if>
            <if test="queryDTO.provinceId != null">
                and s.province_id = #{queryDTO.provinceId}
            </if>
            <if test="queryDTO.cityId != null">
                and s.city_id = #{queryDTO.cityId}
            </if>
            <if test="queryDTO.districtId != null">
                and s.district_id = #{queryDTO.districtId}
            </if>
            <if test="queryDTO.memberLevelId!= null">
                and u.member_level_id = #{queryDTO.memberLevelId}
            </if>
            <if test="queryDTO.roleLevel!= null and queryDTO.roleLevel == 1">
                and u.manager = 1
            </if>
            <if test="queryDTO.roleLevel!= null and queryDTO.roleLevel == 2">
                and u.manager = 0
            </if>
            <if test="queryDTO.storeId!= null">
                and us.store_id = #{queryDTO.storeId}
            </if>
            <if test="queryDTO.departmentId!= null">
                and us.department_id = #{queryDTO.departmentId}
            </if>
            <if test="queryDTO.userType!= null">
                and u.user_type = #{queryDTO.userType}
            </if>
            <if test="queryDTO.roleId!= null">
                and ur.role_id = #{queryDTO.roleId}
            </if>
            <if test="queryDTO.phone!= null and queryDTO.phone!= ''">
                and u.phone like concat('%',#{queryDTO.phone},'%')
            </if>
            <if test="queryDTO.nickname!= null and queryDTO.nickname!= ''">
                and u.nickname like concat('%',#{queryDTO.nickname},'%')
            </if>
            <if test="queryDTO.userCode!= null and queryDTO.userCode!= ''">
                and u.user_code = #{queryDTO.userCode}
            </if>
            <if test="queryDTO.name!= null and queryDTO.name!= ''">
                and u.name like concat('%',#{queryDTO.name},'%')
            </if>
            <if test="queryDTO.createTimeStart!= null">
                and u.create_date BETWEEN #{queryDTO.createTimeStart} AND #{queryDTO.createTimeEnd}
            </if>
            <if test="queryDTO.lastOrderTimeStart != null ">
                and u.last_order_time >= DATE_FORMAT(#{queryDTO.lastOrderTimeStart}, '%Y-%m-%d %H:%i:%s')
            </if>

            <if test="queryDTO.lastOrderTimeEnd != null ">
                and u.last_order_time &lt;= DATE_FORMAT(#{queryDTO.lastOrderTimeEnd}, '%Y-%m-%d %H:%i:%s')
            </if>

            <if test="queryDTO.status!= null">
                and u.status = #{queryDTO.status}
            </if>
            <if test="queryDTO.balanceStart!= null and queryDTO.balanceEnd != null">
                and mc.available_amount BETWEEN #{queryDTO.balanceStart} AND #{queryDTO.balanceEnd}
            </if>
            <if test="queryDTO.balanceStart!= null and queryDTO.balanceEnd == null">
                and mc.available_amount >= #{queryDTO.balanceStart}
            </if>
            <if test="queryDTO.balanceStart== null and queryDTO.balanceEnd != null">
                and mc.available_amount &lt;= #{queryDTO.balanceEnd}
            </if>
            <if test="queryDTO.integralStart!= null and queryDTO.integralEnd != null">
                and up.available_points BETWEEN #{queryDTO.integralStart} AND #{queryDTO.integralEnd}
            </if>
            <if test="queryDTO.integralStart!= null and queryDTO.integralEnd == null">
                and up.available_points >= #{queryDTO.integralStart}
            </if>
            <if test="queryDTO.integralStart== null and queryDTO.integralEnd != null">
                and up.available_points &lt;= #{queryDTO.integralEnd}
            </if>
            <if test="queryDTO.orderAmountStart != null">
                and tp_o.order_total_amount >= #{queryDTO.orderAmountStart}
            </if>
            <if test="queryDTO.orderAmountEnd != null">
                and tp_o.order_total_amount &lt;= #{queryDTO.orderAmountEnd}
            </if>
            <if test="queryDTO.consumeAmountStart!= null and queryDTO.consumeAmountEnd != null">
                and exists (select 1 from
                (select a.user_id,sum(a.consume_amount) as "consumeTotalAmount" from
                (select pp.user_id,pp.split_pay_amount as "consume_amount"
                from blw_order_products_payment pp where pp.product_type in(1,3)
                union all
                select us.user_id,us.used_service_amount as "consume_amount"
                from blw_user_services us where us.used_service_amount>0) a
                group by a.user_id) b where b.user_id=u.id and b.consumeTotalAmount between #{queryDTO.consumeAmountStart} and #{queryDTO.consumeAmountEnd})
            </if>
            <if test="queryDTO.consumeAmountStart!= null and queryDTO.consumeAmountEnd == null">
                and exists (select 1 from
                (select a.user_id,sum(a.consume_amount) as "consumeTotalAmount" from
                (select pp.user_id,pp.split_pay_amount as "consume_amount"
                from blw_order_products_payment pp where pp.user_id=u.id
                union all
                select us.user_id,us.used_service_amount as "consume_amount"
                from blw_user_services us where us.user_id=u.id and us.used_service_amount>0) a
                group by a.user_id) b where b.consumeTotalAmount >= #{queryDTO.consumeAmountStart})
            </if>
            <if test="queryDTO.consumeAmountStart== null and queryDTO.consumeAmountEnd != null">
                and exists (select 1 from
                (select a.user_id,sum(a.consume_amount) as "consumeTotalAmount" from
                (select pp.user_id,pp.split_pay_amount as "consume_amount"
                from blw_order_products_payment pp where pp.user_id=u.id
                union all
                select us.user_id,us.used_service_amount as "consume_amount"
                from blw_user_services us where us.user_id=u.id and us.used_service_amount>0) a
                group by a.user_id) b where b.consumeTotalAmount &lt;= #{queryDTO.consumeAmountEnd})
            </if>
            <if test="queryDTO.adviserUserIds != null and queryDTO.adviserUserIds.size() > 0">
                and (
                <foreach collection="queryDTO.adviserUserIds" item="adviserId" separator="or">
                    FIND_IN_SET(#{adviserId}, u.adviser_user_ids) > 0
                </foreach>
                )
            </if>
            <if test="queryDTO.keyword != null and queryDTO.keyword != ''">
                and (
                u.name like concat('%',#{queryDTO.keyword},'%')
                or u.phone like concat('%',#{queryDTO.keyword},'%')
                )
            </if>
            <if test="powerDTO.needFilter() and queryDTO.userType==2">
                <if test="!powerDTO.isDepartmentManager()">
                    AND u.adviser_user_ids like concat('%,',#{powerDTO.loginUserId},',%')
                </if>              
            </if>       

            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasStore()">
                AND us.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                </if>
                <if test="!powerDTO.hasStore()">
                    AND 1=2
                </if>
            </if>
            <if test="powerDTO.needFilter() and queryDTO.userType==1">
                <if test="powerDTO.hasDepartment()">
                    AND us.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>         
        </where>        
        <if test="queryDTO.storeId!= null or queryDTO.departmentId!=null or queryDTO.userType==1">
            group by u.id
        </if>
         order by u.id desc
    </select>

    <select id="userList" resultType="com.hishop.blw.repository.dto.UsersResultDTO">
        SELECT
            u.*
        FROM  blw_users u
        LEFT JOIN blw_user_stores us on u.id = us.user_id
        WHERE u.user_type = 2
          <if test="izPhone">
              and u.phone = #{phone}
          </if>
          <if test="!izPhone">
              and us.store_id = #{storeId}
              and (
              u.phone like concat('%',#{phone},'%')
                  or u.name like concat('%',#{phone},'%')
                  or u.nickname like concat('%',#{phone},'%')
                  or u.user_code like concat('%',#{phone},'%')
                    or u.id LIKE concat('%',#{phone},'%')
              )
          </if>

    </select>
    <resultMap id="selectUserMap" type="com.hishop.blw.repository.dto.SelectUserDTO">
        <result column="id" property="id"/>
        <result column="gender" property="gender"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="nickname" property="nickname"/>
        <result column="user_code" property="userCode"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="birthday" property="birthday"/>
        <result column="level_name" property="levelName"/>
        <result column="member_level_id" property="memberLevelId"/>
    </resultMap>
    <select id="selectUser" resultMap="selectUserMap">
        SELECT
            Users.id,
            Users.name,
            Users.gender,
            Users.nickname,
            Users.phone,
            Users.user_code,
            Users.birthday,
            Users.avatar_url,
            Points.available_points,
            UL.level_name
        FROM blw_users AS Users
                 LEFT JOIN blw_user_points AS Points ON Users.id = Points.user_id
                 LEFT JOIN blw_user_levels AS UL ON Users.member_level_id = UL.id
        WHERE Users.id = #{userId}
    </select>

    <select id="listByDepartmentId" resultType="com.hishop.blw.repository.entity.Users">
        select u.*
        from blw_users u join blw_user_stores s on u.id=s.user_id
        where s.department_id=#{departmentId}
        <if test="userName!=null and userName!=''">
            and u.`name` like concat('%',#{userName},'%')
        </if>
    </select>

    <select id="listMemberByStoreId" resultType="com.hishop.blw.repository.entity.Users">
        select u.*
        from blw_users u join blw_user_stores s on u.id=s.user_id
        where u.user_type = 2
            and s.store_id=#{po.storeId}
        <if test="po.keyword!=null and po.keyword!=''">
            and (
                u.id = #{po.keyword}
                or u.name like concat('%',#{po.keyword},'%')
                or u.phone like concat('%',#{po.keyword},'%')
                or u.user_code like concat('%',#{po.keyword},'%')
                or u.nickname like concat('%',#{po.keyword},'%')
            )
        </if>
    </select>
    <select id="listMemberByStaffId" resultType="com.hishop.blw.repository.entity.Users">
        select u.*
        from blw_users u
        where u.user_type = 2
        <if test="po.staffId != null">
            and u.adviser_user_ids like concat('%,',#{po.staffId},',%')
        </if>

        <if test="po.izPlat">
            and (
                u.user_code = #{po.keyword}
                or
                u.phone = #{po.keyword}
            )
        </if>
          <if test="!po.izPlat">
              <if test="po.keyword!=null and po.keyword!=''">
                  and (
                  u.id = #{po.keyword} or
                  u.name like concat('%',#{po.keyword},'%')
                  or u.phone like concat('%',#{po.keyword},'%')
                  or u.user_code like concat('%',#{po.keyword},'%')
                  or u.nickname like concat('%',#{po.keyword},'%')
                  )
              </if>
          </if>
    </select>

    <select id="listByStoreIdAndDepartmentId" resultType="com.hishop.blw.repository.entity.Users">
        select
            *
        from blw_users
        where user_type = 1
            and manager = 0
            <if test="departmentId!=null or storeId!=null or powerDTO!=null">
                and id in (
                select user_id
                from blw_user_stores
                where 1=1
                <if test="departmentId!=null">
                    and department_id=#{departmentId}
                </if>
                <if test="storeId!=null">
                    and store_id=#{storeId}
                </if>
                )
            </if>
    </select>




    <select id="listByStoreIdAndDepartmentIdAndUserId" resultType="com.hishop.blw.repository.entity.Users">
        select
            *
        from blw_users
        where user_type = 1
            and manager = 0
            and `status` != 5
            <if test="departmentId!=null or storeId!=null or powerDTO!=null">
                and id in (
                    select user_id
                    from blw_user_stores
                    where 1=1
                        <if test="departmentId!=null">
                            and department_id=#{departmentId}
                        </if>
                        <if test="storeId!=null">
                            and store_id=#{storeId}
                        </if>
                        <if test="powerDTO!=null and powerDTO.needFilter()">
                            <if test="powerDTO.hasStore()">
                                and store_id in
                                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="powerDTO.hasDepartment()">
                                and department_id in
                                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                                and 1=2
                            </if>
                        </if>
                )
            </if>
            <if test="userId!=null">
                and id = #{userId}
            </if>
    </select>

    <select id="listByKeyword" resultType="com.hishop.blw.repository.entity.Users">
        select u.*
        from blw_users u
        where u.user_type = 2
        <if test="po.keyword != null and po.keyword != ''">
            <choose>
                <when test="po.keyword.matches('^\\d{11}$')">
                    and u.phone = #{po.keyword}
                </when>
                <otherwise>
                    and (
                        u.name LIKE CONCAT('%', #{po.keyword}, '%')
                        or
                        u.phone LIKE CONCAT('%', #{po.keyword}, '%')
                    )
                    and u.id in (
                        select s.user_id
                        from blw_user_stores s
                        where s.store_id = #{po.storeId}
                    )
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="getDetailById" resultType="com.hishop.blw.repository.dto.UsersResultDTO">
        select
            u.*,r.name as roleName
        from blw_users u
        left join blw_user_role ur on u.id = ur.user_id
        left join blw_role r on ur.role_id = r.id
        where u.id = #{id}
    </select>


    <select id="list" resultType="com.hishop.blw.repository.entity.Users">
        select *
        from blw_users
        <where>
            <if test="query.userType!=null">
                and user_type = #{query.userType}
            </if>
            <if test="query.manager!=null">
                and manager = #{query.manager}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasDepartment()">
                    and id in (
                        select user_id
                        from blw_user_stores
                        where department_id in
                        <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    )
                </if>
                <if test="!powerDTO.hasDepartment()">
                    AND 1=2
                </if>
            </if>
        </where>

    </select>

    <select id="getEmployeeForBoard" resultType="com.hishop.blw.repository.entity.Users">
        SELECT * FROM blw_users as u                               
        WHERE  u.user_type = 1 and u.status in (1,2) and u.id in (
            select distinct user_id from blw_user_stores as s where s.store_id = #{storeId}
            <if test="powerDTO.needFilter()">           
                <if test="powerDTO.hasDepartment()">
                    and s.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>              
            </if>
        )
        <if test="powerDTO.needFilter()"> 
                <if test="!powerDTO.isDepartmentManager()">
                    and u.id = #{powerDTO.loginUserId}
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    and 1=2
                </if>
        </if>
    </select>

    <select id="getStoreHeadPhone" resultType="java.lang.String">
        SELECT u.phone FROM blw_users as u
                                LEFT JOIN blw_user_stores as s on u.id = s.user_id
        WHERE  s.store_id = #{storeId} and u.user_type = 1 and u.identity = 1
        GROUP BY u.id
        limit 1
    </select>

    <select id="selectUserByBirthday" resultType="com.hishop.blw.repository.entity.Users">
        select t.* from blw_users t where DATE_FORMAT(birthday, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')
    </select>

    <select id="selectUserByStoreIdType" resultType="com.hishop.blw.repository.dto.UserDto">
        select t.id, t.user_type, t.name, s.store_id, s.department_id from blw_users t
        left join blw_user_stores s on t.id = s.user_id
        where s.store_id = #{storeId} and t.user_type = #{userType}
    </select>

    <select id="getRegisterMemberCount" resultType="java.lang.Long">
        select count(distinct u.id) as "number"
        from blw_users u
        <if test="storeId != null">
            join blw_user_stores us on u.id=us.user_id
        </if>
        where u.create_date between #{startDate} and #{endDate}
        <if test="storeId != null">
            and us.store_id=#{storeId}
        </if>
    </select>
</mapper>