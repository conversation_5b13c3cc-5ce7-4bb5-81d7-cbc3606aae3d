<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.RefundItemsExtendMapper">

    <select id="getRefundAmount2" resultType="com.hishop.blw.model.vo.refund.OrderItemRefundVO">
        SELECT ifnull(sum(ri.refund_amount),0) as refundAmount,
        ifnull(sum(ri.refund_points_amount),0) as refundPointsAmount,
        ifnull(sum(ri.refund_quantity),0) as refundQuantity
        FROM blw_refund_items_extend ri
        LEFT JOIN blw_refund r on r.id = ri.refund_id
        WHERE ri.order_item_id = #{orderItemId} and ri.product_id = #{productId}
        <if test="startTime != null">
            AND r.handler_time between #{startTime} and #{endTime}
        </if>
    </select>

</mapper>