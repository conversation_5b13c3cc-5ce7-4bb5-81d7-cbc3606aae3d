<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.AreasMapper">



    <select id="getByStoreCity" resultType="com.hishop.blw.repository.entity.Areas">
        SELECT
            *
        FROM
            blw_areas
        WHERE
            id IN (
                SELECT DISTINCT s.city_id FROM blw_store s WHERE s.city_id IS NOT NULL and `status` = 1
            )
        order by id
    </select>

</mapper>