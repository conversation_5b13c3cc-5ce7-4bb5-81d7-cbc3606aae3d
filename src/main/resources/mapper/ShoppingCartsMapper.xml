<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.ShoppingCartsMapper">

    <update id="updateQuantity">
        update blw_shopping_carts
        set quantity = #{quantity}
        where id = #{id}
    </update>
    <resultMap id="cartListByUserIdMapper" type="com.hishop.blw.repository.entity.ShoppingCarts">
        <result column="id" property="id"/>
        <result column="userId" property="userId"/>
        <result column="productId" property="productId"/>
        <result column="quantity" property="quantity"/>
    </resultMap>
    <select id="cartListByUserId" resultMap="cartListByUserIdMapper">
        SELECT * FROM blw_shopping_carts as a
                          LEFT JOIN blw_products as b ON a.product_id = b.id
        WHERE a.user_id = #{userId} AND b.product_type=5
    </select>
</mapper>