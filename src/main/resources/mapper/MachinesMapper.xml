<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.MachinesMapper">

    <select id="listByOrderId" resultType="com.hishop.blw.repository.dto.MachinesResultDTO">
        SELECT
            b.id AS 'id',
            b.NAME AS 'name',
            b.identification_code AS 'identificationCode'
        FROM blw_machines_usage AS a
        LEFT JOIN blw_machines as b on a.machine_id = b.id
        <where>
            <if test="printType == 1">
                a.department_id in
                (
                    SELECT department_id FROM blw_products as  p
                    LEFT JOIN  blw_order_items as oi on p.id = oi.product_id
                    WHERE oi.order_id = #{orderId}
                )
                <if test="storeId != null and storeId > 0">
                    AND a.store_id = #{storeId}
                </if>
                AND  EXISTS
                (
                    SELECT 1 FROM blw_machines_usage AS C WHERE a.id = c.id
                    and (c.category_id = 0 or c.category_id is null )
                )
            </if>
            <if test="printType == 2">
                a.category_id in
                (
                    SELECT category_id FROM blw_products as  p
                    LEFT JOIN  blw_order_items as oi on p.id = oi.product_id
                    WHERE oi.order_id = #{orderId}
                )
            </if>
        </where>
    </select>

    <select id="pageList" resultType="com.hishop.blw.repository.dto.MachinesResultDTO">
        SELECT
            m.id AS 'id',
            m.NAME AS 'name',
            m.identification_code AS 'identificationCode',
            GROUP_CONCAT(DISTINCT d.department_name SEPARATOR ', ' ) AS 'departmentName',
            GROUP_CONCAT(DISTINCT s.store_name SEPARATOR ', ' ) AS 'storeName',
            GROUP_CONCAT(DISTINCT c.category_name SEPARATOR ', ' ) AS 'categoryName'
        FROM
            blw_machines m
            LEFT JOIN blw_machines_usage mu ON m.id = mu.machine_id
            LEFT JOIN blw_department d ON mu.department_id = d.id
            LEFT JOIN blw_store s ON mu.store_id = s.id
            LEFT JOIN blw_product_categories c ON mu.category_id = c.id
        <where>
            <if test="dto.storeId!= null">
                AND s.id = #{dto.storeId}
            </if>
            <if test="dto.departmentId!= null">
                AND d.id = #{dto.departmentId}
            </if>

            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasDepartment()">
                    AND d.id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasDepartment()">
                    AND 1=2
                </if>
                <if test="!powerDTO.isDepartmentManager()">
                    AND m.create_by = #{powerDTO.loginUserId}
                </if>
            </if>
        </where>
        GROUP BY
            m.id,
            m.NAME,
            m.identification_code
        ORDER BY
            m.id DESC
    </select>
    <select id="listByCategoryId" resultType="com.hishop.blw.repository.dto.MachinesResultDTO">
        SELECT
        b.id AS 'id',
        b.NAME AS 'name',
        b.identification_code AS 'identificationCode'
        FROM blw_machines_usage AS a
        LEFT JOIN blw_machines as b on a.machine_id = b.id
        where
                a.category_id = #{categoryId}
         and a.store_id = #{storeId}
    </select>
</mapper>