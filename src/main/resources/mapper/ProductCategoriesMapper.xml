<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.ProductCategoriesMapper">

    <select id="listPage" resultType="com.hishop.blw.repository.entity.ProductCategories">
        SELECT pc.* FROM blw_product_categories as pc
        LEFT JOIN blw_store_service_areas ssa  on ssa.department_id = pc.department_id
        WHERE pc.category_level = 1
        <if test="storeId != null and storeId > 0">
            AND ssa.store_id = #{storeId}
        </if>
        <if test="departId != null and departId > 0">
            AND ssa.department_id = #{departId}
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and ssa.store_id IN
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                AND ssa.department_id IN
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="pageForReport" resultType="com.hishop.blw.repository.entity.ProductCategories">
        select pc.*
        from blw_product_categories pc join blw_store_service_areas o on pc.department_id=o.department_id
        <if test="queryPO.storeId != null or queryPO.departId != null">
            <if test="queryPO.departId != null">
                join blw_department d on pc.department_id=d.id and d.id=#{queryPO.departId}
                <if test="queryPO.storeId != null">
                    join blw_store_service_areas sa on d.id=sa.department_id and sa.store_id= #{queryPO.storeId}
                </if>
            </if>
            <if test="queryPO.departId == null">
                join blw_department d on pc.department_id=d.id
                join blw_store_service_areas sa on d.id=sa.department_id and sa.store_id= #{queryPO.storeId}
            </if>
        </if>
        where pc.parent_category_id=0
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                and o.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="powerDTO.hasDepartment()">
                and o.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                and 1=2
            </if>
        </if>
        group by pc.id
    </select>
</mapper>