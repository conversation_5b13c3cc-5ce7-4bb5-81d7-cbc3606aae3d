<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.UserStoresMapper">

    <select id="userStoresList" resultType="com.hishop.blw.repository.dto.UserStoresResDTO">
        select bus.store_id as storeId,bus.department_id as "departmentId",bs.store_name as storeName
        from blw_user_stores bus
        join blw_store bs on bus.store_id = bs.id
        <if test="dto.userId != null">
            where bus.user_id = #{dto.userId}
        </if>
        <if test="dto.isAll != null and dto.isAll == false">
            and bs.status = 1
        </if>
        <if test="powerDTO != null and powerDTO.needFilter()">
            <if test="powerDTO.hasDepartment() and powerDTO.getDepartmentIds().size() > 0">
                and bus.department_id in
                <foreach collection="powerDTO.getDepartmentIds()" item="departmentId" open="(" close=")" separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </if>
    </select>

    <select id="listByStoreIds" resultType="com.hishop.blw.model.vo.user.UserStoresVO">
        SELECT userstore.* FROM blw_user_stores userstore
        LEFT JOIN blw_users users on userstore.user_id = users.id
        WHERE users.user_type = 2
          <if test="storeIds != null and storeIds.size() > 0">
              AND userstore.store_id in
              (
              <foreach collection="storeIds" item="storeId" separator=",">
                  #{storeId}
              </foreach>
              )
          </if>
    </select>
    <select id="getDepartmentUser" resultType="com.hishop.blw.model.vo.store.DepartmentUserVO">
        SELECT users.phone, t.department_name, s.store_name,users.identity,users.status FROM blw_user_stores userstore
        LEFT JOIN blw_users users on userstore.user_id = users.id
        LEFT JOIN blw_department t on userstore.department_id = t.id
        LEFT JOIN blw_store s on userstore.store_id = s.id
        WHERE users.user_type = 1 and userstore.store_id = #{storeId} and userstore.department_id = #{departmentId}
    </select>
</mapper>