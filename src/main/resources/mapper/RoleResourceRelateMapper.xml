<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.RoleResourceRelateMapper">

    <select id="listResourceIdsByRoleId" resultType="long">
        SELECT resource_id FROM blw_role_resource WHERE role_id = #{roleId}
    </select>

    <select id="checkResourceAuth" resultType="integer">
        SELECT
            count(r.id)
        FROM
            blw_resource r
        INNER JOIN blw_role_resource rr ON r.id = rr.resource_id
        WHERE rr.role_id = #{roleId}
        AND FIND_IN_SET(#{resourceId}, r.parent_ids)
    </select>

    <select id="listButtonByRoleIds" resultType="com.hishop.blw.repository.entity.Resource">
        select r.*
        from blw_role_resource rr join blw_resource r on rr.resource_id=r.id
        where r.type=3 and rr.role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

</mapper>