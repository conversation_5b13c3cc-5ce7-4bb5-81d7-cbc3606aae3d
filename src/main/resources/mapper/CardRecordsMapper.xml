<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.blw.repository.dao.CardRecordsMapper">

    <select id="pageList" resultType="com.hishop.blw.repository.entity.CardRecords">
        select b.*
        from blw_card_records b
        <where>
            <if test="queryDTO.cardId != null and  queryDTO.cardId > 0">

            </if>
            <if test="queryDTO.keyWords != null and queryDTO.keyWords != ''">
               and b.user_id in (
                SELECT id FROM blw_users us WHERE us.user_type = 2
                AND (us.id like  concat('%',#{queryDTO.keyWords},'%')
                    or us.phone like  concat('%',#{queryDTO.keyWords},'%')
                    or us.name like  concat('%',#{queryDTO.keyWords},'%')
                    or us.nickname like  concat('%',#{queryDTO.keyWords},'%'))
                )
            </if>
            <if test="queryDTO.memberCardId != null">
                and b.member_card_id = #{queryDTO.memberCardId}
            </if>
            <if test="queryDTO.id != null and queryDTO.id > 0">
                and b.id=#{queryDTO.id}
            </if>
            <if test="queryDTO.userId != null">
                and b.user_id=#{queryDTO.userId}
            </if>       
            <if test="queryDTO.cardType != null">
                and b.card_type=#{queryDTO.cardType}
            </if>
            <if test="queryDTO.storeId != null and queryDTO.storeId > 0">
                and b.store_id = #{queryDTO.storeId}
            </if>
            <if test="queryDTO.type != null">
                <if test="queryDTO.type == 1">
                    and b.type in (1,2,9)
                </if>
                <if test="queryDTO.type > 1">
                    and b.type=#{queryDTO.type}
                </if>
            </if>
            <if test="queryDTO.types != null and queryDTO.types.size() > 0">
                and b.type in
                <foreach collection="queryDTO.types" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDTO.createDateBegin != null">
                and b.create_date BETWEEN #{queryDTO.createDateBegin} AND #{queryDTO.createDateEnd}
            </if>
            <if test="queryDTO.nickName != null and  queryDTO.nickName != ''">
               and b.user_id in
                (
                    SELECT id from blw_users where nickname  like concat('%',#{queryDTO.nickName},'%') or name like concat('%',#{queryDTO.nickName},'%')
                )
            </if>
            <if test="queryDTO.phone != null and  queryDTO.phone != ''">
               and b.user_id in
                (
                    SELECT id from blw_users where phone=#{queryDTO.phone}
                )
            </if>
            <if test="queryDTO.payMethodId != null and  queryDTO.payMethodId > 0">
                and b.pay_method_id = #{queryDTO.payMethodId}
            </if>
            <if test="queryDTO.cardType != null and queryDTO.cardType > 0">
                and b.card_type = #{card_type}
            </if>
            <if test="queryDTO.cardId != null and queryDTO.cardId > 0">
                and b.member_card_id in (select id from blw_member_cards where template_card_id in (select id from blw_template_cards where card_id = #{queryDTO.cardId}))
            </if>
            <if test="powerDTO.needFilter() and !queryDTO.filterAll">
            <if test="!powerDTO.isDepartmentManager()">
                    AND (b.sales_user_id = #{powerDTO.loginUserId} or b.create_by = #{powerDTO.loginUserId})
                </if>
                <if test="powerDTO.hasStore()">
                    AND b.member_card_id IN  (SELECT id FROM blw_member_cards WHERE store_id IN
                        <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    )
                </if>
                <if test="powerDTO.hasDepartment()">
                    AND (b.department_id IN
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach> or b.department_id = 0)
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    AND 1=2
                </if>          
            </if>
        </where>
        order by b.create_date desc
    </select>

    <select id="pageRechargeProxy" resultType="com.hishop.blw.repository.dto.CardRecordsResultDTO">
        select cr.id,cr.user_id "userId",u.user_code "userCode",cr.amount,cr.create_by "createBy",cr.create_date "createDate",c.card_type "cardType",c.card_name "cardName"
        from blw_card_records cr join blw_member_cards mc on cr.member_card_id=mc.id
                                 join blw_template_cards tc on mc.template_card_id=tc.id
                                 join blw_cards c on tc.card_id=c.id
                                 join blw_users u on cr.user_id=u.id
        where cr.type in(2,9) and cr.create_by = #{queryDTO.createBy}
            <if test="queryDTO.userCode != null">
                and u.user_code = #{queryDTO.userCode}
            </if>
            <if test="queryDTO.createDateBegin != null">
                and cr.create_date BETWEEN #{queryDTO.createDateBegin} AND #{queryDTO.createDateEnd}
            </if>
    </select>

    <select id="rechargeReport" resultType="com.hishop.blw.repository.entity.CardRecords">
        SELECT
        records.*
        FROM blw_card_records records
        LEFT JOIN blw_users users on records.user_id = users.id
        WHERE records.type in (1,2,5,7,9,11)
        and (
            records.pay_method_id in
            (
                SELECT pmt.id FROM blw_pay_methods pmt
                LEFT JOIN blw_cards  cd on pmt.card_type = cd.card_type
                WHERE pmt.iz_recharge_report = 1
            )
            or records.pay_method_id is null
        )
        <if test="queryDTO.startDate != null">
            AND records.create_date >= #{queryDTO.startDate}
        </if>
        <if test="queryDTO.endDate !=null">
            AND records.create_date &lt;= #{queryDTO.endDate}
        </if>
        <if test="queryDTO.storeId != null and queryDTO.storeId > 0">
            AND records.store_id = #{queryDTO.storeId}
        </if>
        <if test="queryDTO.payMethodId != null and queryDTO.payMethodId > 0">
            AND records.pay_method_id = #{queryDTO.payMethodId}
        </if>
        <if test="queryDTO.salesUserId != null and queryDTO.salesUserId >0">
            AND records.sales_user_id = #{queryDTO.salesUserId}
        </if>
        <if test="queryDTO.flag != null and queryDTO.flag >= 0">
            AND records.flag = #{queryDTO.flag}
        </if>
        <if test="queryDTO.rechargeStartAmount != null and queryDTO.rechargeStartAmount > 0 and queryDTO.rechargeEndAmount != null and queryDTO.rechargeEndAmount > 0">
            AND records.amount between #{queryDTO.rechargeStartAmount} and #{queryDTO.rechargeEndAmount}
        </if>
        <if test="queryDTO.userName != null and queryDTO.userName != ''">
            AND users.name like concat('%',#{queryDTO.userName},'%')
        </if>
        <if test="queryDTO.userPhone != null and queryDTO.userPhone != ''">
            AND users.phone like concat('%',#{queryDTO.userPhone},'%')
        </if>
        <if test="powerDTO.needFilter()">
            <if test="powerDTO.hasStore()">
                AND records.store_id in
                <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>   
            </if>           
            <if test="!powerDTO.hasStore()">
                and 1=2
            </if>
            <if test="!powerDTO.isDepartmentManager()">
                AND records.sales_user_id = #{powerDTO.loginUserId}
            </if>
        </if>
        order by records.create_date desc
    </select>

    <select id="rechargeSummaryReport" resultType="com.hishop.blw.repository.dto.CardRecordsDTO">
        SELECT
            cd.id as cardId,
            cd.card_name as cardName,
            r.*
        from blw_card_records r
        left join blw_users users on r.user_id = users.id
        LEFT JOIN blw_member_cards c on r.member_card_id = c.id
        LEFT JOIN blw_template_cards tc on c.template_card_id = tc.id
        left join blw_cards  cd on cd.id = tc.card_id
        <where>
            r.type in (1,2,5,7,9,11) and
            r.pay_method_id in (
                select id from blw_pay_methods where iz_recharge_summary_report = 1
            )
            <if test="query.startDate != null and query.endDate != null">
                and r.create_date between #{query.startDate} and #{query.endDate}
            </if>
            <if test="query.storeId != null and query.storeId > 0">
                and r.store_id = #{query.storeId}
            </if>
            <if test="query.departmentId != null and query.departmentId > 0">
                and r.department_id = #{query.departmentId}
            </if>
            <if test="query.salesUserId != null and query.salesUserId > 0">
                and r.sales_user_id = #{query.salesUserId}
            </if>
            <if test="query.userName != null and query.userName != ''">
                and users.name like concat('%',#{query.userName},'%')
            </if>
            <if test="query.userPhone != null and query.userPhone != ''">
                and users.phone like concat('%',#{query.userPhone},'%')
            </if>
            <if test="query.cardId != null and query.cardId > 0">
                and cd.id = #{query.cardId}
            </if>
            <if test="query.flag != null">
                <if test="query.flag == 1">
                    and r.flag = 0 -- 标记为首充表示开卡
                </if>
                <if test="query.flag == 2">
                    and r.flag != 0 -- 标记为续卡，不是首充，且属于充值
                    and r.type in (1,2)
                </if>
                <if test="query.flag == 3">
                    and r.type = 4
                </if>
                <if test="query.flag == 4">
                    and r.type = 5
                </if>
            </if>
            <if test="query.cardType != null and query.cardType > 0">
                and r.card_type = #{query.cardType}
            </if>
            <if test="powerDTO.needFilter()">
                <if test="powerDTO.hasStore()">
                    and r.store_id in
                    <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="powerDTO.hasDepartment()">
                    and r.department_id in
                    <foreach collection="powerDTO.getDepartmentIds()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="!powerDTO.hasStore() or !powerDTO.hasDepartment()">
                    and 1=2
                </if>
                <if test="!powerDTO.isDepartmentManager()">
                    AND r.sales_user_id = #{powerDTO.loginUserId}
                </if>
            </if>
        </where>
    </select>

    <select id="crossStoreConsumptionPageList" resultType="com.hishop.blw.model.vo.order.CrossStoreConsumptionVO">
        select aa.hstoreId, aa.cardId, aa.xstoreId, aa.card_name, aa.xstoreName, aa.hstoreName, IFNULL(sum(aa.amount),0) as amount,
               DATE_FORMAT(aa.create_date, '%Y-%m') AS dates
        from (select
                  t3.id as xstoreId, t3.store_name as xstoreName, t2.id as hstoreId, t2.store_name as hstoreName,
                  m.id as cardId, m.card_name, t.amount, t.create_date
              from
                  blw_card_records t
                      INNER JOIN blw_orders t1 on t.order_id = t1.id
                      LEFT JOIN blw_store t2 ON t.store_id = t2.id
                      LEFT JOIN blw_store t3 ON t1.store_id = t3.id
                      LEFT JOIN
                  (select c.id, c.card_name, a.id as member_card_id from blw_member_cards a
                                                                    INNER JOIN blw_template_cards b ON a.template_card_id = b.id
                                                                     INNER JOIN blw_cards c ON b.card_id = c.id) m ON t.member_card_id = m.member_card_id
              where t.store_id != t1.store_id
              <if test="queryDTO.startTime != null and queryDTO.startTime != ''">
                  AND t.create_date >= #{queryDTO.startTime}
              </if>
              <if test="queryDTO.endTime != null and queryDTO.endTime != ''">
                AND t.create_date &lt; #{queryDTO.endTime}
              </if>
              <if test="queryDTO.xstoreId != null">
                AND t3.id = #{queryDTO.xstoreId}
              </if>
              <if test="queryDTO.hstoreId != null">
                AND t2.id = #{queryDTO.hstoreId}
              </if>
              <if test="queryDTO.cardIds != null and queryDTO.cardIds.size > 0">
                  AND m.id IN
                  <foreach collection="queryDTO.cardIds" item="cardId" separator="," open="(" close=")">
                      #{cardId}
                  </foreach>
              </if>
               <if test="powerDTO.needFilter()">
                  <if test="!powerDTO.isDepartmentManager()">
                    AND t.sales_user_id = #{powerDTO.loginUserId}
                  </if>
                </if>          
              ) aa
              <where>
                <if test="powerDTO.needFilter()">
                    <if test="powerDTO.hasStore()">
                        (aa.xstoreId IN
                        <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        OR
                        aa.hstoreId IN
                        <foreach collection="powerDTO.getStoreIds()" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="!powerDTO.hasStore()">
                        AND 1=2
                    </if>
                </if>
              </where>

        GROUP BY aa.hstoreId, aa.cardId, aa.xstoreId, DATE_FORMAT(aa.create_date, '%Y-%m')
        ORDER BY dates desc, aa.hstoreId desc
    </select>

    <select id="crossStoreConsumptionDetailPageList" resultType="com.hishop.blw.model.vo.order.CrossStoreConsumptionDetailVO">
        select
        bb.hstoreId, bb.cardId, bb.xstoreId, bb.card_name, bb.xstoreName, bb.hstoreName, bb.amount,
        bb.dates, bb.orderId, bb.order_no, bb.user_id, bb.proxy_user_id,
        GROUP_CONCAT(boi.product_name SEPARATOR ',') AS itemNames, (case when b1.nickname is null then b1.name else b1.nickname end) nickname, b1.phone, b2.nickname as proxyNickname, bb.pay_date,
        (select IFNULL(sum(t.amount),0) from blw_payments t
        where t.order_id = bb.orderId
        AND t.payment_time >= #{queryDTO.startTime}
        AND t.payment_time &lt; #{queryDTO.endTime}) AS actualAmount
        from
        (select aa.hstoreId, aa.cardId, aa.xstoreId, aa.card_name, aa.xstoreName, aa.hstoreName, IFNULL(sum(aa.amount),0) as amount,
        DATE_FORMAT(aa.create_date, '%Y-%m') AS dates, aa.orderId, aa.order_no, aa.user_id, aa.proxy_user_id, aa.pay_date
        from (select
        t3.id as xstoreId, t3.store_name as xstoreName, t2.id as hstoreId, t2.store_name as hstoreName,
        m.id as cardId, m.card_name, t.amount, t.create_date, t1.order_no, t1.id as orderId, t1.user_id, t1.proxy_user_id, t1.pay_date
        from
        blw_card_records t
        INNER JOIN blw_orders t1 on t.order_id = t1.id
        LEFT JOIN blw_store t2 ON t.store_id = t2.id
        LEFT JOIN blw_store t3 ON t1.store_id = t3.id
        LEFT JOIN
        (select c.id, c.card_name, a.id as member_card_id from blw_member_cards a
        INNER JOIN blw_template_cards b ON a.template_card_id = b.id
        INNER JOIN blw_cards c ON b.card_id = c.id) m ON t.member_card_id = m.member_card_id
        where t.store_id != t1.store_id
        AND t3.id = #{queryDTO.xstoreId}
        AND t2.id = #{queryDTO.hstoreId}
        AND m.id = #{queryDTO.cardId}
        AND t.create_date >= #{queryDTO.startTime}
        AND t.create_date &lt; #{queryDTO.endTime}
        ) aa
        group by aa.hstoreId, aa.cardId, aa.xstoreId, DATE_FORMAT(aa.create_date, '%Y-%m'), aa.orderId) bb
        LEFT JOIN blw_order_items boi ON bb.orderId = boi.order_id
        LEFT JOIN blw_users b1 ON b1.id = bb.user_id
        LEFT JOIN blw_users b2 ON b2.id = bb.proxy_user_id
        WHERE 1 = 1
        <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
            AND bb.order_no like concat('%',#{queryDTO.orderNo},'%')
        </if>
        <if test="queryDTO.nickname != null and queryDTO.nickname != ''">
            AND b1.nickname like concat('%',#{queryDTO.nickname},'%')
        </if>
        <if test="queryDTO.phone != null and queryDTO.phone != ''">
            AND b1.phone like concat('%',#{queryDTO.phone},'%')
        </if>
        <if test="queryDTO.productName != null and queryDTO.productName != ''">
            AND boi.product_name like concat('%',#{queryDTO.productName},'%')
        </if>
        <if test="queryDTO.proxyUserId != null">
            AND b2.id = #{queryDTO.proxyUserId}
        </if>
        GROUP BY bb.orderId ORDER BY bb.orderId DESC
    </select>

    <select id="listCardInfoByMemberCardId" resultType="com.hishop.blw.repository.dto.CardBaseInfoDTO">
        SELECT
            cards.card_number as cardNumber,
            c.id as cardId,
            c.card_name as cardName
        from blw_member_cards cards
        left join blw_template_cards tc on cards.template_card_id = tc.id
        left join blw_cards c on c.id = tc.card_id
        WHERE cards.id in
        <foreach collection="cardIds" item="cardId" separator="," open="(" close=")">
            #{cardId}
        </foreach>
    </select>
</mapper>