server:
  # 开启压缩
  compression:
    enabled: true
    min-response-size: 2048
  #上下文路径
  servlet:
    context-path: /hishop-blw
  port: 8086
  #ssl:
    #enabled: true
    # 或 .pfx文件
    #key-store: classpath:tomcat.jks
    # 证书密码
    #key-store-password: local
    # 若为PFX则改为PKCS12
    #key-store-type: JKS
    # 通过keytool -list查看别名
  #key-alias: blw.sunnycare.com
    #key-alias: local
spring:
  application:
    name: hishop-blw
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: uF0jB2hM5bW3
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
      max-lifetime: 1800000     # 30分钟（单位：毫秒）
      validation-timeout: 5000  # 5秒验证超时
      leak-detection-threshold: 60000 # 60秒泄漏检测
  redis:
    host: r-2zeuy0jh2azclb14b2.redis.rds.aliyuncs.com
    port: 6379
    password: H!shop123456
    database: 5
  #文件上传设置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
# mybaits-plus配置
mybatis-plus:
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
swagger:
  knife4j:
    enable: false
    apiInfo:
      title: 碧朗湾项目接口文档
      description: 碧朗湾项目接口文档
      version: v1.0
      apiUrl: http://127.0.0.1:8081/hishop-blw/doc.html
      baseApiPkg: com.hishop.blw.controller
      groupName: hishopBlw
      contactEmail: <EMAIL>
      contactName: hishop
#测试打开健康检查端口
management:
  endpoints:
    web:
      exposure:
        include: health,info
nfs:
  oss:
    accessKeyId: LTAI5t7UYUzLFVsjtJ1PGFeL
    accessKeySecret: ******************************
    bucketName: 0218blwbucket
    endPoint: oss-cn-beijing.aliyuncs.com
    domain: 0218blwbucket.oss-cn-beijing.aliyuncs.com
    region: cn-beijing
    internalEndPoint: https://oss-cn-beijing.aliyuncs.com
    externalEndPoint: oss-cn-beijing.aliyuncs.com
    mtsLocationRegion:
  mpc:
    productId: 6b5e628ef4f4473aa80b2e9195137687
    endpoint: https://mpc.cn-south-1.myhuaweicloud.com
    templateId: 7000524
    audioTemplateId: 7001053
#wx:
#  mini:
#    config: 2d960bec124890b2cff66d0c36518822
#    tokenUrl: https://api.weixin.qq.com/cgi-bin/token
#    uploadUrl: https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info
wx:
  miniapp:
    isOpen: true
    envVersion: release
    configs:
      - appid: wx539712c93924fa27
        secret: 5bdfa63b7f8912a82f4b7d9a14063e34
ali:
  accessKeyId: LTAI5t7UYUzLFVsjtJ1PGFeL
  accessKeySecret: ******************************
  signName: 北京碧朗湾健康产业集团
  applySceneContent:  http://www.sunnycare.com
  verificationReminder: SMS_479780053
blw:
  seckey: blwseckey
print:
  user: <EMAIL>
  ukey: XbYMamzdAQBBNjg7
consumer:
  statistics:
    max-row: 10000