package com.hishop.blw.interceptor;

import cn.hutool.core.collection.CollectionUtil;
import com.hishop.blw.biz.ResourceBiz;
import com.hishop.blw.constants.BasicConstants;
import com.hishop.blw.model.vo.role.ResourceVO;
import com.hishop.blw.repository.service.ResourceService;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 按钮访问权限拦截器
 *
 */
@Slf4j
@Component
public class ButtonAuthInterceptor implements HandlerInterceptor {

    @Resource
    private ResourceBiz resourceBiz;
    @Resource
    private ResourceService resourceService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //判断
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == BasicConstants.PLAT_MANAGER_ID) {
            return true;
        }
        //从request获取请求路径
        String requestURI = request.getRequestURI();
        com.hishop.blw.repository.entity.Resource resource = resourceService.getByPath(requestURI);
        if (resource == null) {
            return true;
        }
        List<ResourceVO> resourceVOList = resourceBiz.listButtonByUserId(loginUser.getUserId());
        if (CollectionUtil.isEmpty(resourceVOList)) {
            throw new BusinessException("您没有操作权限，请联系后台人员");
        }
        List<Long> resourceIds = resourceVOList.stream().map(ResourceVO::getId).collect(Collectors.toList());
        if (!resourceIds.contains(resource.getId())) {
            throw new BusinessException("您没有操作权限，请联系后台人员");
        }
        return true;
    }

}
