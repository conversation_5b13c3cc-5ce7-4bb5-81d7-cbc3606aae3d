package com.hishop.blw.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**   
 * @Description: 销售报表返回对象
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Data
@ApiModel(value = "SaleReportVO", description = "销售报表返回对象")
public class SaleReportVO {

    @ApiModelProperty(name = "id" , value = "订单id")
    private Long id;

    @ApiModelProperty(name = "orderNo" , value = "订单编号")
	private String orderNo;
    
    @ApiModelProperty(name = "orderStatus" , value = "订单状态 1-待结算 2-已结算 3-已取消 4-已完成 5-已关闭")
	private Integer orderStatus;
    @ApiModelProperty(name = "orderStatusName" , value = "订单状态名称")
    private String orderStatusName;

    @ApiModelProperty(name = "orderDate" , value = "下单日期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(name = "orderSourceId" , value = "订单来源")
	private Long orderSourceId;
    @ApiModelProperty(name = "orderSourceName" , value = "订单来源名称")
    private String orderSourceName;

    @ApiModelProperty(name = "orderCategoryName" , value = "订单类别")
    private String orderCategoryName;
    
    @ApiModelProperty(name = "userId" , value = "会员id")
	private Long userId;
    @ApiModelProperty(name = "userName" , value = "会员名称")
    private String userName;
    @ApiModelProperty(name = "phone" , value = "会员手机号")
    private String phone;
    @ApiModelProperty(name = "adviserUserIds" , value = "顾问id集合")
    private String adviserUserIds;
    @ApiModelProperty(name = "adviserUserNames" , value = "顾问名称")
    private String adviserUserNames;
    
    @ApiModelProperty(name = "recommendedUserId" , value = "推荐员工id")
	private Long recommendedUserId;
    @ApiModelProperty(name = "recommendedUserName" , value = "推荐员工名称")
    private String recommendedUserName;

    @ApiModelProperty(name = "assistantUserId" , value = "辅助员工id")
	private Long assistantUserId;
    @ApiModelProperty(name = "assistantUserName" , value = "辅助员工名称")
    private String assistantUserName;

    @ApiModelProperty(name = "salesUserId" , value = "销售员工id")
	private Long salesUserId;
    @ApiModelProperty(name = "salesUserName" , value = "销售员工名称")
    private String salesUserName;

    @ApiModelProperty(name = "recommendedDepartmentId" , value = "推荐部门id")
    private Long recommendedDepartmentId;
    @ApiModelProperty(name = "recommendedDepartmentName" , value = "推荐部门名称")
    private String recommendedDepartmentName;

    @ApiModelProperty(name = "assistantDepartmentId" , value = "辅助部门id")
    private Long assistantDepartmentId;
    @ApiModelProperty(name = "assistantDepartmentName" , value = "辅助部门名称")
    private String assistantDepartmentName;

    @ApiModelProperty(name = "salesDepartmentId" , value = "销售部门id")
    private Long salesDepartmentId;
    @ApiModelProperty(name = "salesDepartmentName" , value = "销售部门名称")
    private String salesDepartmentName;

    @ApiModelProperty(name = "orderType" , value = "订单类型（1:购买服务、2:点餐）")
    private Integer orderType;

    @ApiModelProperty(name = "paymentMethod" , value = "支付方式（多个支付方式用+组合）")
    private String paymentMethod;

    @ApiModelProperty(name = "payDetail" , value = "支付金额明细")
    private String payDetail;

    @ApiModelProperty(name = "remark" , value = "备注")
    private String remark;

    @ApiModelProperty(name = "saleReportItemVOList" , value = "商品子项返回对象集合")
    List<SaleReportItemVO> saleReportItemVOList;
    @ApiModelProperty(name = "izRefund" , value = "是否包含退款")
    private Boolean izRefund;

    private BigDecimal pointsAmount;
    @ApiModelProperty(name = "eatNumber" , value = "就餐人数")
    private Integer eatNumbers;
}
