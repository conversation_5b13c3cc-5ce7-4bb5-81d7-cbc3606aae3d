package com.hishop.blw.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**   
 * @Description: 销售报表商品子项返回对象
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Data
@ApiModel(value = "SaleReportItemVO", description = "销售报表商品子项返回对象")
public class SaleReportItemVO {

    private Long id;

    private Long orderId;

    @ApiModelProperty(name = "orderDate" , value = "下单日期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date orderDate;

    @ApiModelProperty(name = "originalAmount" , value = "子项原价金额（前端不要用这个，后端专用）")
    private BigDecimal originalAmount;
    @ApiModelProperty(name = "actualAmount" , value = "子项实际支付金额")
    private BigDecimal actualAmount;

    @ApiModelProperty(name = "quantity" , value = "商品数量")
    private BigDecimal quantity;

    @ApiModelProperty(name = "productId" , value = "商品id")
    private Long productId;

    @ApiModelProperty(name = "productName" , value = "商品名称")
    private String productName;

    @ApiModelProperty(name = "productType" , value = "商品类型（1:商品、2:项目/服务、3:菜品、4:疗程、5:套餐）")
    private Integer productType;
    @ApiModelProperty(name = "productTypeName" , value = "商品类型名称")
    private String productTypeName;

    @ApiModelProperty(name = "oneCategoryName" , value = "一级分类名称")
    private String oneCategoryName;
    @ApiModelProperty(name = "twoCategoryName" , value = "二级分类名称")
    private String twoCategoryName;

    @ApiModelProperty(name = "departmentAdviserUserId" , value = "部门顾问id")
    private Long departmentAdviserUserId;

    @ApiModelProperty(name = "departmentAdviserUserName" , value = "部门顾问名称")
    private String departmentAdviserUserName;

    @ApiModelProperty(name = "projectDepartmentId" , value = "项目所属部门id（商品对应的部门id）")
    private Long projectDepartmentId;
    @ApiModelProperty(name = "productDepartmentName" , value = "项目所属部门名称")
    private String productDepartmentName;

    @ApiModelProperty(name = "extendList" , value = "疗程下面的子项")
    private List<SaleReportItemExtendVO> extendList;

    private BigDecimal pointsAmount;

    private BigDecimal amount;
}
