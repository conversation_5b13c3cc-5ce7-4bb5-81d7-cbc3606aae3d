package com.hishop.blw.model.po.remote;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * MedYSys06 PO对象
 */
@Data
public class OutInterfaceMedYSys06PO {
    @ApiModelProperty(name = "mId", value = "mId")
    private String mId;
    
    @ApiModelProperty(name = "status", value = "新建-已成交-已上门")
    private String status;
    
    @ApiModelProperty(name = "adviser", value = "推荐员工")
    private String adviser;
    
    @ApiModelProperty(name = "source", value = "推荐渠道")
    private String source;
    
    @ApiModelProperty(name = "sourceNotes", value = "渠道补充描述")
    private String sourceNotes;
    
    @ApiModelProperty(name = "doctor", value = "主诊人")
    private String doctor;
    
    @ApiModelProperty(name = "potIntention", value = "潜在意向")
    private String potIntention;
    
    @ApiModelProperty(name = "lastUptTime", value = "最后更新时间")
    private String lastUptTime;
    
    @ApiModelProperty(name = "nextPlanTime", value = "下次预约时间")
    private String nextPlanTime;
    
    @ApiModelProperty(name = "visitTimes", value = "到访次数")
    private String visitTimes;
    
    @ApiModelProperty(name = "chronicNotes", value = "慢性病")
    private String chronicNotes;
    
    @ApiModelProperty(name = "totalPay", value = "累计消费金额")
    private String totalPay;
    
    @ApiModelProperty(name = "ext01", value = "预留字段01")
    private String ext01;
    
    @ApiModelProperty(name = "ext02", value = "预留字段02")
    private String ext02;
    
    @ApiModelProperty(name = "ext03", value = "预留字段03")
    private String ext03;
    
    @ApiModelProperty(name = "ext04", value = "预留字段04")
    private String ext04;
    
    @ApiModelProperty(name = "ext05", value = "扩展字段05")
    private String ext05;
    
    @ApiModelProperty(name = "ext06", value = "扩展字段06")
    private String ext06;
    
    @ApiModelProperty(name = "ext07", value = "扩展字段07")
    private String ext07;
    
    @ApiModelProperty(name = "ext08", value = "扩展字段08")
    private String ext08;
    
    @ApiModelProperty(name = "ext09", value = "扩展字段09")
    private String ext09;
} 