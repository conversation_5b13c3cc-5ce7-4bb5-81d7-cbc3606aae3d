package com.hishop.blw.model.po.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description: 用户入参对象
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Data
@ApiModel(value = "UsersUpdatePO", description = "用户入参对象")
public class UsersUpdatePO {

    @ApiModelProperty(name = "id", value = "用户id")
    private Long id;

    @ApiModelProperty(name = "storeIds", value = "门店id列表")
    private List<Long> storeIds;

    @ApiModelProperty(name = "margeId", value = "被合并用户id")
    private Long margeId;

    @ApiModelProperty(name = "memberLevelId", value = "会员等级id")
    private Long memberLevelId;

    @ApiModelProperty(name = "status", value = "状态,1:启用；2：停用")
    private Integer status;

    @ApiModelProperty(name = "name", value = "姓名")
    private String name;

    @ApiModelProperty(name = "gender", value = "性别,1:男，0：女")
    @NotNull(message = "性别不能为空")
    private Integer gender;

    @ApiModelProperty(name = "birthday", value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;
    @ApiModelProperty(name = "avatarUrl", value = "头像")
    private String avatarUrl;

    @ApiModelProperty(name = "createDate", value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(name = "healthRequest", value = "健康诉求")
    private String healthRequest;

    @ApiModelProperty(name = "completeReport", value = "已完成报告")
    private String completeReport;

    @ApiModelProperty(name = "adviserUserIds", value = "顾问")
    private String adviserUserIds;

    @ApiModelProperty(name = "specialTime1", value = "特殊日期1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date specialTime1;

    @ApiModelProperty(name = "specialTime2", value = "特殊日期2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date specialTime2;

    @ApiModelProperty(name = "associatedMemberIds", value = "关联会员id列表")
    private String associatedMemberIds;

    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    @ApiModelProperty(name = "images", value = "图片列表")
    private String images;
    @ApiModelProperty(name = "phone", value = "手机号")
    private String phone;

    // 账号合并参数校验
    public void validateMerge() {
        if (id == null) {
            throw new RuntimeException("用户id不能为空");
        }
        if (margeId == null) {
            throw new RuntimeException("被合并用户id不能为空");
        }
    }

    public void validateLevel() {
        if (id == null) {
            throw new RuntimeException("用户id不能为空");
        }
        if (memberLevelId == null) {
            throw new RuntimeException("会员等级id不能为空");
        }
    }

}
