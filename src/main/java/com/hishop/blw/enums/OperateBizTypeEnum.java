package com.hishop.blw.enums;

import lombok.Getter;

/**
 * 操作日志类型枚举
 */
@Getter
public enum OperateBizTypeEnum {

    UPDATE_MEMBER(1, "修改会员"),
    ADD_MEMBER(2, "添加会员"),
    RESERVATION(3, "代客预约"),
    VERIFY_RESERVATION(3, "核销预约"),
    UPDATE_RESERVATION(4, "修改预约"),
    ORDER_SETTLEMENT(5, "订单结算")
    ;

    private final Integer code;

    private final String message;

    OperateBizTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static OperateBizTypeEnum codeToEnum(Integer code) {
        if (null != code) {
            for (OperateBizTypeEnum e : OperateBizTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }
}
