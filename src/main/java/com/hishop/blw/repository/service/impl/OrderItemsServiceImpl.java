package com.hishop.blw.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.blw.enums.OrderItemsFlagEnums;
import com.hishop.blw.model.vo.order.OrderItemsVO;
import com.hishop.blw.repository.entity.OrderItems;
import com.hishop.blw.repository.dao.OrderItemsMapper;
import com.hishop.blw.repository.service.OrderItemsService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**   
 * @Description: 订单子项表 服务实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Service
public class OrderItemsServiceImpl extends ServiceImpl<OrderItemsMapper, OrderItems> implements OrderItemsService  {

    @Resource
    private OrderItemsMapper orderItemsMapper;

    @Override
    public List<OrderItems> listEntity(OrderItems entity) {
     return null;
    }

    @Override
    public Page<OrderItems> pageList(Page<OrderItems> page, OrderItems entity) {
        //TODO，这里可以写自定义sql或者用基础selectPage方法分页查询数据
        return null;
    }

    @Override
    public List<OrderItems> listByOrderIds(List<Long> orderIds) {
        if(CollUtil.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItems> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItems::getOrderId, orderIds);
        queryWrapper.eq(OrderItems::getFlag, OrderItemsFlagEnums.NORMAL.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<OrderItems> listByOrderIdsAndDate(List<Long> orderIds, Date startTime, Date endTime, String productName, Long projectDepartmentId) {
        if(CollUtil.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        return orderItemsMapper.listByOrderIdsAndDate(orderIds,startTime,endTime,productName,projectDepartmentId);
    }

    @Override
    public OrderItems getOne(Long orderId, Long productId) {
        LambdaQueryWrapper<OrderItems> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItems::getOrderId,orderId);
        queryWrapper.eq(OrderItems::getProductId,productId);
        queryWrapper.eq(OrderItems::getFlag, OrderItemsFlagEnums.NORMAL.getCode());
        List<OrderItems> list = this.list(queryWrapper);
        return list != null ? list.get(0) : null;
    }

    @Override
    public List<OrderItems> listByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItems> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItems::getOrderId,orderId);
        return this.list(queryWrapper);
    }

    @Override
    public List<OrderItemsVO> getByOrderId(Long orderId) {
        return this.baseMapper.getByOrderId(orderId);
    }

    @Override
    public List<OrderItemsVO> listAllItemByOrderId(List<Long> orderIdList,String productName, Long projectDepartmentId) {
        return orderItemsMapper.listAllItemByOrderId(orderIdList,productName,projectDepartmentId);
    }

    @Override
    public List<OrderItemsVO> listById(Long id) {
        return this.baseMapper.listById(id);
    }

    @Override
    public OrderItems getById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public void updateAmountByOrderId(Long orderId, BigDecimal amount) {
        LambdaUpdateWrapper<OrderItems> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderItems::getOrderId, orderId)
                .set(OrderItems::getActualAmount, amount)
                .set(OrderItems::getActualPointsAmount, amount);
        this.update(updateWrapper);
    }


    @Override
    public boolean deleteByOrderAndProductId(Long orderId, Long orderItemId, Long productId) {
        LambdaUpdateWrapper<OrderItems> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderItems::getOrderId, orderId)
                .eq(OrderItems::getId, orderItemId)
                .eq(OrderItems::getProductId, productId);
        return this.remove(updateWrapper);
    }
}