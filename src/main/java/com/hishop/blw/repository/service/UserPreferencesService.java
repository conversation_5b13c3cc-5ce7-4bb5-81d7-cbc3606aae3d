package com.hishop.blw.repository.service;

import com.hishop.blw.repository.dto.UserPreferencesQueryDTO;
import com.hishop.blw.repository.entity.UserPreferences;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 用户偏好表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface UserPreferencesService extends IService<UserPreferences> {

    List<UserPreferences> listEntity(UserPreferences entity);

    Page<UserPreferences> pageList(Page<UserPreferences> page, UserPreferencesQueryDTO entity);

}