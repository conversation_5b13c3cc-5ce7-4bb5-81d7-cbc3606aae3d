package com.hishop.blw.repository.service;

import com.hishop.blw.model.po.report.ManageReportQueryPO;
import com.hishop.blw.repository.entity.ProductCategories;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**
 * @Description: 商品分类表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface ProductCategoriesService extends IService<ProductCategories> {

    List<ProductCategories> listEntity(ProductCategories entity);

    Page<ProductCategories> pageList(Page<ProductCategories> page, ProductCategories entity);
    Page<ProductCategories> pageForReport(Page<ProductCategories> page, ManageReportQueryPO manageReportPO);

    List<ProductCategories> listByNames(List<String> names, Integer level);

    List<ProductCategories> listByIds(List<Long> ids);

    List<ProductCategories> listByParentId(Long parentId);

    Page<ProductCategories> listPage(Page<ProductCategories> page, Long storeId,Long departId);
}