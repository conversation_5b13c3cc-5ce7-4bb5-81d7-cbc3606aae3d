package com.hishop.blw.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.blw.model.po.report.CrossStoreConsumptionDetailQueryPO;
import com.hishop.blw.model.po.report.CrossStoreConsumptionQueryPO;
import com.hishop.blw.model.vo.order.CrossStoreConsumptionDetailVO;
import com.hishop.blw.model.vo.order.CrossStoreConsumptionVO;
import com.hishop.blw.repository.dto.UserDataPowerDTO;

/**
 * <AUTHOR>
 * @Date 2025/02/19/ $
 * @description:
 */
public interface CrossStoreConsumptionService {

    Page<CrossStoreConsumptionVO> pageList(Page<CrossStoreConsumptionVO> page, CrossStoreConsumptionQueryPO queryPO,
            UserDataPowerDTO powerDTO);

    Page<CrossStoreConsumptionDetailVO> detailPageList(Page<CrossStoreConsumptionDetailVO> page,
            CrossStoreConsumptionDetailQueryPO queryPO);
}
