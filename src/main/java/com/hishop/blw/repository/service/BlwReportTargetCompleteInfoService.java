package com.hishop.blw.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.blw.model.po.report.TargetCompletePo;
import com.hishop.blw.repository.dto.TargetCompleteDTO;
import com.hishop.blw.repository.dto.UserDataPowerDTO;
import com.hishop.blw.repository.entity.BlwReportTargetCompleteInfo;
import com.hishop.blw.repository.entity.TaskTarget;

import java.util.Date;

/**
 * @Description: 服务类
 * @Author: snow.xu
 * @since: 2025-02-07
 */

public interface BlwReportTargetCompleteInfoService extends IService<BlwReportTargetCompleteInfo> {

    /**
     * 根据年月删除进度报表
     *
     * @param belongDate 格式yyyy-MM
     */
    void deleteByBelongDate(String belongDate);

    /**
     * 根据年月生成进度报表
     *
     * @param yearMonth 格式yyyy-MM
     * @param year      格式yyyy
     */
    void generateReport(String yearMonth, String year);

    /**
     * 分页查询进度报表
     *
     * @param targetCompletePo
     * @param userPower
     * @return
     */
    Page<TargetCompleteDTO> pageListWithYear(TargetCompletePo targetCompletePo, UserDataPowerDTO userPower);

    /**
     * 分页查询进度报表
     *
     * @param targetCompletePo
     * @param userPower
     * @return
     */
    Page<TargetCompleteDTO> pageListWithMonth(TargetCompletePo targetCompletePo, UserDataPowerDTO userPower);

    void updateByYear(TaskTarget entity);
}