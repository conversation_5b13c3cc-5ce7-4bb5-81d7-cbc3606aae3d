package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.StoreServiceAreas;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**
 * @Description: 门店服务范围表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface StoreServiceAreasService extends IService<StoreServiceAreas> {

    List<StoreServiceAreas> listEntity(StoreServiceAreas entity);

    Page<StoreServiceAreas> pageList(Page<StoreServiceAreas> page, StoreServiceAreas entity);

    List<StoreServiceAreas> listByStoreIds(List<Long> storeIds);

    List<StoreServiceAreas> listByStoreAndDepartment(Long storeId, Long departmentId);

    void saveByStore(Long storeId, List<Long> departmentList, List<Long> riskDepartmentList);

    void deleteByStore(Long storeId);

}