package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.RefundReasons;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 退款理由表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface RefundReasonsService extends IService<RefundReasons> {

    List<RefundReasons> listEntity(RefundReasons entity);

    Page<RefundReasons> pageList(Page<RefundReasons> page, RefundReasons entity);

}