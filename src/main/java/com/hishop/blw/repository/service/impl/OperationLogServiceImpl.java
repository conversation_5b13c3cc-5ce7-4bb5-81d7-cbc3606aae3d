package com.hishop.blw.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.blw.converts.LogConverter;
import com.hishop.blw.repository.dto.OperationLogQueryDTO;
import com.hishop.blw.repository.entity.OperationLog;
import com.hishop.blw.repository.dao.OperationLogMapper;
import com.hishop.blw.repository.service.OperationLogService;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.util.LoginUserUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.net.InetAddress;
import java.util.List;

/**
 * @Description: 服务实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog>
        implements OperationLogService {

    @Override
    public List<OperationLog> listEntity(OperationLog entity) {
        return null;
    }

    @Override
    public Page<OperationLog> pageList(Page<OperationLog> page, OperationLogQueryDTO queryDTO) {
        // TODO，这里可以写自定义sql或者用基础selectPage方法分页查询数据
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        if (queryDTO.getBizId() != null) {
            queryWrapper.eq(OperationLog::getBizId, queryDTO.getBizId());
        }
        if(queryDTO.getBizType() != null) {
            queryWrapper.eq(OperationLog::getBizType, queryDTO.getBizType());
        }
        if (queryDTO.getCreateByIds() != null && queryDTO.getCreateByIds().size() > 0) {
            queryWrapper.in(OperationLog::getCreateBy, queryDTO.getCreateByIds());
        }
        if (queryDTO.getCreateDateStart() != null) {
            queryWrapper.ge(OperationLog::getCreateDate, queryDTO.getCreateDateStart());
            queryWrapper.le(OperationLog::getCreateDate, queryDTO.getCreateDateEnd());
        }
        queryWrapper.orderByDesc(OperationLog::getCreateDate);
        return this.page(page, queryWrapper);
    }

    public void saveOperationLog(Long id, Object entity, Object newEntity, String operateObject) {
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setBizId(id);
            operationLog.setOldContent(LogConverter.convert(entity));
            operationLog.setNewContent(LogConverter.convert(newEntity));
            operationLog.setOperateContent(LogConverter.compareAndConvert(entity, newEntity));
            operationLog.setOperateObject(operateObject);
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            operationLog.setCreateBy(loginUser.getUserId());
            InetAddress localHost = InetAddress.getLocalHost();
            operationLog.setIp(localHost.getHostAddress());
            this.save(operationLog);
        } catch (Exception e) {
            log.error("编辑会员保存日志失败！", e);
        }
    }

    @Override
    public void saveOperationLog(Long id, String operateContent, String operateObject) {
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setBizId(id);
            operationLog.setOperateObject(operateObject);
            operationLog.setOperateContent(operateContent);
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            operationLog.setCreateBy(loginUser.getUserId());
            InetAddress localHost = InetAddress.getLocalHost();
            operationLog.setIp(localHost.getHostAddress());
            this.save(operationLog);
        } catch (Exception e) {
            log.error("保存日志失败！", e);
        }
    }
}