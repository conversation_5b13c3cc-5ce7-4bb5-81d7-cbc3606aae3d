package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.MachinesUsage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 小票机使用范围表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface MachinesUsageService extends IService<MachinesUsage> {

    List<MachinesUsage> listEntity(MachinesUsage entity);

    Page<MachinesUsage> pageList(Page<MachinesUsage> page, MachinesUsage entity);


    void removeByMachineId(Long machineId);

    List<MachinesUsage> listByMachineId(Long machineId);


}