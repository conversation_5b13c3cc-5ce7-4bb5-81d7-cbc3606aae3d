package com.hishop.blw.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.blw.model.po.common.FollowUpRecordQueryPO;
import com.hishop.blw.repository.entity.FollowUpRecord;

import java.util.Date;
import java.util.List;

/**
 * 客户跟进记录(FollowUpRecord)表服务接口
 * <AUTHOR>
 * @since 2025-02-12 09:12:31
 */
public interface FollowUpRecordService extends IService<FollowUpRecord> {

    void saveFollowUpRecord(FollowUpRecord followUpRecord);

    Page<FollowUpRecord> followUpRecordPageList(FollowUpRecordQueryPO followUpRecordQueryPo);

    List<FollowUpRecord> getFollowUpRecordListByUserHomeId(Long userHomeId);

    List<FollowUpRecord> getLatestFollowUpRecordListByUserHomeIds(List<Long> userHomeIds);

    List<FollowUpRecord> searchByNextDate(Date start, Date end, Boolean izSms);

}

