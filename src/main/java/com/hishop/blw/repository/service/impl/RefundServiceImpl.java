package com.hishop.blw.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.blw.enums.RefundStatusEnums;
import com.hishop.blw.model.vo.refund.OrderItemRefundVO;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.Orders;
import com.hishop.blw.repository.entity.Refund;
import com.hishop.blw.repository.dao.RefundMapper;
import com.hishop.blw.repository.service.RefundService;
import com.hishop.blw.repository.service.UsersService;
import com.hishop.common.exception.BusinessException;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**   
 * @Description: 售后表 服务实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Service
public class RefundServiceImpl extends ServiceImpl<RefundMapper, Refund> implements RefundService  {

    @Resource
    private UsersService usersService;


    @Override
    public List<Refund> listEntity(Refund entity) {
     return null;
    }

    @Override
    public Page<RefundResultDTO> pageList(Page<Refund> page, RefundQueryDTO query) {
        UserDataPowerDTO userDataPowerDTO = usersService.getUserDataPower();
        return this.baseMapper.pageList(page, query, userDataPowerDTO);
    }

    @Override
    public List<Refund> listByOrderIds(List<Long> orderIds, Integer status) {
        LambdaQueryWrapper<Refund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Refund::getOrderId, orderIds);
        queryWrapper.eq(Objects.nonNull(status), Refund::getStatus, status);
        return this.list(queryWrapper);
    }

    @Override
    public RefundResultDTO getRefundDetailById(Long id) {
        return this.baseMapper.getRefundDetailById(id);
    }

    @Override
    public List<Map<Long, BigDecimal>> getStoreRefundAmount(List<Long> storeIds,Date handleStartTime, Date handleEndTime) {
        QueryWrapper<Refund> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("store_id",storeIds);
        queryWrapper.eq("status", RefundStatusEnums.PASSED.getCode());
        if(handleStartTime != null){
            queryWrapper.ge("handler_time",handleStartTime);
        }
        if(handleEndTime != null){
            queryWrapper.lt("handler_time",handleEndTime);
        }
        queryWrapper.select("store_id","sum(ifNull(refund_amount,0)) as refundAmount")
                .groupBy("store_id");

        List<Map<String, Object>> maps = baseMapper.selectMaps(queryWrapper);
        // 将结果转换为所需的类型
        List<Map<Long, BigDecimal>> collect = maps.stream().map(map -> {
            String storeIdKey = "store_id";
            String consumeAmountKey = "refundAmount";

            // 确保键存在
            if (!map.containsKey(storeIdKey) || !map.containsKey(consumeAmountKey)) {
                throw new BusinessException("500", "服务器异常");
            }

            Long storeId = ((Number) map.get(storeIdKey)).longValue();
            BigDecimal consumeAmount = new BigDecimal(map.get(consumeAmountKey).toString());

            // 使用 HashMap 创建新的 Map
            Map<Long, BigDecimal> result = new HashMap<>();
            result.put(storeId, consumeAmount);

            return result;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public boolean existsRefundByOrderItemId(Long orderItemId){
        Long count = this.baseMapper.countExistsRefundByOrderItemId(orderItemId);
        return count > 0;
    }

    @Override
    public List<Refund> listByUserId(Long userId) {
        LambdaQueryWrapper<Refund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Refund::getUserId, userId);
        return this.list(wrapper);
    }

    @Override
    public List<ItemRefundAmountDTO> getOrderItemRefundAmount(List<Long> orderItemIdList,Date startTime, Date endTime, String productName, Long projectDepartmentId){
        return this.baseMapper.getOrderItemRefundAmount(orderItemIdList, startTime, endTime, productName, projectDepartmentId);
    }

    @Override
    public List<ItemRefundCountDTO> getOrderItemRefundCount(List<Long> orderItemIds){
        return this.baseMapper.getOrderItemRefundCount(orderItemIds);
    }

    @Override
    public List<RefundQuantityDTO> listOrderItemRefundCount(List<Long> orderItemIds) {
        return this.baseMapper.listOrderItemRefundCount(orderItemIds);
    }

    @Override
    public boolean existsByOrderIdAndStatus(Long orderId, Integer status) {
        LambdaQueryWrapper<Refund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Refund::getOrderId, orderId);
        wrapper.eq(Refund::getStatus, status);
        return count(wrapper) > 0;
    }

    @Override
    public List<RefundDTO> listByOrderItemId(Long orderItemId) {
        return this.baseMapper.listByOrderItemId(orderItemId);
    }

    @Override
    public Refund first(Long orderId) {
        LambdaQueryWrapper<Refund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Refund::getOrderId, orderId);
        return this.getOne(wrapper);
    }

    @Override
    public OrderItemRefundVO getOrderItemRefundAmount2(Long orderItemId, Date startTime, Date endTime){
        return this.baseMapper.getOrderItemRefundAmount2(orderItemId, startTime, endTime);
    }

    @Override
    public List<RefundQuantityDTO> listByProductIds(List<Long> productIds, String month) {
        if(productIds == null || productIds.isEmpty()){
            return Collections.emptyList();
        }
        return this.baseMapper.listByProductIds(productIds, month);
    }
    @Override
    public List<RefundQuantityDTO> listOrderRefundQuantity(List<Long> productIds, String month) {
        if(productIds == null || productIds.isEmpty()){
            return Collections.emptyList();
        }
        return this.baseMapper.listOrderRefundQuantity(productIds, month);
    }

    @Override
    public List<RefundItemsRefundDateDTO> getOrderItemRefundDate(List<Long> orderIdList, Date startTime, Date endTime) {
        return this.baseMapper.getOrderItemRefundDate(orderIdList, startTime, endTime);
    }

}