package com.hishop.blw.repository.service;

import com.hishop.blw.model.vo.refund.OrderItemRefundVO;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.Refund;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**   
 * @Description: 售后表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface RefundService extends IService<Refund> {

    List<Refund> listEntity(Refund entity);

    Page<RefundResultDTO> pageList(Page<Refund> page, RefundQueryDTO query);

    List<Refund> listByOrderIds(List<Long> orderIds, Integer status);

    RefundResultDTO getRefundDetailById( Long id);

    /**
     * 获取门店订单成功售后金额
     * @param storeIds
     * @return Map<Long, BigDecimal> 门店售后金额
     */
    List<Map<Long, BigDecimal>> getStoreRefundAmount(List<Long> storeIds, Date handleStartTime, Date handleEndTime);

    boolean existsRefundByOrderItemId(Long orderItemId);

    List<Refund> listByUserId(Long userId);

    List<ItemRefundAmountDTO> getOrderItemRefundAmount(List<Long> orderItemIdList, Date startTime, Date endTime, String productName, Long projectDepartmentId);

    List<ItemRefundCountDTO> getOrderItemRefundCount(List<Long> orderItemIds);

    List<RefundQuantityDTO> listOrderItemRefundCount(List<Long> orderItemIds);


    boolean existsByOrderIdAndStatus(Long orderId, Integer status);

    List<RefundDTO> listByOrderItemId(Long orderItemId);

    Refund first(Long orderId);

    OrderItemRefundVO getOrderItemRefundAmount2(Long orderItemId, Date startTime, Date endTime);

    List<RefundQuantityDTO> listByProductIds(List<Long> productIds, String month);
    List<RefundQuantityDTO> listOrderRefundQuantity(List<Long> productIds, String month);

    List<RefundItemsRefundDateDTO> getOrderItemRefundDate(List<Long> orderIdList, Date startTime, Date endTime);
}