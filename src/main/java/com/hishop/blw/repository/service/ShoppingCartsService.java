package com.hishop.blw.repository.service;

import com.hishop.blw.model.po.order.ShoppingCartsQueryPO;
import com.hishop.blw.repository.entity.ShoppingCarts;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 购物车表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface ShoppingCartsService extends IService<ShoppingCarts> {

    List<ShoppingCarts> listByUserId(ShoppingCartsQueryPO queryPO);

    Page<ShoppingCarts> pageList(Page<ShoppingCarts> page, ShoppingCarts entity);

    ShoppingCarts getByProductIdAndUserId(Long productId, Long userId, Long orderUserId, Long departmentId);

    void updateQuantity(Long id, Integer quantity);

    List<ShoppingCarts> cartListByUserId(Long userId);
}