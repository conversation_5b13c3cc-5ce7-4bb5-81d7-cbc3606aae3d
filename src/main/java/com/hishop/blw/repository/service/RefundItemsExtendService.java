package com.hishop.blw.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.blw.model.vo.refund.OrderItemRefundVO;
import com.hishop.blw.repository.entity.RefundItemsExtend;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface RefundItemsExtendService extends IService<RefundItemsExtend> {
    void add(RefundItemsExtend refundItemsExtend);

    List<RefundItemsExtend> listByWhere(Long productId,Long orderItemId);

    BigDecimal getRefundAmount(Long orderItemId,Long productId);

    OrderItemRefundVO getRefundAmount2(Long orderItemId, Long productId, Date startTime, Date endTime);

    List<RefundItemsExtend> listByRefundItemId(Long refundItemId);
    List<RefundItemsExtend> listByRefundItemId(Long refundItemId, Boolean isGift);

}
