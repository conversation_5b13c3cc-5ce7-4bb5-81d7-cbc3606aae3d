package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.OrderComments;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 订单评论表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface OrderCommentsService extends IService<OrderComments> {

    List<OrderComments> listEntity(OrderComments entity);

    Page<OrderComments> pageList(Page<OrderComments> page, OrderComments entity);

}