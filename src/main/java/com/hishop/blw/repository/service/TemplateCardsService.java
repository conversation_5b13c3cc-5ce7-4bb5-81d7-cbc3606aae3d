package com.hishop.blw.repository.service;

import com.hishop.blw.enums.CardTypeEnums;
import com.hishop.blw.repository.entity.TemplateCards;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 用户模板卡表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface TemplateCardsService extends IService<TemplateCards> {

    List<TemplateCards> listEntity(TemplateCards entity);

    Page<TemplateCards> pageList(Page<TemplateCards> page, TemplateCards entity);

    TemplateCards getById(Long userId, Long cardId,Long storeId);

    List<TemplateCards> listShareByUserIds(List<Long> userIds);

    List<TemplateCards> listBy(List<Long> userIds, Long cardId, Long storeId, CardTypeEnums cardType);

    void updateCardTypeByCardId(Integer cardType, Long cardId);
}