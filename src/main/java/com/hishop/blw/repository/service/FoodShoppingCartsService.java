package com.hishop.blw.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.blw.model.po.order.ShoppingCartsFoodQueryPO;
import com.hishop.blw.repository.entity.FoodShoppingCarts;

import java.util.List;

/**   
 * @Description: 餐饮购物车表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface FoodShoppingCartsService extends IService<FoodShoppingCarts> {

    List<FoodShoppingCarts> listEntity(FoodShoppingCarts entity);

    Page<FoodShoppingCarts> pageList(Page<FoodShoppingCarts> page, FoodShoppingCarts entity);

    FoodShoppingCarts getByProductAndUser(Long userId, Long productId, Long boardId, Long storeId);

    void updateQuantity(Long id, Integer quantity);

    List<FoodShoppingCarts> listByUserId(ShoppingCartsFoodQueryPO queryPO);

}