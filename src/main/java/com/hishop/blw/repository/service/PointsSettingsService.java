package com.hishop.blw.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.blw.repository.entity.PointsSettings;

/**   
 * @Description: 积分设置表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface PointsSettingsService extends IService<PointsSettings> {

    Page<PointsSettings> pageList(Page<PointsSettings> page, Long storeId);

    PointsSettings getByStoreId(Long storeId);
}