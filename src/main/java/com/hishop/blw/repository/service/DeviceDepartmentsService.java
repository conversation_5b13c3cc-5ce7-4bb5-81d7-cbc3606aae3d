package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.DeviceDepartments;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 设备部门表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface DeviceDepartmentsService extends IService<DeviceDepartments> {

    List<DeviceDepartments> listEntity(DeviceDepartments entity);

    Page<DeviceDepartments> pageList(Page<DeviceDepartments> page, DeviceDepartments entity);

}