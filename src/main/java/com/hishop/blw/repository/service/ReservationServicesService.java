package com.hishop.blw.repository.service;

import com.hishop.blw.model.po.report.ManageReportQueryPO;
import com.hishop.blw.model.vo.report.ManagerOrderAmountVO;
import com.hishop.blw.model.vo.report.ManagerVerifyAmountVO;
import com.hishop.blw.repository.dto.ReservationServicesPageQueryDTO;
import com.hishop.blw.repository.dto.ReservationServicesPageResultDTO;
import com.hishop.blw.repository.entity.ReservationServices;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;

/**   
 * @Description: 预约服务表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface ReservationServicesService extends IService<ReservationServices> {

    List<ReservationServices> listEntity(ReservationServices entity);

    Page<ReservationServicesPageResultDTO> pageList(Page<ReservationServicesPageResultDTO> page, ReservationServicesPageQueryDTO entity);

    List<ReservationServices> listByUserId(Long userId);

    List<ReservationServices> getBoard(Date reservationDate, List<Long> userIds, Long storeId, Long departmentId);

    List<ReservationServices> listByUserServiceIds(List<Long> userServiceIds);

    boolean izExist(Long orderId);

    ReservationServices getById(Long id);

    //根据分类id查询核销金额（经营报表）
    List<ManagerVerifyAmountVO> listByCategoryIds(List<Long> categoryIds, ManageReportQueryPO manageReportPO);

    Long countByOrderItemId(Long orderItemId, Integer status);
}