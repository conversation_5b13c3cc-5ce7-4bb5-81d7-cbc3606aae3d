package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.CombineProducts;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.checkerframework.checker.units.qual.C;

import java.util.List;

/**   
 * @Description: 组合商品表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface CombineProductsService extends IService<CombineProducts> {

    List<CombineProducts> listByParentId(Long parentId);

    Page<CombineProducts> pageList(Page<CombineProducts> page, CombineProducts entity);

    void removeByProductId(Long productId);

    List<CombineProducts> listByRelateProductId(List<Long> relateProductIds);

    List<CombineProducts> listByProductId(Long productId);
    List<CombineProducts> listByProductIds(List<Long> productIds);

}