package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.OrderItemsExtend;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.aspectj.weaver.ast.Or;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**   
 * @Description: 订单子项扩展表(订单中有疗程商品用到) 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface OrderItemsExtendService extends IService<OrderItemsExtend> {

    List<OrderItemsExtend> listByOrderItemId(Long orderItemId);
    List<OrderItemsExtend> listByOrderItemIds(List<Long> orderItemIds, String productName, Long projectDepartmentId);
    Map<Long, BigDecimal> getListByOrderItemsId(List<Long> extendIds);
    void updateAmountByOrderId(Long orderId, BigDecimal amount);

    List<OrderItemsExtend> listByOrderId(Long orderId);
    List<OrderItemsExtend> listByOrderItemId(Long orderItemId, Boolean isGift);
}