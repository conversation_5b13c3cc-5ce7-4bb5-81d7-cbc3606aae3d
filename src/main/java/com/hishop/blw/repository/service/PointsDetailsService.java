package com.hishop.blw.repository.service;

import com.hishop.blw.repository.dto.PointsDetailsQueryDTO;
import com.hishop.blw.repository.entity.PointsDetails;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 积分明细表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface PointsDetailsService extends IService<PointsDetails> {

    List<PointsDetails> listEntity(PointsDetails entity);

    Page<PointsDetails> pageList(Page<PointsDetails> page, PointsDetailsQueryDTO queryDTO);

    List<PointsDetails> listByOrderId(Long orderId);

    List<PointsDetails> listByRefundId(Long refundId);

    List<PointsDetails> listByOrderIdAndType(Long orderId, Integer type);

}