package com.hishop.blw.repository.service;

import com.hishop.blw.repository.entity.CommonAttributes;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * @Description: 公共属性表 服务类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface CommonAttributesService extends IService<CommonAttributes> {

    List<CommonAttributes> listEntity(CommonAttributes entity);

    Page<CommonAttributes> pageList(Page<CommonAttributes> page, CommonAttributes entity);

    CommonAttributes getByName(String name);

    List<CommonAttributes> getByNames(List<String> names);

}