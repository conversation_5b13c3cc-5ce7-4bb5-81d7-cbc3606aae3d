package com.hishop.blw.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.blw.model.vo.refund.OrderItemRefundVO;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.Refund;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**   
 * 售后表 Mapper 接口
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface RefundMapper extends BaseMapper<Refund> {


    Page<RefundResultDTO> pageList(Page<Refund> page, @Param("query") RefundQueryDTO query, @Param("powerDTO") UserDataPowerDTO userDataPowerDTO);

    RefundResultDTO getRefundDetailById( Long id);

    Long countExistsRefundByOrderItemId(@Param("orderItemId") Long orderItemId);

    List<ItemRefundAmountDTO> getOrderItemRefundAmount(@Param("orderItemIdList") List<Long> orderItemIdList, @Param("startTime")Date startTime, @Param("endTime")Date endTime,
                                        @Param("productName") String productName, @Param("projectDepartmentId") Long projectDepartmentId);

    List<ItemRefundCountDTO> getOrderItemRefundCount(@Param("orderItemIds") List<Long> orderItemIds);

    List<RefundQuantityDTO> listOrderItemRefundCount(@Param("orderItemIds") List<Long> orderItemIds);

    List<RefundDTO> listByOrderItemId(@Param("orderItemId") Long orderItemId);

    OrderItemRefundVO getOrderItemRefundAmount2(@Param("orderItemId") Long orderItemId, @Param("startTime")Date startTime, @Param("endTime")Date endTime);

    List<RefundQuantityDTO> listByProductIds(@Param("productIds") List<Long> productIds, @Param("month") String month);
    List<RefundQuantityDTO> listOrderRefundQuantity(@Param("productIds") List<Long> productIds, @Param("month") String month);

    List<RefundItemsRefundDateDTO> getOrderItemRefundDate(@Param("orderIdList") List<Long> orderIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
