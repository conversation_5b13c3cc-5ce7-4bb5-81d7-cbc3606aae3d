package com.hishop.blw.biz;

import com.hishop.blw.model.po.refund.*;
import com.hishop.blw.model.vo.order.OrderContentVO;
import com.hishop.blw.model.vo.refund.*;
import com.hishop.blw.repository.dto.ItemRefundAmountDTO;
import com.hishop.blw.repository.dto.ItemRefundCountDTO;
import com.hishop.blw.repository.dto.RefundQuantityDTO;
import com.hishop.common.response.PageResult;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 售后表 业务逻辑接口
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

public interface RefundBiz {

    void create(RefundCreatePO refundCreatePO);

    void update(RefundUpdatePO refundUpdatePO);

    void deleteById(Long id);

    List<RefundPageVO> list(RefundQueryPO queryPO);

    PageResult<RefundPageVO> pageList(RefundQueryPO queryPO);

    RefundPageVO getById(Long id);

//    List<OrderContentVO> filterRefunded(List<OrderContentVO> reservationOrder);

    void pass(RefundPassPO po);

    void reject(RefundRejectPO po);

    void close(Long id);

    RefundDetailVO getDetail(Long id);

    List<ItemRefundAmountDTO> getOrderItemRefundAmount(List<Long> orderItemIdList, Date startTime, Date endTime, String productName, Long projectDepartmentId);

    List<ItemRefundCountDTO> getOrderItemRefundCount(List<Long> orderItemIds);

    List<RefundQuantityDTO> listOrderItemRefundCount(List<Long> orderItemIds);

    BigDecimal calculateRefundAmount(CalculateRefundAmountPO po);


    OrderItemRefundVO getOrderItemRefundAmount2(Long orderItemId, Date startTime, Date endTime);

    RefundPreCheckVO preCheck(Long orderItemId);

    List<RefundItemsExtendVO> listExtendByRefund(Long refundId);

    Map<Long,Date> getOrderItemRefundDateMap(List<Long> orderIdList, Date startTime, Date endTime);


}