package com.hishop.blw.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.blw.biz.*;
import com.hishop.blw.constants.BasicConstants;
import com.hishop.blw.converts.LogConverter;
import com.hishop.blw.enums.*;
import com.hishop.blw.excel.dealer.ImportAssist;
import com.hishop.blw.excel.dto.FileImportType;
import com.hishop.blw.excel.dto.UserImportDto;
import com.hishop.blw.excel.listener.UserReadListener;
import com.hishop.blw.model.po.reservation.ReservationCreatePagePO;
import com.hishop.blw.model.po.user.*;
import com.hishop.blw.model.po.login.MobileLoginPO;
import com.hishop.blw.model.po.login.PcLoginPO;
import com.hishop.blw.model.vo.card.MemberCardsVO;
import com.hishop.blw.model.vo.reservation.ReservationCreatePageVO;
import com.hishop.blw.model.vo.user.UsersVO;
import com.hishop.blw.model.vo.login.MobileLoginVO;
import com.hishop.blw.model.vo.login.PcLoginVO;
import com.hishop.blw.model.vo.user.UserCardVO;
import com.hishop.blw.model.vo.user.UserDetailLogisticsVO;
import com.hishop.blw.model.vo.user.UserDetailVO;
import com.hishop.blw.model.vo.user.UserOptionVO;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.*;
import com.hishop.blw.repository.service.*;
import com.hishop.common.excel.read.ExcelReadHelper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginResult;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 用户表 业务逻辑实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Slf4j
@Service
public class UsersBizImpl implements UsersBiz {

    @Resource
    private UsersService usersService;
    @Resource
    private UserStoresService userStoresService;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private RoleService roleService;
    @Resource
    private AreasService areasService;
    @Resource
    private ReservationServicesService reservationServicesService;
    @Resource
    private MemberCardsService memberCardsService;

    @Resource
    private MemberCardsBiz memberCardsBiz;
    @Resource
    private UserPointsService userPointsService;
    @Resource
    private ImportAssist importAssist;
    @Resource
    private StoreServiceAreasService storeServiceAreasService;
    @Resource
    private UserLevelsBiz userLevelsBiz;

    @Resource
    private StoreService storeService;

    @Resource
    private PointsDetailsService pointsDetailsService;
    @Resource
    private StoreBiz storeBiz;

    @Resource
    private CardRecordsService cardRecordsService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private UserLevelsService userLevelsService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private OrderItemsService orderItemsService;
    @Resource
    private UserServicesService userServicesService;

    @Resource
    private OperationLogService operationLogService;
    @Resource
    private CardsService cardsService;
    @Resource
    private TemplateCardsService templateCardsService;
    @Resource
    private SnowflakeGenerator snowflakeGenerator;
    @Resource
    private RefundService refundService;
    @Resource
    private UserHomeService userHomeService;
    @Resource
    private CardsStoreService cardsStoreService;
    @Override
    public PcLoginVO pcLogin(PcLoginPO pcLoginPO) {
        // 校验账号
        Users users = usersService.getByPhone(pcLoginPO.getPhone(), UsersTypeEnums.STAFF.getCode());
        this.validLoginUser(users);
        // 校验密码
        String password = MD5.create().digestHex(pcLoginPO.getPassword());
        if (!password.equals(users.getPassword())) {
            throw new BusinessException("密码错误！");
        }
        LoginUser loginUser = this.buildLoginUser(users);
        LoginResult loginResult = SessionUtil.login(loginUser, BasicConstants.EXPIRE_TIME);
        PcLoginVO pcLoginVO = BeanUtil.copyProperties(loginUser, PcLoginVO.class);
        pcLoginVO.setToken(loginResult.getToken());
        pcLoginVO.setRefreshToken(loginResult.getRefreshToken());
        if (BasicConstants.SUPER_ADMIN_ROLE_ID == users.getId()) {
            pcLoginVO.setIzSupAdmin(true);
        }
        if (users.getIdentity() != null && users.getIdentity() == 1) {
            pcLoginVO.setIdentity(true);
        } else {
            pcLoginVO.setIdentity(false);
        }
        return pcLoginVO;
    }

    @Override
    public MobileLoginVO mobileLogin(MobileLoginPO mobileLoginPO) {
        Users users = usersService.getByPhone(mobileLoginPO.getPhone(), UsersTypeEnums.MEMBER.getCode());
        if (Objects.isNull(users)) {
            // 创建用户
            if (Objects.isNull(mobileLoginPO.getStoreId()) || mobileLoginPO.getStoreId() == 0) {
                // 从当前门店中随机一个店铺ID
                List<Store> stores = storeService.listStore();
                if (CollectionUtil.isNotEmpty(stores)) {
                    // 生成0到size-1的随机索引
                    int randomIndex = new Random().nextInt(stores.size());
                    Long storeId = stores.get(randomIndex).getId();
                    mobileLoginPO.setStoreId(storeId);
                } else {
                    mobileLoginPO.setStoreId(1L);
                }
            }
            UsersCreatePO usersCreatePO = new UsersCreatePO();
            usersCreatePO.setPhone(mobileLoginPO.getPhone());
            usersCreatePO.setPassword(mobileLoginPO.getPassword());
            usersCreatePO.setNickname(mobileLoginPO.getPhone());
            usersCreatePO.setName(mobileLoginPO.getPhone());
            List<Long> storeIds = new ArrayList<>();
            storeIds.add(mobileLoginPO.getStoreId());
            usersCreatePO.setStoreIds(storeIds);
            usersCreatePO.setBirthday(new Date());
            // 会员默认等级，永远是第一个
            // UserLevels userLevels = userLevelsService.defaultLevel();
            Long memberLevelId = 1L;
            usersCreatePO.setMemberLevelId(memberLevelId);
            createMember(usersCreatePO);
            users = usersService.getByPhone(mobileLoginPO.getPhone(), UsersTypeEnums.MEMBER.getCode());
        }
        // 验证实现登录
        this.validLoginUser(users);
        // 校验密码
        String password = MD5.create().digestHex(mobileLoginPO.getPassword());
        if (!password.equals(users.getPassword())) {
            throw new BusinessException("密码错误！");
        }
        LoginUser loginUser = this.buildLoginUser(users);
        LoginResult loginResult = SessionUtil.login(loginUser, BasicConstants.EXPIRE_TIME);
        MobileLoginVO pcLoginVO = BeanUtil.copyProperties(loginUser, MobileLoginVO.class);
        pcLoginVO.setToken(loginResult.getToken());
        pcLoginVO.setRefreshToken(loginResult.getRefreshToken());
        return pcLoginVO;
    }

    // 校验同一个手机号和用户类型的用户是否存在
    private void validExistUser(Users entity) {
        List<String> phones = new ArrayList<>();
        phones.add(entity.getPhone());
        List<Users> existUsers = usersService.listByPhones(phones, entity.getId(), entity.getUserType());
        if (CollectionUtil.isNotEmpty(existUsers)) {
            throw new BusinessException("该手机号已存在！");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createMember(UsersCreatePO usersCreatePO) {
        log.info("新增会员入参:{}", usersCreatePO);
        // 会员参数校验
        usersCreatePO.validateMember();
        Users entity = BeanUtil.copyProperties(usersCreatePO, Users.class);
        entity.setUserType(UsersTypeEnums.MEMBER.getCode());
        // 校验同一个手机号和用户类型的用户是否存在
        this.validExistUser(entity);
        // 处理会员顾问，谁添加谁就是顾问，格式是,1,2,
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            if (StrUtil.isNotBlank(entity.getAdviserUserIds())) {
//                if (!entity.getAdviserUserIds().contains("," + loginUser.getUserId() + ",")) {
//                    entity.setAdviserUserIds(entity.getAdviserUserIds() + loginUser.getUserId() + ",");
//                }
                entity.setAdviserUserIds(","+entity.getAdviserUserIds() + ",");
            } else {
                entity.setAdviserUserIds("," + loginUser.getUserId() + ",");
            }
        }

        if (StrUtil.isBlank(entity.getPassword())){
            entity.setPassword("Db123@");
        }
        if(StrUtil.isBlank(entity.getNickname())){
            entity.setNickname(entity.getPhone());
        }
        if(StrUtil.isBlank(entity.getName())){
            entity.setName(entity.getPhone());
        }

        if (StrUtil.isNotBlank(entity.getPassword())) {
            String password = MD5.create().digestHex(entity.getPassword());
            entity.setPassword(password);
        }
        usersService.save(entity);
        usersCreatePO.setId(entity.getId());
        // 保存门店会员信息
        UserStores userStores = new UserStores();
        userStores.setUserId(entity.getId());
        userStores.setStoreId(usersCreatePO.getStoreIds().get(0));
        userStoresService.save(userStores);
        // 默认给会员创建储值卡和赠送卡
        // List<Cards> cardsList = cardsService.listByStoreId(null);
        // if (CollectionUtil.isEmpty(cardsList)) {
        // return;
        // }
        // cardsList = cardsList.stream().filter(card -> {
        // return CardTypeEnums.STORE_CARD.getCode().equals(card.getCardType())
        // || CardTypeEnums.GIFT_CARD.getCode().equals(card.getCardType());
        // }).collect(Collectors.toList());
        // if (CollectionUtil.isEmpty(cardsList)) {
        // return;
        // }
        // 新增会员卡信息

        createUsersStoreCards(entity.getId());

        // for (Cards cards : cardsList) {
        // TemplateCards templateCards = new TemplateCards();
        // templateCards.setCardId(cards.getId());
        // templateCards.setCardType(cards.getCardType());
        // templateCards.setStoreId(usersCreatePO.getStoreIds().get(0));
        // templateCards.setUserId(entity.getId());
        // templateCardsService.save(templateCards);
        // MemberCards memberCards = new MemberCards();
        // memberCards.setCardType(cards.getCardType());
        // memberCards.setStoreId(usersCreatePO.getStoreIds().get(0));
        // memberCards.setTemplateCardId(templateCards.getId());
        // memberCards.setUserId(entity.getId());
        // memberCards.setCardNumber(snowflakeGenerator.next().toString());
        // memberCardsService.save(memberCards);
        // }

        try {
            operationLogService.saveOperationLog(entity.getId(),
                    String.format("添加会员{%s}", entity.getPhone()),
                    "添加会员");
        } catch (Exception e) {
            log.error("添加会员保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMember(UsersUpdatePO usersUpdatePO) {
        log.info("编辑会员入参:{}", usersUpdatePO);
        Users entity = usersService.getById(usersUpdatePO.getId());
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        Users newEntity = BeanUtil.copyProperties(entity, Users.class);
        if(StrUtil.isNotBlank(usersUpdatePO.getPhone())){
            newEntity.setPhone(usersUpdatePO.getPhone());
        }else{
            usersUpdatePO.setPhone(entity.getPhone());
        }
        // 校验同一个手机号和用户类型的用户是否存在
        this.validExistUser(newEntity);
        CopyOptions copyOptions = CopyOptions.create().setIgnoreNullValue(true);
        BeanUtil.copyProperties(usersUpdatePO, newEntity, copyOptions);
        usersService.updateById(newEntity);
        // 会员归属门店
        if (CollectionUtil.isNotEmpty(usersUpdatePO.getStoreIds())) {
            Long newStoreId = usersUpdatePO.getStoreIds().get(0);
            List<UserStores> userStoresList = userStoresService.listByUserId(usersUpdatePO.getId());
            Long oldStoreId = userStoresList.get(0).getStoreId();
            if (!newStoreId.equals(oldStoreId)) {
                UserStores userStores = userStoresList.get(0);
                userStores.setStoreId(newStoreId);
                userStoresService.updateById(userStores);
            }
        }

        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setBizId(entity.getId());
            operationLog.setBizType(1);
            operationLog.setOperateContent(LogConverter.compareAndConvert(entity, newEntity));
            operationLog.setOldContent(LogConverter.convert(entity));
            operationLog.setNewContent(LogConverter.convert(newEntity));
            operationLog.setOperateObject("修改会员");
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            operationLog.setCreateBy(loginUser.getUserId());
            operationLogService.save(operationLog);
            // operationLogService.saveOperationLog(entity.getId(),entity,newEntity,"修改会员");
        } catch (Exception e) {
            log.error("编辑会员保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopMember(Long id) {
        log.info("停用会员入参:{}", id);
        Users entity = usersService.getById(id);
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        if (UsersStatusEnums.STOP.getCode().equals(entity.getStatus())) {
            throw new BusinessException("会员已停用，不能重复停用！");
        }
        if (UsersStatusEnums.LOGOUT.getCode().equals(entity.getStatus())
                || UsersStatusEnums.DELETE.getCode().equals(entity.getStatus())
                || UsersStatusEnums.MERGE.getCode().equals(entity.getStatus())) {
            throw new BusinessException("会员当前状态不支持停用！");
        }

        usersService.updateStatus(id, UsersStatusEnums.STOP.getCode());
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setBizId(id);
            operationLog.setBizType(1);
            operationLog.setOperateContent("停用会员:" + entity.getPhone());
            operationLog.setOperateObject("停用会员");
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            operationLog.setCreateBy(loginUser.getUserId());
            operationLogService.save(operationLog);
        } catch (Exception e) {
            log.error("停用会员保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void restoreMember(Long id) {
        log.info("恢复会员入参:{}", id);
        Users entity = usersService.getById(id);
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        if (UsersStatusEnums.DELETE.getCode().equals(entity.getStatus())
                || UsersStatusEnums.MERGE.getCode().equals(entity.getStatus())
                || UsersStatusEnums.LOGOUT.getCode().equals(entity.getStatus())) {
            throw new BusinessException("会员当前状态不支持恢复！");
        }
        Integer status = UsersStatusEnums.REGISTER.getCode();
        Boolean hasPayedOrder = ordersService.hasPayedOrder(id);
        if (hasPayedOrder) {
            status = UsersStatusEnums.CONSUMED.getCode();
        }

        usersService.updateStatus(id, status);
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setBizId(id);
            operationLog.setBizType(1);
            operationLog.setOperateContent("恢复会员:" + entity.getPhone());
            operationLog.setOperateObject("恢复会员");
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            operationLog.setCreateBy(loginUser.getUserId());
            operationLogService.save(operationLog);
        } catch (Exception e) {
            log.error("恢复会员保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createEmployee(UsersCreatePO usersCreatePO) {
        log.info("新增员工入参:{}", usersCreatePO);
        // 员工参数校验
        usersCreatePO.validateEmployee();

        // 只有新增员工时才需要判断密码
        if (StrUtil.isBlank(usersCreatePO.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        if (StrUtil.isBlank(usersCreatePO.getConfirmPassword())) {
            throw new RuntimeException("确认密码不能为空");
        }
        if (!usersCreatePO.getPassword().equals(usersCreatePO.getConfirmPassword())) {
            throw new RuntimeException("密码不一致");
        }
        Users entity = BeanUtil.copyProperties(usersCreatePO, Users.class);
        if (StrUtil.isBlank(entity.getName())) {
            entity.setName(usersCreatePO.getNickname());
        }
        entity.setUserType(UsersTypeEnums.STAFF.getCode());
        entity.setManager(usersCreatePO.getRoleLevel() == 1 ? 1 : 0);
        // 校验同一个手机号和用户类型的用户是否存在
        this.validExistUser(entity);
        String password = MD5.create().digestHex(entity.getPassword());
        entity.setPassword(password);
        usersService.save(entity);
        // 保存门店会员信息
        List<Long> storeIds = usersCreatePO.getStoreIds();
        if (CollectionUtil.isNotEmpty(storeIds)) {
            List<StoreServiceAreas> storeServiceAreasList = storeServiceAreasService.listByStoreIds(storeIds);
            Map<Long, List<Long>> storeServiceAreasMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(storeServiceAreasList)) {
                storeServiceAreasMap = storeServiceAreasList.stream()
                        .collect(Collectors.groupingBy(StoreServiceAreas::getStoreId,
                                Collectors.mapping(StoreServiceAreas::getDepartmentId, Collectors.toList())));
            }
            List<UserStores> userStoresList = new ArrayList<>();
            for (Long storeId : storeIds) {
                List<Long> serviceAreas = storeServiceAreasMap.get(storeId);
                if (CollectionUtil.isEmpty(serviceAreas)) {
                    UserStores userStores = new UserStores();
                    userStores.setUserId(entity.getId());
                    userStores.setStoreId(storeId);
                    userStoresList.add(userStores);
                    continue;
                }
                usersCreatePO.getDepartmentIds().forEach(departmentId -> {
                    if (!serviceAreas.contains(departmentId)) {
                        return;
                    }
                    UserStores userStores = new UserStores();
                    userStores.setUserId(entity.getId());
                    userStores.setStoreId(storeId);
                    userStores.setDepartmentId(departmentId);
                    userStoresList.add(userStores);
                });
            }
            if (CollectionUtil.isNotEmpty(userStoresList)) {
                userStoresService.saveBatch(userStoresList);
            }
        }

        // 保存角色
        UserRole userRole = new UserRole();
        userRole.setRoleId(usersCreatePO.getRoleId());
        userRole.setUserId(entity.getId());
        userRoleService.save(userRole);
    }

    /**
     * 创建会员在门店的储值卡
     * 
     * @param userId 会员ID
     */
    private void createUsersStoreCards(Long userId) {
        // 获取所有的门店卡信息
        List<CardsStore> list = cardsStoreService.list();
        if (CollectionUtil.isNotEmpty(list)) {
            for (CardsStore cardsStore : list) {
                Cards card = cardsService.getById(cardsStore.getCardId());
                if (!Objects.isNull(card) && card.getCardType() != CardTypeEnums.SHARE_CARD.getCode()) {
                    if (card.getStatus().intValue() == 1) {
                        TemplateCards templateCards = new TemplateCards();
                        templateCards.setCardId(cardsStore.getCardId());
                        templateCards.setCardType(card.getCardType());
                        templateCards.setStoreId(cardsStore.getStoreId());
                        templateCards.setUserId(userId);
                        templateCardsService.save(templateCards);
                        MemberCards memberCards = new MemberCards();
                        memberCards.setCardType(card.getCardType());
                        memberCards.setStoreId(cardsStore.getStoreId());
                        memberCards.setTemplateCardId(templateCards.getId());
                        memberCards.setUserId(userId);
                        memberCards.setCardNumber(snowflakeGenerator.next().toString());
                        memberCardsService.save(memberCards);
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEmployee(UsersCreatePO usersCreatePO) {
        log.info("编辑员工入参:{}", usersCreatePO);
        if (BasicConstants.PLAT_MANAGER_ID.equals(usersCreatePO.getId())) {
            throw new BusinessException("平台管理员不可删除！");
        }
        // 员工参数校验
        usersCreatePO.validateEmployee();
        Users entity = usersService.getById(usersCreatePO.getId());
        if (entity == null) {
            throw new BusinessException("数据不存在！");
        }
        // 校验同一个手机号和用户类型的用户是否存在
        this.validExistUser(entity);
        HishopBeanUtil.copyProperties(usersCreatePO, entity);
        if (StrUtil.isBlank(entity.getName())) {
            entity.setName(usersCreatePO.getNickname());
        }
        entity.setManager(usersCreatePO.getRoleLevel() == 1 ? 1 : 0);
        usersService.updateById(entity);
        // 删除门店会员信息
        userStoresService.deleteByUserId(entity.getId());
        // 保存门店会员信息
        List<Long> storeIds = usersCreatePO.getStoreIds();
        if (CollectionUtil.isNotEmpty(storeIds)) {
            List<StoreServiceAreas> storeServiceAreasList = storeServiceAreasService.listByStoreIds(storeIds);
            Map<Long, List<Long>> storeServiceAreasMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(storeServiceAreasList)) {
                storeServiceAreasMap = storeServiceAreasList.stream()
                        .collect(Collectors.groupingBy(StoreServiceAreas::getStoreId,
                                Collectors.mapping(StoreServiceAreas::getDepartmentId, Collectors.toList())));
            }
            List<UserStores> userStoresList = new ArrayList<>();
            for (Long storeId : storeIds) {
                List<Long> serviceAreas = storeServiceAreasMap.get(storeId);
                if (CollectionUtil.isEmpty(serviceAreas)) {
                    UserStores userStores = new UserStores();
                    userStores.setUserId(entity.getId());
                    userStores.setStoreId(storeId);
                    userStoresList.add(userStores);
                    continue;
                }
                usersCreatePO.getDepartmentIds().forEach(departmentId -> {
                    if (!serviceAreas.contains(departmentId)) {
                        return;
                    }
                    UserStores userStores = new UserStores();
                    userStores.setUserId(entity.getId());
                    userStores.setStoreId(storeId);
                    userStores.setDepartmentId(departmentId);
                    userStoresList.add(userStores);
                });
            }
            if (CollectionUtil.isNotEmpty(userStoresList)) {
                userStoresService.saveBatch(userStoresList);
            }
        }
        // 删除角色关联
        userRoleService.removeByUserId(entity.getId());
        // 重新保存角色
        UserRole userRole = new UserRole();
        userRole.setRoleId(usersCreatePO.getRoleId());
        userRole.setUserId(entity.getId());
        userRoleService.save(userRole);

        try {
            operationLogService.saveOperationLog(usersCreatePO.getId(),
                    BeanUtil.copyProperties(entity, UsersCreatePO.class), usersCreatePO,
                    "账号管理编辑");
        } catch (Exception e) {
            log.error("账号管理编辑保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void mergeMember(UserMargePO usersMergePO) {
        log.info("合并账号入参：{}", usersMergePO);
        usersMergePO.validateMerge();
        for (Long margeId : usersMergePO.getMargeIds()) {
            UsersVO margeUser = usersService.getDetailById(margeId);
            if (UsersStatusEnums.MERGE.getCode().equals(margeUser.getStatus())) {
                throw new BusinessException(margeUser.getPhone() + "会员已被合并，不能再被合并！");
            }
            // 存在使用的预约不可合并
            List<ReservationServices> reservationList = reservationServicesService.listByUserId(margeId);
            if (CollectionUtil.isNotEmpty(reservationList)) {
                reservationList.forEach(reservation -> {
                    // 如果是待完成状态，不能合并
                    if (ReservationServicesEnums.Status.PENDING.getCode().equals(reservation.getStatus())) {
                        throw new BusinessException(margeUser.getPhone() + "会员，存在未完成的预约不可合并！");
                    }
                });
            }
            // 存在待结算订单
            List<Orders> ordersList = ordersService.listByUserId(margeId);
            if (CollectionUtil.isNotEmpty(ordersList)) {
                ordersList.forEach(orders -> {
                    // 存在待结算订单，都不能合并
                    if (OrderStatusEnums.UN_PAY.getCode().equals(orders.getOrderStatus())) {
                        throw new BusinessException(margeUser.getPhone() + "会员，存在待结算订单，不能合并！");
                    }
                });
            }
            // 存在进行中售后单
            List<Refund> refundList = refundService.listByUserId(margeId);
            if (CollectionUtil.isNotEmpty(refundList)) {
                refundList.forEach(refund -> {
                    // 存在进行中售后单，都不能合并
                    if (RefundStatusEnums.PENDING_APPROVAL.getCode().equals(refund.getStatus())) {
                        throw new BusinessException(margeUser.getPhone() + "会员，存在进行中售后单，不能合并！");
                    }
                });
            }
            // 1、将被合并的账号状态修改为"被合并"
            usersService.updateStatus(margeId, UsersStatusEnums.MERGE.getCode());
            // 2、将被合并账号积分迁移，相同门店的迁移过去，不同门店的不处理
            this.margeUserPoints(usersMergePO.getUserId(), margeId);
            // 3、储值卡金额合并，相同卡卡迁移过去，不同卡卡的不处理
            this.margeUserCard(usersMergePO.getUserId(), margeId);
            // 4、没使用的服务迁移过去
            this.margeUserServices(usersMergePO.getUserId(), margeId);
            try {
                operationLogService.saveOperationLog(usersMergePO.getUserId(),
                        String.format("会员{%s}被合并到{%s}", usersService.getDetailById(margeId).getPhone(),
                                usersService.getDetailById(usersMergePO.getUserId()).getPhone()),
                        "合并会员");
            } catch (Exception e) {
                log.error("合并会员保存日志失败！", e);
            }
        }
    }

    private void margeUserPoints(Long userId, Long margeId) {
        List<UserPoints> userPointsList = userPointsService.listByUserIds(Arrays.asList(margeId, userId));
        if (CollectionUtil.isEmpty(userPointsList)) {
            return;
        }
        // 分组处理用户积分记录
        Map<Long, List<UserPoints>> pointMap = userPointsList.stream()
                .collect(Collectors.groupingBy(UserPoints::getUserId));
        // 被合并账号积分列表
        List<UserPoints> margePointsList = pointMap.get(margeId);
        if (CollectionUtil.isEmpty(margePointsList)) {
            return;
        }
        // 合并至账号积分列表
        List<UserPoints> toPointsList = pointMap.get(userId);
        Map<Long, UserPoints> toPointsListMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(toPointsList)){
            toPointsListMap = toPointsList.stream().collect(Collectors.toMap(UserPoints::getStoreId, Function.identity()));
        }
        for (UserPoints margePoints : margePointsList){
            if(margePoints.getAvailablePoints() == null || margePoints.getAvailablePoints() == 0){
                continue;
            }
            UserPoints userPoints = toPointsListMap.get(margePoints.getStoreId());
            if (userPoints == null) {
                this.createUserStorePoints(margePoints,userId);
                continue;
            }
            //被合并积分
            Long margeAvailablePoint = margePoints.getAvailablePoints();
            userPoints.setAvailablePoints(userPoints.getAvailablePoints() + margeAvailablePoint);
            userPointsService.updateById(userPoints);
            // 转赠
            PointsDetails pointsDetails = new PointsDetails();
            pointsDetails.setUserId(margePoints.getUserId());
            pointsDetails.setType(PointsTypeEnums.TRANSFERT.getCode());
            pointsDetails.setStoreId(margePoints.getStoreId());
            pointsDetails.setPoints(-margeAvailablePoint);
            pointsDetails.setRemarks("合并账号后转赠");

            PointsDetails getPointsDetails = new PointsDetails();
            getPointsDetails.setUserId(userPoints.getUserId());
            getPointsDetails.setType(PointsTypeEnums.TRANSFER_GET.getCode());
            getPointsDetails.setStoreId(userPoints.getStoreId());
            getPointsDetails.setPoints(margeAvailablePoint);
            getPointsDetails.setRemarks("合并账号后获赠");
            pointsDetailsService.saveBatch(Arrays.asList(pointsDetails, getPointsDetails));
            //最后在更新
            margePoints.setAvailablePoints(0L);
            userPointsService.updateById(margePoints);
        }
    }

    private void createUserStorePoints(UserPoints userPoints,Long userId){
        //被合并的用户
        Long mergedUserId = userPoints.getUserId();
        //可用积分
        Long availablePoints = userPoints.getAvailablePoints();
        // 创建积分店铺账户
        UserPoints newUserPoints = new UserPoints();
        newUserPoints.setUserId(userId);
        newUserPoints.setStoreId(userPoints.getStoreId());
        newUserPoints.setAvailablePoints(availablePoints);
        userPointsService.save(newUserPoints);
        userPointsService.clearById(userPoints.getId());
        // 处理积分记录
        // 转赠
        PointsDetails pointsDetails = new PointsDetails();
        pointsDetails.setUserId(mergedUserId);
        pointsDetails.setType(PointsTypeEnums.TRANSFERT.getCode());
        pointsDetails.setStoreId(userPoints.getStoreId());
        pointsDetails.setPoints(-availablePoints);
        pointsDetails.setRemarks("合并账号后转赠");

        PointsDetails getPointsDetails = new PointsDetails();
        getPointsDetails.setUserId(newUserPoints.getUserId());
        getPointsDetails.setType(PointsTypeEnums.TRANSFER_GET.getCode());
        getPointsDetails.setStoreId(newUserPoints.getStoreId());
        getPointsDetails.setPoints(availablePoints);
        getPointsDetails.setRemarks("合并账号后获赠");
        pointsDetailsService.saveBatch(Arrays.asList(pointsDetails, getPointsDetails));
    }

    private void margeUserCard(Long userId, Long margeId) {
        // 共享卡不做合并处理
        List<UserCardDTO> userCardList = memberCardsService.listByUserIdsGroupStore(Arrays.asList(margeId, userId))
                .stream()
                .filter(userCard -> {
                    return userCard.getCardType() != CardTypeEnums.SHARE_CARD.getCode();
                }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(userCardList)) {
            return;
        }
        Map<Long, List<UserCardDTO>> cardMap = userCardList.stream()
                .collect(Collectors.groupingBy(UserCardDTO::getUserId));
        List<UserCardDTO> margeCardsList = cardMap.get(margeId).stream().filter(userCard -> {
            return userCard.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());
        List<UserCardDTO> toCardsList = cardMap.get(userId);
        if (CollectionUtil.isEmpty(margeCardsList) || CollectionUtil.isEmpty(toCardsList)) {
            return;
        }
        Map<Long, List<UserCardDTO>> toCardMap = toCardsList.stream()
                .collect(Collectors.groupingBy(UserCardDTO::getCardId));
        for (UserCardDTO margeCards : margeCardsList) {
            List<UserCardDTO> toCards = toCardMap.get(margeCards.getCardId());
            if (CollectionUtil.isEmpty(toCards)) {
                continue;
            }
            UserCardDTO toCard = toCards.get(0);
            for (UserCardDTO temp : toCards) {
                // 如果有同店的，优先同店，没有同店的，随便选一个店
                if (temp.getStoreId().equals(margeCards.getStoreId())) {
                    toCard = temp;
                    break;
                }
            }
            memberCardsService.addAmount(toCard.getId(), margeCards.getAvailableAmount());
            memberCardsService.subAmount(margeCards.getId(), margeCards.getAvailableAmount());

            // 转赠记录
            CardRecords records = new CardRecords();
            records.setUserId(margeId);
            records.setMemberCardId(margeCards.getId());
            records.setCardNumber(margeCards.getCardNumber());
            records.setCardType(margeCards.getCardType());
            records.setType(CardRecordsTypeEnums.TRANSFER.getCode());
            records.setAvailableAmount(margeCards.getAvailableAmount().subtract(margeCards.getAvailableAmount()));
            records.setFlag(CardRecordsFlagEnum.RETURN_CARD.getCode());
            records.setAmount(BigDecimal.ZERO.subtract(margeCards.getAvailableAmount()));
            records.setStoreId(margeCards.getStoreId());
            records.setRemarks("账号被合并后转赠");

            // 获赠记录
            CardRecords getRecords = new CardRecords();
            getRecords.setUserId(userId);
            getRecords.setMemberCardId(toCard.getId());
            getRecords.setCardNumber(toCard.getCardNumber());
            getRecords.setCardType(toCard.getCardType());
            getRecords.setType(CardRecordsTypeEnums.TRANSFER_GET.getCode());
            getRecords.setAvailableAmount(toCard.getAvailableAmount().add(margeCards.getAvailableAmount()));
            getRecords.setFlag(CardRecordsFlagEnum.CONTINUED_CHARGING.getCode());
            getRecords.setAmount(margeCards.getAvailableAmount());
            getRecords.setStoreId(toCard.getStoreId());
            getRecords.setRemarks("合并账号后获赠");

            cardRecordsService.saveBatch(Arrays.asList(records, getRecords));
        }
    }

    private void margeUserServices(Long userId, Long margeId) {
        List<UserServices> userServicesList = userServicesService.listByUserId(margeId);
        if (CollectionUtil.isEmpty(userServicesList)) {
            return;
        }
        userServicesList = userServicesList.stream().filter(userServices -> {
            return userServices.getAvailableQuantity().compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userServicesList)) {
            return;
        }
        for (UserServices userServices : userServicesList) {
            //如果之前的服务从来没用过，直接将用户id改为新的用户id
            if (userServices.getUsedServiceAmount().compareTo(BigDecimal.ZERO) == 0 && userServices.getUsedQuantity().compareTo(BigDecimal.ZERO) == 0) {
                userServices.setUserId(userId);
                userServicesService.updateById(userServices);
            }else {
                BigDecimal availableQuantity = userServices.getAvailableQuantity();
                BigDecimal newServiceTotalAmount = userServices.getServiceTotalAmount()
                        .subtract(userServices.getUsedServiceAmount());
                // 原服务可用数量改为0，服务总金额需要更新
                userServices.setServiceTotalAmount(userServices.getUsedServiceAmount());
                userServices.setAvailableQuantity(BigDecimal.ZERO);
                userServicesService.updateById(userServices);
                // 新增新服务
                userServices.setId(null);
                userServices.setUserId(userId);
                userServices.setAvailableQuantity(availableQuantity);
                userServices.setServiceTotalAmount(newServiceTotalAmount);
                userServicesService.save(userServices);
            }
        }
    }

    @Override
    public void deleteById(Long id) {
        if (BasicConstants.PLAT_MANAGER_ID.equals(id)) {
            throw new BusinessException("平台管理员不可删除！");
        }
        usersService.updateDelete(id);

        try {
            operationLogService.saveOperationLog(id,
                    String.format("账号管理删除：{%s}", id),
                    "账号管理删除");
        } catch (Exception e) {
            log.error("账号管理删除保存日志失败！", e);
        }
    }

    @Override
    public List<UsersVO> listByDepartmentId(UserStoresQueryPO queryPO) {
        List<Users> list = usersService.listByDepartmentId(queryPO.getDepartmentId(), queryPO.getUserName());
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public PageResult<UsersVO> pageList(UsersQueryPO queryPO) {
        // 查询用户列表
        UsersQueryDTO queryDTO = BeanUtil.copyProperties(queryPO, UsersQueryDTO.class);
        // 顾问所属角色
        if (queryPO.getAdviserUserRoleId() != null) {
            List<UserRole> userRoleList = userRoleService.listByRoleId(queryPO.getAdviserUserRoleId());
            List<Long> roleUserIds = userRoleList.stream().map(UserRole::getUserId).distinct()
                    .collect(Collectors.toList());
            queryDTO.setAdviserUserIds(roleUserIds);
        }

        Page<UsersResultDTO> result = usersService.pageList(queryPO.buildPage(), queryDTO);
        if (CollectionUtil.isEmpty(result.getRecords())) {
            return PageResultHelper.transfer(result, UsersVO.class);
        }

        // 获取用户ID列表
        List<Long> userIdList = result.getRecords().stream()
                .map(UsersResultDTO::getId)
                .collect(Collectors.toList());

        // 查询用户门店关系
        List<UserStores> userStoresList = userStoresService.listByUserIds(userIdList);
        if (CollectionUtil.isEmpty(userStoresList)) {
            return PageResultHelper.transfer(result, UsersVO.class);
        }

        // 查询门店和部门信息
        List<Long> storeIdList = userStoresList.stream()
                .map(UserStores::getStoreId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> departmentIdList = userStoresList.stream()
                .map(UserStores::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
        List<Store> storeList = storeService.listByIds(storeIdList);
        List<Department> departmentList = departmentService.listByIds(departmentIdList);

        // 组装数据
        Map<Long, List<UserStores>> userStoresMap = userStoresList.stream()
                .collect(Collectors.groupingBy(UserStores::getUserId));

        // 查询住所信息
        List<UserHome> userHomeList = userHomeService.listByUserIds(userIdList);

        for (UsersResultDTO item : result.getRecords()) {
            List<UserStores> userStores = userStoresMap.get(item.getId());
            if (CollectionUtil.isEmpty(userStores)) {
                continue;
            }

            // 设置门店和部门ID
            List<Long> storeIds = userStores.stream()
                    .map(UserStores::getStoreId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Long> departmentIds = userStores.stream()
                    .map(UserStores::getDepartmentId)
                    .distinct()
                    .collect(Collectors.toList());
            item.setStoreIds(storeIds);
            item.setDepartmentIds(departmentIds);

            // 设置门店和部门名称
            String departmentNames = departmentList.stream()
                    .filter(d -> departmentIds.contains(d.getId()))
                    .map(Department::getDepartmentName)
                    .collect(Collectors.joining(","));
            String storeNames = storeList.stream()
                    .filter(s -> storeIds.contains(s.getId()))
                    .map(Store::getStoreName)
                    .collect(Collectors.joining(","));
            item.setDepartmentNames(departmentNames);
            item.setStoreNames(storeNames);

            item.setContainUserHome(
                    userHomeList.stream().anyMatch(userHome -> userHome.getUserId().equals(item.getId())));

            // 会员类型设置区域信息
            if (UsersTypeEnums.MEMBER.getCode().equals(item.getUserType()) && !storeList.isEmpty()) {
                Store store = storeList.stream()
                        .filter(s -> storeIds.contains(s.getId()))
                        .findFirst()
                        .orElse(null);
                if (store != null) {
                    String fullName = areasService.getFullAreaName(store.getDistrictId());
                    item.setAreaName(fullName);
                }
            }
        }

        return PageResultHelper.transfer(result, UsersVO.class);
    }

    @Override
    public <T> void fullUserName(List<T> list, Function<T, Long> getUserId, BiConsumer<T, String> setUserName) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<Long> userIds = list.stream()
                .map(getUserId)
                .distinct()
                .collect(Collectors.toList());
        List<Users> userList = usersService.listByIds(userIds);
        Map<Long, Users> userMap = userList.stream()
                .collect(Collectors.toMap(Users::getId, Function.identity()));
        list.forEach(item -> {
            Users user = userMap.get(getUserId.apply(item));
            if (user != null) {
                if (StrUtil.isNotBlank(user.getName())) {
                    setUserName.accept(item, user.getName());
                } else {
                    setUserName.accept(item, user.getNickname());
                }
            }
        });
    }

    @Override
    public <T> void fullData(List<T> list, Function<T, Long> getUserId, BiConsumer<T, Users> setData) {
        List<Long> userIds = list.stream()
                .map(getUserId)
                .distinct()
                .collect(Collectors.toList());
        List<Users> userList = usersService.listByIds(userIds);
        Map<Long, Users> userMap = userList.stream()
                .collect(Collectors.toMap(Users::getId, Function.identity()));
        list.forEach(item -> {
            Users user = userMap.get(getUserId.apply(item));
            if (user != null) {
                setData.accept(item, user);
            }
        });
    }

    @Override
    public void importUsers(MultipartFile file) {
        ReadResult<UserImportDto> importResult = ExcelReadHelper.read(file, UserImportDto.class, new UserReadListener(),
                1);
        if (CollectionUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("模板导入数据为空，请检查导入文件");
        }
        importAssist.importData(FileImportType.USER_IMPORT, null, importResult);

        try {
            operationLogService.saveOperationLog(null,
                    "导入会员",
                    "导入会员");
        } catch (Exception e) {
            log.error("导入会员保存日志失败！", e);
        }
    }

    @Override
    public List<UsersVO> userList(Long storeId, String keyWords) {
        if (storeId == null) {
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            storeId = loginUser.getStoreId();
        }
        final Long finalStoreId = storeId;
        List<UsersResultDTO> usersResultDTOS = usersService.userList(finalStoreId, keyWords);
        List<UsersVO> result = BeanUtil.copyToList(usersResultDTOS, UsersVO.class);
        if (CollectionUtil.isNotEmpty(result)) {
            // 会员等级ID
            List<Long> memberLevelIds = result.stream().map(UsersVO::getMemberLevelId).distinct()
                    .collect(Collectors.toList());
            List<UserLevels> userLevels = userLevelsService.listByIds(memberLevelIds);
            Map<Long, UserLevels> userLevelsMap = userLevels.stream()
                    .collect(Collectors.toMap(UserLevels::getId, Function.identity()));
            // 会员ID
            List<Long> memberIds = result.stream().map(UsersVO::getId).collect(Collectors.toList());
            // 获取会员积分
            List<UserPoints> userPoints = userPointsService.listByUserIds(memberIds);
            // Map<Long, UserPoints> userPointsMap = userPoints.stream()
            // .collect(Collectors.toMap(UserPoints::getUserId, Function.identity()));
            // 获取会员可用余额
            List<UserCardDTO> userCardList = memberCardsService.listByUserIds(memberIds);
            Map<Long, UserCardDTO> userCardMap = userCardList.stream()
                    .collect(Collectors.toMap(UserCardDTO::getUserId, Function.identity(), (existing, replacement) -> {
                        BigDecimal existingAmount = existing.getAvailableAmount() != null
                                ? existing.getAvailableAmount()
                                : BigDecimal.ZERO;
                        BigDecimal replacementAmount = replacement.getAvailableAmount() != null
                                ? replacement.getAvailableAmount()
                                : BigDecimal.ZERO;
                        existing.setAvailableAmount(existingAmount.add(replacementAmount));
                        return existing;
                    }));

            result.forEach(item -> {
                UserLevels levels = userLevelsMap.get(item.getMemberLevelId());
                if (Objects.nonNull(levels)) {
                    item.setLevelName(levels.getLevelName());
                }
                UserPoints points = userPoints.stream()
                        .filter(p -> p.getUserId().equals(item.getId()) && p.getStoreId().equals(
                                finalStoreId))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(points)) {
                    item.setAvailablePoints(BigDecimal.valueOf(points.getAvailablePoints()));
                }
                UserCardDTO userCard = userCardMap.get(item.getId());
                if (Objects.nonNull(userCard)) {
                    item.setAvailableAmount(userCard.getAvailableAmount());
                }
            });

        }
        return result;
    }

    @Override
    public SelectUserDTO selectUser(Long userId) {
        return usersService.selectUser(userId);
    }

    @Override
    public void resetPassword(UsersCreatePO usersCreatePO) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        if (!loginUser.getUserId().equals(1) && usersCreatePO.getId().equals(1)) {
            throw new RuntimeException("超级管理员的密码只能由超级管理员本人重置");
        }
        Users users = usersService.getById(usersCreatePO.getId());
        if(users.getUserType() == UsersTypeEnums.STAFF.getCode() && loginUser.getUserId().intValue()!=1 && !(loginUser.getUserId().equals(usersCreatePO.getId()))) {
            throw new RuntimeException("只能重置本人的密码");
        }
        usersCreatePO.validateResetPassword();
        String password = MD5.create().digestHex(usersCreatePO.getPassword());
        usersService.resetPassword(usersCreatePO.getId(), password);
    }

    @Override
    public void changeStatus(UsersCreatePO usersCreatePO) {
        usersService.changeStatus(usersCreatePO.getId(), usersCreatePO.getStatus());
    }

    @Override
    public UserDetailVO getUserDetail(Long id) {
        Users users = usersService.getById(id);
        if (users == null) {
            throw new BusinessException("数据不存在！");
        }
        UserDetailVO userDetailVO = BeanUtil.copyProperties(users, UserDetailVO.class);
        // 获取会员等级名称
        if (users.getMemberLevelId() != null) {
            UserLevels userLevel = userLevelsService.getById(users.getMemberLevelId());
            if (userLevel != null) {
                userDetailVO.setMemberLevelName(userLevel.getLevelName());
            }
        }

        // 获取顾问名称
        if (StrUtil.isNotBlank(users.getAdviserUserIds())) {
            List<Long> adviserIds = Arrays.stream(users.getAdviserUserIds().split(","))
                    .filter(e -> StrUtil.isNotBlank(e))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            List<Users> adviserUsers = usersService.listByIds(adviserIds);
            if (CollectionUtil.isNotEmpty(adviserUsers)) {
                String adviserNames = adviserUsers.stream()
                        .map(user -> StrUtil.isNotBlank(user.getName()) ? user.getName() : user.getNickname())
                        .collect(Collectors.joining(","));
                userDetailVO.setAdviserUserNames(adviserNames);
            }
        }

        // 获取关联会员名称
        if (StrUtil.isNotBlank(users.getAssociatedMemberIds())) {
            List<Long> memberIds = Arrays.stream(users.getAssociatedMemberIds().split(","))
                    .filter(e -> StrUtil.isNotBlank(e))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(memberIds)) {
                List<Users> memberUsers = usersService.listByIds(memberIds);
                if (CollectionUtil.isNotEmpty(memberUsers)) {
                    String memberNames = memberUsers.stream()
                            .map(user -> user.getPhone())
                            .collect(Collectors.joining(","));
                    userDetailVO.setAssociatedMemberNames(memberNames);
                }
            }
        }

        // 获取区域名称和门店名称
        UserStores userStore = userStoresService.getByUserId(id);
        if (userStore != null) {

            Store store = storeService.getById(userStore.getStoreId());
            if (store != null) {
                // 设置门店名称
                userDetailVO.setStoreName(store.getStoreName());
                userDetailVO.setStoreId(store.getId());

                // 设置区域名称
                if (store.getDistrictId() != null) {
                    String fullName = areasService.getFullAreaName(store.getDistrictId());
                    userDetailVO.setAreaName(fullName);
                }
            }
        }

        // 获取用户积分
        Long userPoints = userPointsService.getUserPoints(id);
        userDetailVO.setUserPoints(userPoints);

        // 获取总余额
        BigDecimal totalAvailableAmount = memberCardsService.getMemberCardAmount(id);
        userDetailVO.setTotalAvailableAmount(totalAvailableAmount);

        return userDetailVO;
    }

    @Override
    public UsersVO getBriefUser(Long id) {
        if (id == null) {
            return null;
        }
        Users user = usersService.getById(id);
        return BeanUtil.copyProperties(user, UsersVO.class);
    }

    @Override
    public UserDetailLogisticsVO getUserDetailLogistics(Long id, Long storeId) {
        if (storeId == null && storeId.intValue() == 0L) {
            LoginUser loginUser = LoginUserUtil.getLoginUser();
            storeId = loginUser.getStoreId();
        }

        UserDetailLogisticsVO userDetailLogisticsVO = new UserDetailLogisticsVO();

        // 用户当前门店的积分
        UserPoints userPoints = userPointsService.getByStoreId(id, storeId);
        if (userPoints != null) {
            userDetailLogisticsVO.setUserPoints(userPoints.getAvailablePoints());
        }
        // 储值金额（储值卡、赠送卡、共享卡）
        List<MemberCards> memberCardsList = memberCardsService.listByUserId(id, null);
        if (CollectionUtil.isNotEmpty(memberCardsList)) {
            // 计算可用金额的总和
            BigDecimal amount = memberCardsList.stream()
                    .map(MemberCards::getAvailableAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            userDetailLogisticsVO.setAmount(amount);
            // 计算总充值金额
            BigDecimal rechargeAmount = memberCardsList.stream()
                    .map(MemberCards::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            userDetailLogisticsVO.setRechargeAmount(rechargeAmount);
        }
        // 总购买金额
        BigDecimal totalOrderAmount = BigDecimal.ZERO;
        // 总消耗次数 = 餐饮订单数量 + 非餐饮订单子项数量 - 未消耗的服务次数
        // long totalUsedCount = 0;
        // 消费订单，总购买金额
        List<Orders> orderList = ordersService.listByUserId(id);
        Integer payOrderCount = ordersService.consumerOrdersCount(id);
        if (CollectionUtil.isNotEmpty(orderList)) {
            // 已支付、已完成的订单数量
//            final Long finalStoreId = storeId;
//            long payOrderCount = orderList.stream()
//                    .filter(order -> order.getStoreId().equals(finalStoreId)
//                            && (OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
//                                    OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus())))
//                    .count();

            userDetailLogisticsVO.setConsumeOrderCount(payOrderCount);
            // 总购买金额 = 每条订单中实付金额-已退款金额
            totalOrderAmount = orderList.stream()
                    .filter(order -> OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
                            OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus()))
                    .map(order -> order.getActualPayAmount().subtract(order.getRefundTotalAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            userDetailLogisticsVO.setTotalOrderAmount(totalOrderAmount);
        }
        // 可用订单，总消耗金额，总消耗次数
        ConsumeReportQueryDTO consumeReportPO = new ConsumeReportQueryDTO();
        consumeReportPO.setUserId(id);
        ConsumerStatisticsDTO consumerStatisticsDTO = ordersService.consumerStatistics(consumeReportPO);
        if (Objects.nonNull(consumerStatisticsDTO)) {
            // 总消耗金额
            userDetailLogisticsVO.setTotalUsedAmount(consumerStatisticsDTO.getAmount());
            // 总消耗次数
            userDetailLogisticsVO.setTotalUsedCount(consumerStatisticsDTO.getQuantity());
        } else {
            userDetailLogisticsVO.setTotalUsedAmount(BigDecimal.ZERO);
            userDetailLogisticsVO.setTotalUsedCount(BigDecimal.ZERO);
        }
        // 最后消费时间和最后消费金额
        if (CollectionUtil.isNotEmpty(orderList)) {
            List<Orders> tempList = orderList.stream()
                    .filter(order -> OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
                            OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus()))
                    .sorted(Comparator.comparing(Orders::getPayDate).reversed()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tempList)) {
                userDetailLogisticsVO.setLastOrderTime(tempList.get(0).getPayDate());
                userDetailLogisticsVO.setLastOrderAmount(tempList.get(0).getActualPayAmount());
            }
        }
        return userDetailLogisticsVO;
    }

    @Override
    public UserDetailLogisticsVO getUserDetailLogisticsPC(Long id) {
        UserDetailLogisticsVO userDetailLogisticsVO = new UserDetailLogisticsVO();
        // 总购买金额
        BigDecimal totalOrderAmount = BigDecimal.ZERO;
        // 总消耗次数 = 餐饮订单数量 + 非餐饮订单子项数量 - 未消耗的服务次数
        // 消费订单，总购买金额
        List<Orders> orderList = ordersService.listByUserId(id);
        if (CollectionUtil.isNotEmpty(orderList)) {
            // 已支付、已完成的订单数量
            long payOrderCount = orderList.stream()
                    .filter(order -> (OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
                            OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus())))
                    .count();
            userDetailLogisticsVO.setConsumeOrderCount(payOrderCount);
            // 总购买金额 = 每条订单中实付金额-已退款金额
            totalOrderAmount = orderList.stream()
                    .filter(order -> OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
                            OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus()))
                    .map(order -> order.getActualPayAmount().subtract(order.getRefundTotalAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            userDetailLogisticsVO.setTotalOrderAmount(totalOrderAmount);
        }
        // 可用订单，总消耗金额，总消耗次数
        ConsumeReportQueryDTO consumeReportPO = new ConsumeReportQueryDTO();
        consumeReportPO.setUserId(id);
        ConsumerStatisticsDTO consumerStatisticsDTO = ordersService.consumerStatistics(consumeReportPO);
        if (Objects.nonNull(consumerStatisticsDTO)) {
            // 总消耗金额
            userDetailLogisticsVO.setTotalUsedAmount(consumerStatisticsDTO.getAmount());
            // 总消耗次数
            userDetailLogisticsVO.setTotalUsedCount(consumerStatisticsDTO.getQuantity());
        } else {
            userDetailLogisticsVO.setTotalUsedAmount(BigDecimal.ZERO);
            userDetailLogisticsVO.setTotalUsedCount(BigDecimal.ZERO);
        }
        // 最后消费时间和最后消费金额
        if (CollectionUtil.isNotEmpty(orderList)) {
            List<Orders> tempList = orderList.stream()
                    .filter(order -> OrderStatusEnums.PAY.getCode().equals(order.getOrderStatus()) ||
                            OrderStatusEnums.COMPLETE.getCode().equals(order.getOrderStatus()))
                    .sorted(Comparator.comparing(Orders::getPayDate).reversed()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tempList)) {
                userDetailLogisticsVO.setLastOrderTime(tempList.get(0).getPayDate());
                userDetailLogisticsVO.setLastOrderAmount(tempList.get(0).getActualPayAmount());
            }
        }
        return userDetailLogisticsVO;
    }

    @Override
    public List<UsersVO> listByStoreIdAndDepartmentId(Long storeId, Long departmentId) {
        List<Users> list = usersService.listByStoreIdAndDepartmentId(storeId, departmentId);
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public List<UsersVO> listByStoreIdAndDepartmentIdAndUserId(Long storeId, Long departmentId, Long userId) {
        List<Users> list = usersService.listByStoreIdAndDepartmentIdAndUserId(storeId, departmentId, userId);
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public List<UserOptionVO> listByKeyword(UsersKeywordPO po) {
        List<Users> usersList = usersService.listByKeyword(po);
        usersList = usersList.stream()
                .filter(user -> user.getStatus() != UsersStatusEnums.DELETE.getCode()
                        && user.getStatus() != UsersStatusEnums.STOP.getCode()
                        && user.getStatus() != UsersStatusEnums.MERGE.getCode()
                        && user.getStatus() != UsersStatusEnums.LOGOUT.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(usersList)) {
            return Collections.emptyList();
        }
        List<UserOptionVO> result = BeanUtil.copyToList(usersList, UserOptionVO.class);
        userLevelsBiz.fullName(result, UserOptionVO::getMemberLevelId, UserOptionVO::setMemberLevelName);
        // 填充顾问
        DataHandleUtils.convertIdsToValue(result,
                UserOptionVO::getAdviserUserIds,
                usersService::listByIds,
                Users::getId,
                (res, list) -> res
                        .setAdviserUserNameList(list.stream().map(Users::getName).collect(Collectors.toList())));
        // 填充会员卡
        List<Long> ids = result.stream().map(UserOptionVO::getId).collect(Collectors.toList());
        List<UserCardVO> list = memberCardsBiz.listByUserIds(ids);
        Map<Long, List<UserCardVO>> usercardMap = list.stream().collect(Collectors.groupingBy(UserCardVO::getUserId));
        result.forEach(r -> r.setCardList(usercardMap.get(r.getId())));
        return result;
    }

    @Override
    public PageResult<UserDetailVO> listUserByKeyword(UsersKeywordPO po) {
        // 验证当前请求来源
        if(po.getIzPlat()){
            // 来源于会员端，根据手机号码全匹配查询
            Page<Users> userList = usersService.listMemberByStaffId(po.buildPage(), po);
            PageResult<UserDetailVO> pageResult = PageResultHelper.transfer(userList, UserDetailVO.class);
            return pageResult;
        }
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        Users user = usersService.getById(loginUser.getUserId());
        if (user.getManager() == 1 || UsersIdentityEnums.MANAGER.getCode().equals(user.getIdentity())) {
            // 门店负责人
            po.setStoreId(loginUser.getStoreId());
            Page<Users> userList = usersService.listMemberByStoreId(po.buildPage(), po);
            PageResult<UserDetailVO> pageResult = PageResultHelper.transfer(userList, UserDetailVO.class);
            return pageResult;
        } else {
            // 普通员工
            po.setStaffId(loginUser.getUserId());
            Page<Users> userList = usersService.listMemberByStaffId(po.buildPage(), po);
            PageResult<UserDetailVO> pageResult = PageResultHelper.transfer(userList, UserDetailVO.class);
            return pageResult;
        }
    }

    /**
     * 查询会员的关联会员列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<UsersVO> listAssociatedMembers(Long userId) {
        Users users = usersService.getById(userId);
        if (StrUtil.isBlank(users.getAssociatedMemberIds())) {
            return Collections.emptyList();
        }
        String[] associatedMemberIds = users.getAssociatedMemberIds().split(",");
        List<Long> ids = Arrays.stream(associatedMemberIds).filter(StrUtil::isNotBlank).map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ids)) {
            List<Users> usersList = usersService.listByIds(ids);
            return BeanUtil.copyToList(usersList, UsersVO.class);
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAssociatedMember(UsersAssociatedPO cmdPO) {
        Users users = usersService.getById(cmdPO.getUserId());
        Users associatedMember = usersService.getById(cmdPO.getAssociatedMemberId());
        String memberId = "," + associatedMember.getId() + ",";
        if (StrUtil.isBlank(users.getAssociatedMemberIds())) {
            users.setAssociatedMemberIds(memberId);
        } else {
            if (users.getAssociatedMemberIds().contains(memberId)) {
                throw new BusinessException("会员已关联！");
            }
            users.setAssociatedMemberIds(users.getAssociatedMemberIds() + associatedMember.getId() + ",");
        }

        usersService.updateById(users);

        this.associationShareCards(cmdPO.getUserId(),cmdPO.getAssociatedMemberId());
    }

    /**
     * 关联贡献卡
     * @param userId
     * @param associatedId
     */
    private void associationShareCards(Long userId, Long associatedId) {
        // 获取平台下可用的共享卡
        List<Cards> cardList = cardsService.listByWhere(CardTypeEnums.SHARE_CARD, 1);
        if(CollectionUtil.isEmpty(cardList))
            return;
        for (Cards card : cardList){
            // 根据门店ID获取贡献卡使用范围的门店ID和卡ID
            List<CardsStoreDTO> cards = cardsService.listByCardId(card.getId());
            if(CollectionUtil.isEmpty(cards)){
                continue;
            }
            for (CardsStoreDTO cardsStoreDTO : cards) {
                Long storeId = cardsStoreDTO.getStoreId();



                List<Long> userIds = new ArrayList<>();
                userIds.add(userId);
                userIds.add(associatedId);
                List<TemplateCards> templateCards = templateCardsService.listBy(userIds, card.getId(), storeId, CardTypeEnums.SHARE_CARD);
                List<Long> templateCardsId = templateCards.stream().map(TemplateCards::getId).collect(Collectors.toList());
                List<MemberCards> memberCards = new ArrayList<>();
                if(templateCardsId != null && templateCardsId.size() > 0){
                    memberCards = memberCardsService.listByWhere(userIds, storeId,templateCardsId,CardTypeEnums.SHARE_CARD);
                }

                if (CollectionUtil.isEmpty(memberCards)) {
                    // 需要创建
                    createMemberStoreShareCard(cardsStoreDTO, userId);
                    // 给被关联的用户创建关联用户的共享卡
                    createAssociatedMemberStoreShareCard(userId, associatedId, cardsStoreDTO);
                } else if (memberCards.size() > 0) {
                    for (MemberCards memberCard : memberCards) {
                        if (Objects.isNull(memberCard)) {
                            return;
                        }
                        Long userId1 = userId;
                        Long relactionUserId = 0L;
                        if (memberCard.getUserId().intValue() == userId) {
                            userId = userId;
                            relactionUserId = associatedId;
                        } else {
                            userId = associatedId;
                            relactionUserId = userId;
                        }
                        createAssociatedMemberStoreShareCard(userId, relactionUserId, cardsStoreDTO);
                    }
                }
            }
        }
    }
    private void createAssociatedMemberStoreShareCard(Long userId, Long associatedId, CardsStoreDTO card) {
        if (userId == 0L || associatedId == 0L || Objects.isNull(card)) {
            return;
        }
        // 获取关联用户的共享卡
        List<MemberCards> memberCards = memberCardsService.listByUserId(userId, card.getStoreId());
        List<MemberCards> shareCards = memberCards.stream()
                .filter(item -> item.getCardType() == CardTypeEnums.SHARE_CARD.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(shareCards)) {
            shareCards.stream().forEach(item -> {
                item.setUserId(associatedId);
                item.setId(null);
                LambdaQueryWrapper<MemberCards> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberCards::getTemplateCardId, item.getTemplateCardId());
                queryWrapper.eq(MemberCards::getUserId, associatedId);
                List<MemberCards> list = memberCardsService.list(queryWrapper);
                if (CollectionUtil.isEmpty(list)) {
                    memberCardsService.save(item);
                }
            });
        }
    }

    /**
     * 给会员添加共享卡
     * 
     * @param card  共享卡列表
     * @param userId 会员id
     */
    private void createMemberStoreShareCard(CardsStoreDTO card, Long userId) {
        if (Objects.nonNull(card)) {
            // 验证会员是否拥有该卡
            boolean options = false;
            TemplateCards templateCards1 = templateCardsService.getById(userId, card.getId(), card.getStoreId());
            if (Objects.nonNull(templateCards1)) {
                LambdaQueryWrapper<MemberCards> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberCards::getTemplateCardId, templateCards1.getId());
                queryWrapper.eq(MemberCards::getUserId, userId);
                queryWrapper.eq(MemberCards::getStoreId, card.getStoreId());
                MemberCards one = memberCardsService.getOne(queryWrapper);
                if (Objects.isNull(one)) {
                    options = false;
                } else {
                    options = true;
                }
            }

            if (!options) {
                TemplateCards templateCards = new TemplateCards();
                templateCards.setCardId(card.getId());
                templateCards.setCardType(card.getCardType());
                templateCards.setStoreId(card.getStoreId());
                templateCards.setUserId(userId);
                templateCardsService.save(templateCards);

                MemberCards memberCards = new MemberCards();
                memberCards.setCardType(card.getCardType());
                memberCards.setStoreId(card.getStoreId());
                memberCards.setTemplateCardId(templateCards.getId());
                memberCards.setUserId(userId);
                memberCards.setCardNumber(snowflakeGenerator.next().toString());
                memberCardsService.save(memberCards);
            }
        }
    }

    @Override
    public void deleteAssociatedMember(UsersAssociatedPO cmdPO) {
        // 检测关联用户信息是否存在
        Users users = usersService.getById(cmdPO.getUserId());
        if (StrUtil.isBlank(users.getAssociatedMemberIds())) {
            return;
        }
        // 移除关联用户id
        users.setAssociatedMemberIds(
                users.getAssociatedMemberIds().replace("," + cmdPO.getAssociatedMemberId() + ",", ","));
        usersService.updateById(users);
        // 移除使用一方的共享卡
        List<Long> userIds = new ArrayList<>();
        userIds.add(cmdPO.getUserId());
        userIds.add(cmdPO.getAssociatedMemberId());
        List<TemplateCards> templateCards = templateCardsService.listShareByUserIds(userIds);
        if (CollectionUtil.isNotEmpty(templateCards)) {
            for (TemplateCards templateCard : templateCards) {
                if (Objects.isNull(templateCard)) {
                    continue;
                }
                if (cmdPO.getUserId().equals(templateCard.getUserId())) {
                    memberCardsService.DeleteUserShareCards(cmdPO.getAssociatedMemberId(), templateCard.getStoreId());
                } else if (cmdPO.getAssociatedMemberId().equals(templateCard.getUserId())) {
                    memberCardsService.DeleteUserShareCards(cmdPO.getUserId(), templateCard.getStoreId());
                } else {
                    LambdaQueryWrapper<MemberCards> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MemberCards::getTemplateCardId, templateCard.getId());
                    queryWrapper.eq(MemberCards::getUserId, cmdPO.getAssociatedMemberId());
                    queryWrapper.eq(MemberCards::getStoreId, templateCard.getStoreId());
                    queryWrapper.eq(MemberCards::getCardType, CardTypeEnums.SHARE_CARD.getCode());
                    memberCardsService.remove(queryWrapper);
                }
            }
        } else {
            // 处理A共享B，B共享C的情况
            List<Long> userId = new ArrayList<>();
            userId.add(cmdPO.getUserId());
            List<MemberCards> memberCards = memberCardsService.listBy(userId, null, CardTypeEnums.SHARE_CARD);
            if (CollectionUtil.isNotEmpty(memberCards)) {
                for (MemberCards item : memberCards) {
                    LambdaQueryWrapper<MemberCards> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MemberCards::getTemplateCardId, item.getTemplateCardId());
                    queryWrapper.eq(MemberCards::getUserId, cmdPO.getAssociatedMemberId());
                    queryWrapper.eq(MemberCards::getStoreId, item.getStoreId());
                    queryWrapper.eq(MemberCards::getCardType, CardTypeEnums.SHARE_CARD.getCode());
                    memberCardsService.remove(queryWrapper);
                }
            }
        }
    }

    @Override
    public List<UserDetailVO> searchUser(UsersKeywordPO po) {
        List<Users> usersList = usersService.listByKeyword(po);
        usersList = usersList.stream()
                .filter(user -> user.getStatus() != UsersStatusEnums.DELETE.getCode()
                        && user.getStatus() != UsersStatusEnums.STOP.getCode()
                        && user.getStatus() != UsersStatusEnums.MERGE.getCode()
                        && user.getStatus() != UsersStatusEnums.LOGOUT.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(usersList)) {
            return new ArrayList<>();
        }
        List<UserDetailVO> userDetailVOList = BeanUtil.copyToList(usersList, UserDetailVO.class);
        List<Long> levelIds = userDetailVOList.stream().map(UserDetailVO::getMemberLevelId)
                .collect(Collectors.toList());
        List<UserLevels> userLevels = userLevelsService.listByIds(levelIds);
        Map<Long, String> userLevelsMap = userLevels.stream()
                .collect(Collectors.toMap(UserLevels::getId, UserLevels::getLevelName));
        Map<Long, List<MemberCardsVO>> userCardMap = new HashMap<>();
        // 组装金额
        List<Long> userIds = userDetailVOList.stream().map(UserDetailVO::getId).collect(Collectors.toList());
        List<MemberCards> userCardList = memberCardsService.listAllByUserIds(userIds);
        if (CollectionUtil.isNotEmpty(userCardList)) {
            List<Long> templateCardIds = userCardList.stream().map(MemberCards::getTemplateCardId)
                    .collect(Collectors.toList());
            List<CardDTO> cards = cardsService.getByTemplateCardId(templateCardIds);
            Map<Long, String> cardMap = cards.stream()
                    .collect(Collectors.toMap(CardDTO::getTemplateCardId, CardDTO::getCardName));
            List<MemberCardsVO> memberCardsVOList = userCardList.stream().map(m -> {
                MemberCardsVO cardVo = BeanUtil.copyProperties(m, MemberCardsVO.class);
                cardVo.setCardName(cardMap.get(m.getTemplateCardId()));
                return cardVo;
            }).collect(Collectors.toList());
            storeBiz.fullName(
                    memberCardsVOList,
                    MemberCardsVO::getStoreId,
                    MemberCardsVO::setStoreName);
            if (CollectionUtil.isNotEmpty(memberCardsVOList)) {
                userCardMap = memberCardsVOList.stream()
                        .collect(Collectors.groupingBy(MemberCardsVO::getUserId));
            }
        }
        // 会员积分
        List<UserPoints> userPointsList = userPointsService.listByUserIds(userIds);
        // 先根据userId分组，在累加分组中的可用积分
        Map<Long, Long> userPointsMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userPointsList)) {
            userPointsMap = userPointsList.stream()
                    .collect(Collectors.groupingBy(UserPoints::getUserId,
                            Collectors.summingLong(UserPoints::getAvailablePoints)));
        }
        for (UserDetailVO item : userDetailVOList) {
            // 会员等级名称
            item.setMemberLevelName(userLevelsMap.get(item.getMemberLevelId()));
            // 顾问名称
            if (StrUtil.isNotBlank(item.getAdviserUserIds())) {
                List<Long> adviserIds = Arrays.stream(item.getAdviserUserIds().split(","))
                        .filter(e -> StrUtil.isNotBlank(e))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<Users> adviserUsers = usersService.listByIds(adviserIds);
                if (CollectionUtil.isNotEmpty(adviserUsers)) {
                    String adviserNames = adviserUsers.stream()
                            .map(user -> StrUtil.isNotBlank(user.getName()) ? user.getName() : user.getNickname())
                            .collect(Collectors.joining(","));
                    item.setAdviserUserNames(adviserNames);
                }
            }
            // 会员所有的卡
            List<MemberCardsVO> userCardVOList = userCardMap.get(item.getId());
            item.setMemberCardsVOList(userCardVOList);
            // 会员积分
            Long userPoints = userPointsMap.get(item.getId());
            item.setUserPoints(userPoints);
        }
        return userDetailVOList;
    }

    @Override
    public List<UsersVO> searchUserByBirthday() {
        List<Users> list = usersService.selectUserByBirthday();
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public List<UsersVO> searchUserBySpecial(String start, String end, Long memberLevelId) {
        Date startTime = DateUtil.parse(start, "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parse(end, "yyyy-MM-dd HH:mm:ss");
        List<Users> list = usersService.selectUserBySpecial(startTime, endTime, memberLevelId);
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public List<UsersVO> searchUserByType(List<Long> ids, Integer userType) {
        List<Users> list = usersService
                .list(new LambdaQueryWrapper<Users>().eq(Users::getUserType, userType).in(Users::getId, ids));
        return BeanUtil.copyToList(list, UsersVO.class);
    }

    @Override
    public UsersVO getDetailById(Long id) {
        UsersVO usersVO = usersService.getDetailById(id);
        if (usersVO == null) {
            return null;
        }
        List<UserRole> userRoles = userRoleService.listByUserIds(Collections.singletonList(id));
        if (userRoles != null && userRoles.size() > 0) {
            Role role = roleService.getById(userRoles.get(0).getRoleId());
            if (role != null) {
                usersVO.setRoleName(role.getName());
            }
        }
        return usersVO;
    }

    @Override
    public List<UsersVO> listEmp() {
        Users query = new Users();
        query.setUserType(1);
        query.setManager(0);
        usersService.getUserDataPower();
        List<Users> usersList = usersService.list(query);
        return BeanUtil.copyToList(usersList, UsersVO.class);
    }

    private void validLoginUser(Users users) {
        if (users == null) {
            throw new BusinessException("账号不存在或不可用！");
        }
    }

    private LoginUser buildLoginUser(Users user) {
        // 创建登录用户信息
        LoginUser loginUser = BeanUtil.copyProperties(user, LoginUser.class);
        loginUser.setUserId(user.getId());

        // 会员登录，设置小程序相关信息
        if (UsersTypeEnums.MEMBER.getCode().equals(user.getUserType())) {
            // TODO sessionKey
            // loginUser.setSessionKey(user.getSessionKey());
        }
        return loginUser;
    }

    @Override
    public Integer getUserCount() {
        return usersService.getUserCount();
    }

    @Override
    public PageResult<UsersVO> pageList2(UsersQueryPO queryPO) {
        // 查询用户列表
        UsersQueryDTO queryDTO = BeanUtil.copyProperties(queryPO, UsersQueryDTO.class);
        queryDTO.setUserType(2);
        Page<UsersResultDTO> result = usersService.pageList2(queryPO.buildPage(), queryDTO);
        if (CollectionUtil.isEmpty(result.getRecords())) {
            return PageResultHelper.transfer(result, UsersVO.class);
        }

        return PageResultHelper.transfer(result, UsersVO.class);
    }
}