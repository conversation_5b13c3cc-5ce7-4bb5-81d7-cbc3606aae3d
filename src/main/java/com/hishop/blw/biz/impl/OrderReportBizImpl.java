package com.hishop.blw.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.blw.biz.*;
import com.hishop.blw.enums.*;
import com.hishop.blw.model.po.report.ConsumeReportPO;
import com.hishop.blw.model.po.report.FinanceReportQueryPO;
import com.hishop.blw.model.po.report.SaleReportQueryPO;
import com.hishop.blw.model.vo.order.OrderItemsVO;
import com.hishop.blw.model.vo.order.PayMethodsVO;
import com.hishop.blw.model.vo.report.*;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.*;
import com.hishop.blw.repository.service.*;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderReportBizImpl implements OrderReportBiz {

    private static final OrderAmountStatisticsDTO EMPTY_ORDER_AMOUNT_STATISTICS_DTO = OrderAmountStatisticsDTO.builder()
            .individual(BigDecimal.ZERO)
            .member(BigDecimal.ZERO)
            .other(BigDecimal.ZERO)
            .build();

    @Value("${consumer.statistics.max-row:2000}")
    private Integer consumerStatisticsMaxRow;

    @Resource
    private OrdersService ordersService;
    @Resource
    private OrderSourcesService orderSourcesService;
    @Resource
    private UsersService usersService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private ProductsService productsService;
    @Resource
    private ProductCategoriesService productCategoriesService;
    @Resource
    private PaymentsService paymentsService;
    @Resource
    private PayMethodsService payMethodsService;
    @Resource
    private OrderItemsService orderItemsService;
    @Resource
    private TaskTargetService taskTargetService;

    @Resource
    private OrderSourcesBiz orderSourcesBiz;
    @Resource
    private DepartmentBiz departmentBiz;
    @Resource
    private UsersBiz usersBiz;
    @Resource
    private ProductsBiz productsBiz;
    @Resource
    private ProductCategoriesBiz productCategoriesBiz;
    @Resource
    private OrderItemsExtendService orderItemsExtendService;
    @Resource
    private RefundBiz refundBiz;
    @Resource
    private RefundItemsExtendService refundItemsExtendService;
    @Resource
    private RoomsService roomsService;
    @Resource
    private DevicesService devicesService;
    @Resource
    private RefundService refundService;

    @Override
    public PageResult<SaleReportVO> saleReport(SaleReportQueryPO queryPO) {
        if (StrUtil.isNotBlank(queryPO.getProductName()) || queryPO.getProductType() != null
                || queryPO.getOrderCategory() != null
                || queryPO.getProjectDepartmentId() != null) {
            // 用来设置关联订单子项扩展表查询
            queryPO.setOrderItemExtendSelect(1);
        }
        if (queryPO.getOrderDateEnd() != null) {
            LocalDateTime date = queryPO.getOrderDateEnd().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime newLocalDateTime = date.plusDays(1);
            Date newDate = Date.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            queryPO.setOrderDateEnd(newDate);
        }
        // 排除手续费商品ID，不进入该报表,DD 说的 2025-03-19
        queryPO.setNotInProductId(0L);

        Page<SaleReportVO> result = ordersService.saleReport(queryPO.buildPage(), queryPO);
        PageResult<SaleReportVO> pageResult = PageResultHelper.transfer(result, SaleReportVO.class);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            // 数据组装
            this.dataAssembly(pageResult.getList(), queryPO);
            // 处理商品实际数量
            this.assemblyQuantity(queryPO,pageResult.getList());
        }
        return pageResult;
    }

    @Override
    public SaleReportTotalVO saleReportTotal(SaleReportQueryPO queryPO) {
        if (StrUtil.isNotBlank(queryPO.getProductName()) || queryPO.getProductType() != null
                || queryPO.getOrderCategory() != null
                || queryPO.getProjectDepartmentId() != null) {
            // 用来设置关联订单子项扩展表查询
            queryPO.setOrderItemExtendSelect(1);
        }
        if (queryPO.getOrderDateEnd() != null) {
            LocalDateTime date = queryPO.getOrderDateEnd().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime newLocalDateTime = date.plusDays(1);
            Date newDate = Date.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            queryPO.setOrderDateEnd(newDate);
        }
        // 排除手续费商品ID，不进入该报表,DD 说的 2025-03-19
        queryPO.setNotInProductId(0L);
        // 查询支付金额明细（需要过滤不计入财务统计的数据）
        List<OrderAmountDTO> orderAmountDTOList = ordersService.saleReportTotal(queryPO);
        SaleReportTotalVO resultVO = new SaleReportTotalVO();
        if (CollectionUtil.isEmpty(orderAmountDTOList)) {
            return resultVO;
        }
        // 支付金额明细合计，只统计支付记录里符合条件的金额
        BigDecimal amountDetailTotal = orderAmountDTOList.stream()
                .collect(Collectors.groupingBy(
                        OrderAmountDTO::getPayId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.get(0).getAmount()
                        )
                ))
                .values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setPayDetailAmountTotal(amountDetailTotal);
        queryPO.setIzSalesReport(1);
        List<OrderAmountDTO> orderAmountDTOAll = ordersService.saleReportTotal(queryPO);
        if (CollectionUtil.isEmpty(orderAmountDTOAll)) {
            return resultVO;
        }
        // 支付金额合计 = 所有支付金额（不用管是否进入财务统计报表），有两种条件统计，第一种如果没有商品相关的条件，直接从支付表统计，如果有商品相关的条件，则要从订单子项和订单子项扩展表以及退款相关表统计
        BigDecimal payAmountTotalAll = BigDecimal.ZERO;
        //第一种方式，因为支付记录里面已经包含了支付和退款的记录
        if (queryPO.getOrderItemExtendSelect() == null) {
            payAmountTotalAll = orderAmountDTOAll.stream()
                    //.filter(e -> {
                    //    if (queryPO.getOrderDateBegin() != null) {
                    //        return e.getOrderDate().after(queryPO.getOrderDateBegin()) && e.getOrderDate().before(queryPO.getOrderDateEnd());
                    //    }
                    //    return true;
                    //})
                    .map(OrderAmountDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }else {
            //第二种条件计算
            BigDecimal payAmountTotal = orderAmountDTOList.stream()
                    .filter(e -> {
                        boolean one = e.getItemActualAmount() != null;
                        boolean two = true;
                        //加入two是为了过滤订单创建时间不在指定时间段内的数据
                        if (queryPO.getOrderDateBegin() != null) {
                            two = e.getOrderDate().after(queryPO.getOrderDateBegin()) && e.getOrderDate().before(queryPO.getOrderDateEnd());
                        }
                        return one && two;
                    })
                    .collect(Collectors.groupingBy(
                            OrderAmountDTO::getOrderItemId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> list.get(0).getItemActualAmount()
                            )
                    ))
                    .values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal pointPayAmountTotal = orderAmountDTOList.stream()
                    .filter(e -> {
                        boolean one = e.getItemActualPointAmount() != null;
                        boolean two = true;
                        if (queryPO.getOrderDateBegin() != null) {
                            two = e.getOrderDate().after(queryPO.getOrderDateBegin()) && e.getOrderDate().before(queryPO.getOrderDateEnd());
                        }
                        return one && two;
                    })
                    .collect(Collectors.groupingBy(
                            OrderAmountDTO::getOrderItemId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> list.get(0).getItemActualPointAmount()
                            )
                    ))
                    .values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //统计退款金额
            List<ReportRefundAmountDTO> refundAll = ordersService.saleRefundReportTotal(queryPO);
            BigDecimal refundTotalAmount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(refundAll)) {
                //统计所有退款子项的退款金额
                BigDecimal itemRefundAmountAll = refundAll.stream()
                        .filter(e -> e.getRefundAmount() != null)
                        .collect(Collectors.groupingBy(
                                ReportRefundAmountDTO::getId,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> list.get(0).getRefundAmount()
                                )
                        ))
                        .values().stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //统计所有退款子项的退款积分金额
                BigDecimal itemRefundPointAmountAll = refundAll.stream()
                        .filter(e -> e.getRefundAmount() != null)
                        .collect(Collectors.groupingBy(
                                ReportRefundAmountDTO::getId,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> list.get(0).getRefundPointsAmount()
                                )
                        ))
                        .values().stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                refundTotalAmount = itemRefundAmountAll.add(itemRefundPointAmountAll);
            }
            payAmountTotalAll = payAmountTotal.add(pointPayAmountTotal).subtract(refundTotalAmount);
        }
        resultVO.setPayAmountTotal(payAmountTotalAll);
        // 数量合计，数量是统计进入销售报表的订单的商品数量
        List<Long> orderIdList = orderAmountDTOList.stream().map(OrderAmountDTO::getId).collect(Collectors.toList());
        List<OrderItemsVO> orderItemsVOList = orderItemsService.listAllItemByOrderId(orderIdList, queryPO.getProductName(), queryPO.getProjectDepartmentId());
        Map<Long, List<OrderItemsVO>> orderItemsVOMap = orderItemsVOList.stream().collect(Collectors.groupingBy(OrderItemsVO::getId));
        List<OrderItems> orderItemList = orderItemsService.listByOrderIds(orderIdList);
        Map<Long, OrderItems> orderItemsMap = orderItemList.stream().collect(Collectors.toMap(OrderItems::getId, Function.identity()));
        List<Long> orderItemIdList = orderItemsVOList.stream().map(OrderItemsVO::getId).collect(Collectors.toList());
        List<RefundQuantityDTO> refundQuantityDTOList = refundBiz.listOrderItemRefundCount(orderItemIdList);
        Map<Long, BigDecimal> refundQuantityMap = new HashMap<>();
        if (CollUtil.isNotEmpty(refundQuantityDTOList)) {
            refundQuantityMap = refundQuantityDTOList.stream().collect(Collectors.toMap(RefundQuantityDTO::getOrderItemId, RefundQuantityDTO::getRefundQuantity));
        }
        orderItemsVOList.forEach(oi -> oi.setQuantityByReport(BigDecimal.valueOf(oi.getQuantity())));
        //lambda问题，这里需要重新赋值
        Map<Long, BigDecimal> refundQuantityMapNew = refundQuantityMap;
        orderItemsVOMap.forEach((id, tempOrderItemList) -> {
            BigDecimal orderItemRefundCount = refundQuantityMapNew.get(id);
            if (orderItemRefundCount == null || BigDecimal.ZERO.compareTo(orderItemRefundCount) >= 0) {
                return;
            }
            OrderItems orderItem = orderItemsMap.get(id);
            BigDecimal oldQuantity = BigDecimal.valueOf(orderItem.getQuantity());
            BigDecimal newQuantity = oldQuantity.subtract(orderItemRefundCount);
            tempOrderItemList.forEach(item -> {
//                Integer quantity = item.getQuantity() / oldQuantity * newQuantity;
                item.setQuantityByReport(BigDecimal.valueOf(item.getQuantity()).divide(oldQuantity, 0 , RoundingMode.DOWN).multiply(newQuantity));
            });
            //如果有时间参数，如果item创建时间不在时间范围内的，直接取退款数量为最终的数量，但是要是负数
            if (queryPO.getOrderDateBegin() != null) {
                Date itemPayDate = orderItem.getPayDate();
                if (itemPayDate.after(queryPO.getOrderDateEnd()) || itemPayDate.before(queryPO.getOrderDateBegin())) {
//                    orderItem.setQuantity(-orderItemRefundCount);
                    tempOrderItemList.forEach(item -> {
//                        Integer quantity = item.getQuantity() / oldQuantity * orderItemRefundCount;
                        item.setQuantityByReport(BigDecimal.valueOf(item.getQuantity()).divide(oldQuantity, 0 , RoundingMode.DOWN).multiply(orderItemRefundCount).negate());
                    });
                }
            }
        });
        BigDecimal quantityTotal = orderItemsVOList.stream().map(OrderItemsVO::getQuantityByReport).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setQuantityTotal(quantityTotal);
        return resultVO;
    }

    @Override
    public PageResult<FinanceReportVO> financeSaleReport(FinanceReportQueryPO queryPO) {
        // 校验开始时间和结束时间不能跨月
        if (queryPO.getPayDateBegin().getMonth() != queryPO.getPayDateEnd().getMonth()) {
            throw new BusinessException("开始时间和结束时间不能跨月");
        }
//        if (queryPO.getPayDateEnd() != null) {
//            LocalDateTime date = queryPO.getPayDateEnd().toInstant()
//                    .atZone(ZoneId.systemDefault())
//                    .toLocalDateTime();
////            LocalDateTime newLocalDateTime = date.plusDays(1);
////            Date newDate = Date.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
//            queryPO.setPayDateEnd(newDate);
//        }
        // 先根据条件分页查询所有部门
        // Page<Long> departmentIdList = ordersService.listDepartmentList(queryPO.buildPage(), queryPO);

        // 获取当前登录用户的部门权限数据
        Page<Department> departmentIdList = departmentService.listPage(queryPO.buildPage(),queryPO);


        if (CollectionUtil.isEmpty(departmentIdList.getRecords())) {
            return PageResultHelper.transfer(new Page<>(), FinanceReportVO.class);
        }
        List<Long> departmentIds = departmentIdList.getRecords().stream().map(Department::getId).collect(Collectors.toList());
        PageResult<FinanceReportVO> pageResult = new PageResult<>();
        pageResult.setPages((int) departmentIdList.getPages());
        pageResult.setTotal(departmentIdList.getTotal());
        pageResult.setPageNum((int) departmentIdList.getCurrent());
        pageResult.setPageSize((int) departmentIdList.getSize());
        queryPO.setDepartmentIdList(departmentIds);
//        List<Department> departmentList = departmentService.listByIds(departmentIds);
//        if (CollectionUtil.isEmpty(departmentList)) {
//            return pageResult;
//        }
        Map<Long, Department> departmentMap = departmentIdList.getRecords().stream()
                .collect(Collectors.toMap(Department::getId, Function.identity(),(existing, replacement) -> existing));
        // 在根据部门和时间条件查询所有订单（不分页）
        List<FinanceSaleReportDTO> result = ordersService.financeSaleReport(queryPO);
//        if (CollectionUtil.isEmpty(result)) {
//            return pageResult;
//        }
        // 计算报表内容
        List<Long> orderIds = result.stream().map(FinanceSaleReportDTO::getOrderId).collect(Collectors.toList());
        List<OrderItems> orderItemsList = orderItemsService.listByOrderIds(orderIds);
//        if (CollectionUtil.isEmpty(orderItemsList)) {
//            return pageResult;
//        }
        // 根据订单子项中商品是否包含疗程商品来设置订单消费类型，如果包含疗程商品，则是疗程消费类型，否则是项目消费类型
        Map<Long, Boolean> orderConsumeTypeMap = this.getOrderConsumeType(orderItemsList);
        // 根据部门id分组
        Map<Long, List<FinanceSaleReportDTO>> financeSaleReportMap = result.stream()
                .collect(Collectors.groupingBy(FinanceSaleReportDTO::getDepartmentId));
        List<FinanceReportVO> list = new ArrayList<>();
        for (Long departmentId : departmentIds) {
            FinanceReportVO resultVO = new FinanceReportVO();
            resultVO.setDepartmentId(departmentId);
            Department department = departmentMap.get(departmentId);
            if(Objects.nonNull(department)) {
                resultVO.setDepartmentName(departmentMap.get(departmentId).getDepartmentName());
            }
            List<FinanceSaleReportDTO> departmentData = financeSaleReportMap.get(departmentId);
            if (CollectionUtil.isEmpty(departmentData)) {
                list.add(resultVO);
                continue;
            }
            // 统计会员单次划扣和散客单次现金（项目和疗程）
            resultVO = this.getMemberSingleAmount(resultVO, departmentData, queryPO.getPayDateEnd(), orderConsumeTypeMap);
            // 统计会员单次划扣累计和散客单次现金累计（项目/疗程）
            resultVO = this.getMemberSingleAmountSum(resultVO,departmentData,orderConsumeTypeMap);
            // 统计会员单次划扣累积和散客单次现金累积（项目和疗程）
            this.getMemberTotalAmount(resultVO, departmentData, orderConsumeTypeMap);
            // 统计所有订单的消费金额（不区分项目和疗程）
            this.getDayTotalAmount(resultVO, departmentData, queryPO.getPayDateEnd());
            this.getMonthTotalAmount(resultVO, departmentData);
            // 指标
            TaskTarget queryEntity = new TaskTarget();
            queryEntity.setStoreId(queryPO.getStoreId());
            queryEntity.setDepartmentId(departmentId);
            queryEntity.setTargetType(TargetTypeEnum.MONTH.getCode());
            // 日期转换为2000-01格式
            queryEntity.setTargetScope(DateUtil.format(queryPO.getPayDateEnd(), DatePattern.NORM_MONTH_PATTERN));
            TaskTarget taskTarget = taskTargetService.getExists(queryEntity);
            if(Objects.isNull(taskTarget)){
                resultVO.setCompletionRate(BigDecimal.valueOf(100L).divide(BigDecimal.valueOf(1L),2,RoundingMode.HALF_UP));
            }
            else {
                BigDecimal salesAmount = taskTarget.getSalesAmount();
                resultVO.setBudgetAmount(salesAmount);
                BigDecimal monthTotalAmount = resultVO.getMonthTotalAmount();
                BigDecimal results = BigDecimal.ZERO;
                if(salesAmount.signum() != 0){
                    results = monthTotalAmount.divide(salesAmount,4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100L)).setScale(2,RoundingMode.HALF_UP);
                }else{
                    results = BigDecimal.valueOf(100L);
                }
                resultVO.setCompletionRate(results);
            }
            resultVO.setActualAmount(resultVO.getMonthTotalAmount().subtract(resultVO.getBudgetAmount()));
            list.add(resultVO);
        }
        pageResult.setList(list);
        return pageResult;
    }



    @Override
    public PageResult<FinanceReportVO> financeConsumeReport(FinanceReportQueryPO queryPO) {
        // 校验开始时间和结束时间不能跨月
        if (queryPO.getPayDateBegin().getMonth() != queryPO.getPayDateEnd().getMonth()) {
            throw new BusinessException("开始时间和结束时间不能跨月");
        }

//        // 报表重够
//        // 先获取部门分页数据
//        // 再次根据部门信息获取消耗订单
//        // 然后在计算对应的数据
//        // 商品、菜品、项目、套餐统计到项目消费中，疗程统计到疗程消费中
//        // 会员单次划扣: 根据消耗订单的支付方式（`iz_walkin` int(10) DEFAULT NULL COMMENT '是否散客支付 0-否 1-是 2-不选择'）即获取iz_walkin = 0
//        // 散客单次现金: 根据消耗订单的支付方式（`iz_walkin` int(10) DEFAULT NULL COMMENT '是否散客支付 0-否 1-是 2-不选择'）即获取iz_walkin = 1
//        FinanceReportQueryPO queryPO1=new FinanceReportQueryPO();
//        queryPO1.setStoreId(queryPO.getStoreId());
//        queryPO1.setDepartmentId(queryPO.getDepartmentId());
//        departmentService.listPage(queryPO.buildPage(), queryPO)



        // 先根据条件分页查询所有部门
        Page<Long> departmentIdList = ordersService.listDepartmentList(queryPO.buildPage(), queryPO);
        if (CollectionUtil.isEmpty(departmentIdList.getRecords())) {
            return PageResultHelper.transfer(queryPO.buildPage(), FinanceReportVO.class);
        }

        queryPO.setDepartmentIdList(departmentIdList.getRecords());
        // 在根据部门和时间条件查询所有订单（不分页）
        List<FinanceConsumeReportDTO> reportList = ordersService.financeConsumeReport(queryPO);
        if (CollectionUtil.isEmpty(reportList)) {
            return PageResultHelper.transfer(departmentIdList, FinanceReportVO.class, (e, v) -> v.setDepartmentId(e));
        }

        Map<String, List<OrderItemPaymentDTO>> orderItemPaymentMap = this.getOrderItemPaymentMap(reportList);


        Map<Long, List<FinanceConsumeReportDTO>> groupByDepartment = reportList.stream()
                .collect(Collectors.groupingBy(FinanceConsumeReportDTO::getDepartmentId));

        int endYear = DateUtil.year(queryPO.getPayDateEnd());
        int endDay = DateUtil.dayOfYear(queryPO.getPayDateEnd());
        PageResult<FinanceReportVO> result = PageResultHelper.transfer(departmentIdList, FinanceReportVO.class,
                (e, v) -> v.setDepartmentId(e));


        for (FinanceReportVO financeReportVO : result.getList()) {
            // 当前部门的数据
            List<FinanceConsumeReportDTO> curDepartmentList = groupByDepartment.get(financeReportVO.getDepartmentId());
            if (CollUtil.isEmpty(curDepartmentList)) {
                continue;
            }

            PairDTO<BigDecimal, BigDecimal> memberProject = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .filter(oip -> !ProductTypeEnums.COURSE.getCode().equals(oip.getProductType()))
                    .map(oip -> new PairDTO<>(oip.sumAmount(0), oip.sumAmount(1)))
                    .reduce(new PairDTO<>(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN),
                            BigDecimal.ZERO.setScale(2, RoundingMode.DOWN)), (a, b) -> {
                                a.setFirst(a.getFirst().add(b.getFirst()));
                                a.setSecond(a.getSecond().add(b.getSecond()));
                                return a;
                            });
            financeReportVO.setProjectMemberSingleAmountSum(memberProject.getFirst());
            financeReportVO.setProjectWalkinSingleAmountSum(memberProject.getSecond());

            PairDTO<BigDecimal, BigDecimal> memberProjectByEnd = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .filter(oip -> endYear == DateUtil.year(oip.getDate()) && endDay == DateUtil.dayOfYear(oip.getDate()))
                    .filter(oip -> !ProductTypeEnums.COURSE.getCode().equals(oip.getProductType()))
                    .map(oip -> new PairDTO<>(oip.sumAmount(0), oip.sumAmount(1)))
                    .reduce(new PairDTO<>(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN),
                            BigDecimal.ZERO.setScale(2, RoundingMode.DOWN)), (a, b) -> {
                                a.setFirst(a.getFirst().add(b.getFirst()));
                                a.setSecond(a.getSecond().add(b.getSecond()));
                                return a;
                            });
            financeReportVO.setProjectMemberSingleAmount(memberProjectByEnd.getFirst());
            financeReportVO.setProjectWalkinSingleAmount(memberProjectByEnd.getSecond());

            PairDTO<BigDecimal, BigDecimal> memberCourse = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .filter(oip -> endYear == DateUtil.year(oip.getDate()) && endDay == DateUtil.dayOfYear(oip.getDate()))
                    .filter(oip -> ProductTypeEnums.COURSE.getCode().equals(oip.getProductType()))
                    .map(oip -> new PairDTO<>(oip.sumAmount(0), oip.sumAmount(1)))
                    .reduce(new PairDTO<>(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN),
                            BigDecimal.ZERO.setScale(2, RoundingMode.DOWN)), (a, b) -> {
                                a.setFirst(a.getFirst().add(b.getFirst()));
                                a.setSecond(a.getSecond().add(b.getSecond()));
                                return a;
                            });
            financeReportVO.setCourseMemberSingleAmount(memberCourse.getFirst());
            financeReportVO.setCourseWalkinSingleAmount(memberCourse.getSecond());

            PairDTO<BigDecimal, BigDecimal> memberCourseByEnd = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .filter(oip -> ProductTypeEnums.COURSE.getCode().equals(oip.getProductType()))
                    .map(oip -> new PairDTO<>(oip.sumAmount(0), oip.sumAmount(1)))
                    .reduce(new PairDTO<>(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN),
                            BigDecimal.ZERO.setScale(2, RoundingMode.DOWN)), (a, b) -> {
                                a.setFirst(a.getFirst().add(b.getFirst()));
                                a.setSecond(a.getSecond().add(b.getSecond()));
                                return a;
                            });
            financeReportVO.setCourseMemberSingleAmountSum(memberCourseByEnd.getFirst());
            financeReportVO.setCourseWalkinSingleAmountSum(memberCourseByEnd.getSecond());

            BigDecimal dayTotalAmount = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .filter(oip -> endYear == DateUtil.year(oip.getDate()) && endDay == DateUtil.dayOfYear(oip.getDate()))
                    .map(oip -> oip.sumAmount(0).add(oip.sumAmount(1).add(oip.sumAmount(2))))
                    .reduce(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), BigDecimal::add);
            financeReportVO.setDayTotalAmount(dayTotalAmount);

            BigDecimal monthTotalAmount = curDepartmentList.stream()
                    .map(fcr -> OrderItemPaymentDTO.getKey(fcr.getId(), fcr.getOrderItemId(), fcr.getProductId()))
                    .map(orderItemPaymentMap::get)
                    .flatMap(List::stream)
                    .map(oip -> oip.sumAmount(0).add(oip.sumAmount(1).add(oip.sumAmount(2))))
                    .reduce(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), BigDecimal::add);
            financeReportVO.setMonthTotalAmount(monthTotalAmount);

            TaskTarget queryEntity = new TaskTarget();
            queryEntity.setStoreId(queryPO.getStoreId());
            queryEntity.setDepartmentId(financeReportVO.getDepartmentId());
            queryEntity.setTargetType(TargetTypeEnum.MONTH.getCode());
            // 日期转换为2000-01格式
            queryEntity.setTargetScope(DateUtil.format(queryPO.getPayDateEnd(), DatePattern.NORM_MONTH_PATTERN));

            TaskTarget taskTarget = taskTargetService.getExists(queryEntity);
            if(Objects.isNull(taskTarget)){
                financeReportVO.setCompletionRate(BigDecimal.valueOf(100L).divide(BigDecimal.valueOf(1L),2,RoundingMode.HALF_UP));
            }
            else {
                BigDecimal salesAmount = taskTarget.getSalesAmount();
                financeReportVO.setBudgetAmount(salesAmount);
                BigDecimal monthTotal = financeReportVO.getMonthTotalAmount();
                BigDecimal results;
                if(salesAmount.signum() != 0){
                    results = monthTotal.divide(salesAmount,2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100L)).setScale(2,RoundingMode.HALF_UP);
                }else{
                    results = BigDecimal.valueOf(100L);
                }
                financeReportVO.setCompletionRate(results);
            }
            financeReportVO.setActualAmount(financeReportVO.getMonthTotalAmount().subtract(financeReportVO.getBudgetAmount()));

        }

        departmentBiz.fullName(result.getList(), FinanceReportVO::getDepartmentId, FinanceReportVO::setDepartmentName);
        return result;
    }

    @Override
    public PageResult<ConsumeReportVO> consumerReport(ConsumeReportPO po) {
        ConsumeReportQueryDTO query = BeanUtil.copyProperties(po, ConsumeReportQueryDTO.class);
        Page<ConsumeReportDTO> page = ordersService.consumerReport(po.buildPage(), query);
        PageResult<ConsumeReportVO> res = PageResultHelper.transfer(page, ConsumeReportVO.class);
        List<ConsumeReportVO> list = res.getList();
        if (CollUtil.isEmpty(list)) {
            return res;
        }

        List<Long> orderIds = list.stream()
                .map(ConsumeReportVO::getOrderId)
                .distinct()
                .collect(Collectors.toList());

        // 支付金额是包括积分的价值的，所以要把积分价值加上
        for (ConsumeReportVO consumeReportVO : list) {
            BigDecimal amount = Optional.ofNullable(consumeReportVO.getPayAmount()).orElse(BigDecimal.ZERO);
            BigDecimal pointAmount = consumeReportVO.getPayPointAmount();
            if(pointAmount != null){
                amount = amount.add(pointAmount);
            }
            BigDecimal refundAmount = consumeReportVO.getRefundAmount();
            if(refundAmount != null){
                amount = amount.subtract(refundAmount);
            }
            consumeReportVO.setAmount(amount);
        }

        Map<Long, List<OrderPaymentAmountDTO>> orderPayMap = paymentsService.listOrderPaymentAmount(orderIds);

        // 计算所有订单的总支付金额
        Map<Long, BigDecimal> orderTotalAmountMap = orderPayMap.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(OrderPaymentAmountDTO::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                ));

        orderSourcesBiz.fullData(list, ConsumeReportVO::getOrderSourceId,
                (vo, source) -> vo.setOrderSource(source.getSourceName()));
        departmentBiz.fullName(list, ConsumeReportVO::getSalesDepartmentId, ConsumeReportVO::setSalesDepartmentName);
        usersBiz.fullUserName(list, ConsumeReportVO::getSalesUserId, ConsumeReportVO::setSalesUserName);
        usersBiz.fullUserName(list, ConsumeReportVO::getUserId, ConsumeReportVO::setNickName);
        productsBiz.fullData(list, ConsumeReportVO::getProductId, (vo, one) -> vo.setCategoryId(one.getCategoryId()));
        productCategoriesBiz.fullData(list,
                ConsumeReportVO::getCategoryId,
                (vo, one) -> vo.setCategoryOne(one.getCategoryName()),
                (vo, two) -> vo.setCategoryTwo(two.getCategoryName()));

        // 处理预约单预约信息，预约员工、设备、房间等信息
        List<Long> employeeIds = list.stream().filter(e -> e.getEmployeeId() != null && e.getEmployeeId() > 0)
                .map(ConsumeReportVO::getEmployeeId)
                .collect(Collectors.toList());

        List<Long> roomIds = list.stream().filter(e -> e.getRoomId() != null && e.getRoomId() > 0)
                .map(ConsumeReportVO::getRoomId)
                .collect(Collectors.toList());

        List<Long> deviceIds = list.stream().filter(e -> e.getDeviceId() != null && e.getDeviceId() > 0)
                .map(ConsumeReportVO::getDeviceId)
                .collect(Collectors.toList());

        // 定义预约单：预约员工、设别、房间信息Map
        Map<Long,Users> employeeMap = new HashMap<>();
        Map<Long,Rooms> roomMap = new HashMap<>();
        Map<Long,Devices> deviceMap = new HashMap<>();

        // 如果预约员工、设别、房间 Ids 信息不为空，则填充对应的Map
        if(CollectionUtil.isNotEmpty(employeeIds)){
            employeeMap = usersService.listByUserIds(employeeIds);
        }
        if(CollectionUtil.isNotEmpty(roomIds)){
            roomMap = roomsService.listMap(roomIds);
        }
        if(CollectionUtil.isNotEmpty(deviceIds)){
            deviceMap = devicesService.listMap(deviceIds);
        }

        for (ConsumeReportVO vo : list) {
            Users users = employeeMap.get(vo.getEmployeeId());
            if(users != null){
                vo.setEmployeeName(users.getNickname());
            }
            Rooms rooms = roomMap.get(vo.getRoomId());
            if(rooms != null){
                vo.setRoomName(rooms.getRoomName());
            }
            Devices devices = deviceMap.get(vo.getDeviceId());
            if(devices != null){
                vo.setDeviceName(devices.getDeviceName());
            }

            // 填充类别
            ProductTypeEnums productTypeEnums = ProductTypeEnums.codeToEnum(vo.getProductType());
            List<OrderPaymentAmountDTO> orderPaymentList = orderPayMap.get(vo.getOrderId());
            if (CollUtil.isEmpty(orderPaymentList)) {
                continue;
            }

            Set<OrderCategoryEnums> orderCategorySet = new HashSet<>();
            for (OrderPaymentAmountDTO orderPayment : orderPaymentList) {
                if (orderPayment.getIzWalkin() == null || orderPayment.getIzWalkin().equals(2)) {
                    orderCategorySet.add(OrderCategoryEnums.FIVE);
                } else if (orderPayment.getIzWalkin().equals(0)) {
                    if (ProductTypeEnums.COURSE != productTypeEnums) {
                        orderCategorySet.add(OrderCategoryEnums.ONE);
                    } else {
                        orderCategorySet.add(OrderCategoryEnums.TWO);
                    }
                } else {
                    if (ProductTypeEnums.COURSE != productTypeEnums) {
                        orderCategorySet.add(OrderCategoryEnums.THREE);
                    } else {
                        orderCategorySet.add(OrderCategoryEnums.FOUR);
                    }
                }
            }
            String orderCategory = orderCategorySet.stream()
                    .sorted(OrderCategoryEnums::compare)
                    .map(OrderCategoryEnums::getMessage)
                    .collect(Collectors.joining(","));
            vo.setOrderCategory(orderCategory);
            // 填充数量
            BigDecimal productQuantity = Optional.ofNullable(vo.getProductQuantity()).orElse(BigDecimal.ZERO);
            BigDecimal refundQuantity = Optional.ofNullable(vo.getRefundQuantity()).orElse(BigDecimal.ZERO);
            BigDecimal subtract = productQuantity.subtract(refundQuantity);
            vo.setQuantity(subtract);
            // 填充支付金额
//            BigDecimal payAmount = Optional.ofNullable(vo.getPayAmount()).orElse(BigDecimal.ZERO);
//            Integer  eatNumbers = Optional.ofNullable(vo.getEatNumbers()).orElse(1);
//            payAmount = payAmount.multiply(BigDecimal.valueOf(eatNumbers)).setScale(2, RoundingMode.DOWN);
//            BigDecimal refundAmount = Optional.ofNullable(vo.getRefundAmount()).orElse(BigDecimal.ZERO);
//            vo.setAmount(payAmount.subtract(refundAmount));

            // 填充支付方式

            // 获取订单的总支付金额
            BigDecimal totalAmount = orderTotalAmountMap.get(vo.getOrderId());


            BigDecimal beforeAmount = BigDecimal.ZERO;

            StringBuilder payMethodBuff = new StringBuilder();

            Iterator<OrderPaymentAmountDTO> iterator = orderPaymentList.iterator();
            while (iterator.hasNext()) {

                OrderPaymentAmountDTO orderPaymentAmount = iterator.next();
                String key = orderPaymentAmount.getPayMethod();
                BigDecimal value = orderPaymentAmount.getAmount();
                BigDecimal curPayMethodAmount;
                if(value.compareTo(BigDecimal.ZERO) == 0 || totalAmount.compareTo(BigDecimal.ZERO) == 0){
                    // 这里处理0元订单的情况
                    curPayMethodAmount = BigDecimal.ZERO;
                }else if (iterator.hasNext() || beforeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    curPayMethodAmount = value.divide(totalAmount, 2, RoundingMode.DOWN)
                            .multiply(vo.getAmount())
                            .setScale(2, RoundingMode.DOWN);
                    beforeAmount = beforeAmount.add(curPayMethodAmount);
                } else {
                    curPayMethodAmount = vo.getAmount().subtract(beforeAmount);
                }

                // 如果当前支付方式不进入消耗报表，则将当前支付方式的金额置为0
                // 之所以后置判断，是因为不进入消耗报表的情况下，不能影响原有的均摊算法
                if(!orderPaymentAmount.getIzConsumeReport()){
                    curPayMethodAmount = BigDecimal.ZERO;
                }

                payMethodBuff.append(key);
                payMethodBuff.append(":");
                payMethodBuff.append(curPayMethodAmount);
                payMethodBuff.append(",");

                if (StrUtil.isNotEmpty(payMethodBuff)) {
                    vo.setPayMethod(payMethodBuff.substring(0, payMethodBuff.length() - 1));
                }
            }
        }
        return res;
    }

    @Override
    public ConsumerStatisticsVO consumerStatistics(ConsumeReportPO queryPO) {

        ConsumerStatisticsVO result = ConsumerStatisticsVO.builder()
                .quantity(BigDecimal.ZERO)
                .amount(BigDecimal.ZERO)
                .amountDetail(BigDecimal.ZERO)
                .point(BigDecimal.ZERO)
                .build();

        ConsumeReportQueryDTO query = BeanUtil.copyProperties(queryPO, ConsumeReportQueryDTO.class);
        Integer count = ordersService.countByConsumerReport(query);
        if(count > this.consumerStatisticsMaxRow){
            throw new BusinessException(String.format("搜索的数据已超出{%s}条，请重新填写搜索条件", this.consumerStatisticsMaxRow));
        }

        queryPO.setPageNo(1);
        queryPO.setPageSize(this.consumerStatisticsMaxRow);

        Page<ConsumeReportDTO> page = ordersService.consumerReport(queryPO.buildPage(), query);
        PageResult<ConsumeReportVO> res = PageResultHelper.transfer(page, ConsumeReportVO.class);

        List<ConsumeReportVO> list = res.getList();
        if (CollUtil.isEmpty(list)) {
            return result;
        }

        List<Long> orderIds = list.stream()
                .map(ConsumeReportVO::getOrderId)
                .distinct()
                .collect(Collectors.toList());

        // 支付金额是包括积分的价值的，所以要把积分价值加上
        for (ConsumeReportVO consumeReportVO : list) {
            BigDecimal amount = Optional.ofNullable(consumeReportVO.getPayAmount()).orElse(BigDecimal.ZERO);
            BigDecimal pointAmount = consumeReportVO.getPayPointAmount();
            if(pointAmount != null){
                amount = amount.add(pointAmount);
            }
            BigDecimal refundAmount = consumeReportVO.getRefundAmount();
            if(refundAmount != null){
                amount = amount.subtract(refundAmount);
            }
            consumeReportVO.setAmount(amount);


            BigDecimal quantity = Optional.ofNullable(consumeReportVO.getProductQuantity()).orElse(BigDecimal.ZERO);
            BigDecimal refundQuantity = consumeReportVO.getRefundQuantity();
            if(refundQuantity != null){
                quantity = quantity.subtract(refundQuantity);
            }
            consumeReportVO.setQuantity(quantity);
        }

        Map<Long, List<OrderPaymentAmountDTO>> orderPayMap = paymentsService.listOrderPaymentAmount(orderIds);
        // 计算所有订单的总支付金额
        Map<Long, BigDecimal> orderTotalAmountMap = orderPayMap.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(OrderPaymentAmountDTO::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                ));


        for (ConsumeReportVO vo : list) {

            result.addQuantity(vo.getQuantity());
            result.addAmount(vo.getAmount());

            if(vo.getPayPoint() != null) {
                result.addPoint(BigDecimal.valueOf(vo.getPayPoint()));
            }


            List<OrderPaymentAmountDTO> orderPaymentList = orderPayMap.get(vo.getOrderId());

            // 获取订单的总支付金额
            BigDecimal totalAmount = orderTotalAmountMap.get(vo.getOrderId());

            BigDecimal beforeAmount = BigDecimal.ZERO;
            Iterator<OrderPaymentAmountDTO> iterator = orderPaymentList.iterator();
            while (iterator.hasNext()) {

                OrderPaymentAmountDTO orderPaymentAmount = iterator.next();
                BigDecimal value = orderPaymentAmount.getAmount();
                BigDecimal curPayMethodAmount;
                if(value.compareTo(BigDecimal.ZERO) == 0 || totalAmount.compareTo(BigDecimal.ZERO) == 0){
                    // 这里处理0元订单的情况
                    curPayMethodAmount = BigDecimal.ZERO;
                }else if (iterator.hasNext() || beforeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    curPayMethodAmount = value.divide(totalAmount, 2, RoundingMode.DOWN)
                            .multiply(vo.getAmount())
                            .setScale(2, RoundingMode.DOWN);
                    beforeAmount = beforeAmount.add(curPayMethodAmount);
                } else {
                    curPayMethodAmount = vo.getAmount().subtract(beforeAmount);
                }

                // 如果当前支付方式不进入消耗报表，则将当前支付方式的金额置为0
                // 之所以后置判断，是因为不进入消耗报表的情况下，不能影响原有的均摊算法
                if(!orderPaymentAmount.getIzConsumeReport()){
                    curPayMethodAmount = BigDecimal.ZERO;
                }
                result.addAmountDetail(curPayMethodAmount);
            }
        }

        return BeanUtil.copyProperties(result, ConsumerStatisticsVO.class);
    }

    private Map<String, List<OrderItemPaymentDTO>> getOrderItemPaymentMap(List<FinanceConsumeReportDTO> reportList) {
        List<Long> orderIds = reportList.stream()
                .map(FinanceConsumeReportDTO::getId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, List<OrderPaymentAmountDTO>> orderPayMap = paymentsService.listOrderPaymentAmount(orderIds);


        // 计算所有订单的总支付金额
        Map<Long, BigDecimal> orderTotalAmountMap = orderPayMap.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(OrderPaymentAmountDTO::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                ));

        List<OrderItemPaymentDTO> list = new ArrayList<>();

        for (FinanceConsumeReportDTO fcr : reportList) {
            List<OrderPaymentAmountDTO> paymentList = orderPayMap.get(fcr.getId());
            if(CollUtil.isEmpty(paymentList)){
                continue;
            }

            OrderItemPaymentDTO ois = OrderItemPaymentDTO.builder()
                    .orderId(fcr.getId())
                    .orderItemId(fcr.getOrderItemId())
                    .productId(fcr.getProductId())
                    .productType(fcr.getProductType())
                    .totalAmount(fcr.getAmount())
                    .date(fcr.getDate())
                    .build();
            list.add(ois);

            BigDecimal totalAmount = orderTotalAmountMap.get(fcr.getId());
            BigDecimal beforeAmount = BigDecimal.ZERO;


            Iterator<OrderPaymentAmountDTO> iterator = paymentList.iterator();
            while (iterator.hasNext()) {
                OrderPaymentAmountDTO orderPaymentAmount = iterator.next();
                BigDecimal value = orderPaymentAmount.getAmount();
                BigDecimal curPayMethodAmount;
                if (value.compareTo(BigDecimal.ZERO) == 0 || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
                    // 这里处理0元订单的情况
                    curPayMethodAmount = BigDecimal.ZERO;
                } else if (iterator.hasNext() || beforeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    curPayMethodAmount = value.divide(totalAmount, 2, RoundingMode.DOWN)
                            .multiply(fcr.getAmount())
                            .setScale(2, RoundingMode.DOWN);
                    beforeAmount = beforeAmount.add(curPayMethodAmount);
                } else {
                    curPayMethodAmount = fcr.getAmount().subtract(beforeAmount);
                }

                // 如果当前支付方式不进入消耗报表，则将当前支付方式的金额置为0
                // 之所以后置判断，是因为不进入消耗报表的情况下，不能影响原有的均摊算法
                if (!orderPaymentAmount.getIzConsumeReport()) {
                    curPayMethodAmount = BigDecimal.ZERO;
                }

                ois.add(OrderItemPaymentDTO.PaymentMethodDTO.builder()
                        .paymentMethodId(orderPaymentAmount.getPaymentMethodId())
                        .payMethod(orderPaymentAmount.getPayMethod())
                        .amount(curPayMethodAmount)
                        .izWalkin(orderPaymentAmount.getIzWalkin())
                        .izConsumeReport(orderPaymentAmount.getIzConsumeReport())
                        .build());

            }

        }

        return list.stream().collect(Collectors.groupingBy(OrderItemPaymentDTO::getKey));
    }



    /**
     * 根据订单子项中商品是否包含疗程商品来设置订单统计类型，如果包含疗程商品，则是疗程消费类型，否则是项目消费类型
     */
    private Map<Long, Boolean> getOrderConsumeType(List<OrderItems> orderItemsList) {
        Map<Long, Boolean> orderCourseMap = new HashMap<>();
        Map<Long, List<OrderItems>> orderItemsMap = orderItemsList.stream()
                .collect(Collectors.groupingBy(OrderItems::getOrderId));
        for (Long orderId : orderItemsMap.keySet()) {
            List<OrderItems> orderItems = orderItemsMap.get(orderId);
            List<OrderItems> courseList = orderItems.stream()
                    .filter(e -> ProductTypeEnums.COURSE.getCode().equals(e.getProductType()))
                    .collect(Collectors.toList());
            // 如果子项中存在商品类型是疗程的，则该订单是疗程消费类型，否则是项目消费类型
            if (CollectionUtil.isNotEmpty(courseList)) {
                orderCourseMap.put(orderId, true);
            } else {
                orderCourseMap.put(orderId, false);
            }
        }
        return orderCourseMap;
    }
    // 会员单次划扣月累计：时间范围累计
    private FinanceReportVO getMemberSingleAmountSum(FinanceReportVO resultVO, List<FinanceSaleReportDTO> financeSaleReportDTOList, Map<Long, Boolean> orderConsumeTypeMap){
        List<FinanceSaleReportDTO> lastDayDataList = BeanUtil.copyToList(financeSaleReportDTOList, FinanceSaleReportDTO.class);
        if (CollectionUtil.isEmpty(lastDayDataList)) {
            return resultVO;
        }
        // 项目消费，散客支付为true或者otherPayMethodId不为空，则是散客单次现金
        BigDecimal projectWalkinSingleAmount = lastDayDataList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return !isCourse && e.getIzWalkin() == 1;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 项目消费，散客支付为false，otherPayMethodId为空，则是会员单次划扣
        BigDecimal projectMemberSingleAmount = lastDayDataList.stream().filter(item -> {
            boolean isCourse = orderConsumeTypeMap.get(item.getOrderId());
            return !isCourse && item.getIzWalkin() == 0;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setProjectWalkinSingleAmountSum(projectWalkinSingleAmount);
        resultVO.setProjectMemberSingleAmountSum(projectMemberSingleAmount);
        return resultVO;
    }

    // 会员单次划扣，散客单次现金
    private FinanceReportVO getMemberSingleAmount(FinanceReportVO resultVO, List<FinanceSaleReportDTO> financeSaleReportDTOList,
            Date payDateEnd, Map<Long, Boolean> orderConsumeTypeMap) {
        // 取日期范围内最后一天的数据，也就是日期等于queryPO.payDateEnd
        List<FinanceSaleReportDTO> lastDayDataList = financeSaleReportDTOList.stream()
                .filter(item -> {
                    LocalDate itemDate = item.getPayDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    LocalDate payDateEndDate = payDateEnd.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return itemDate.equals(payDateEndDate);
                }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(lastDayDataList)) {
            return resultVO;
        }
        // 疗程消费，散客支付为true或者otherPayMethodId不为空，则是散客单次现金
        BigDecimal courseWalkinSingleAmount = lastDayDataList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return isCourse && e.getIzWalkin() == 1;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 疗程消费，散客支付为false，otherPayMethodId为空，则是会员单次划扣
        BigDecimal courseMemberSingleAmount = lastDayDataList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return isCourse && e.getIzWalkin() == 0;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setCourseWalkinSingleAmount(courseWalkinSingleAmount);
        resultVO.setCourseMemberSingleAmount(courseMemberSingleAmount);

        // 项目
        BigDecimal projectWalkinSingleAmount = lastDayDataList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return !isCourse && e.getIzWalkin() == 1;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setProjectWalkinSingleAmount(projectWalkinSingleAmount);

        BigDecimal projectMemberSingleAmount = lastDayDataList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return !isCourse && e.getIzWalkin() == 0;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setProjectMemberSingleAmount(projectMemberSingleAmount);
        return resultVO;
    }

    // 会员单次划扣月累积，散客单次现金月累积
    private void getMemberTotalAmount(FinanceReportVO resultVO, List<FinanceSaleReportDTO> financeSaleReportDTOList,
            Map<Long, Boolean> orderConsumeTypeMap) {
        // 疗程消费，散客支付为true或者otherPayMethodId不为空，则是散客单次现金
        BigDecimal courseWalkinSingleAmountSum = financeSaleReportDTOList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return isCourse && e.getIzWalkin() == 1;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setCourseWalkinSingleAmountSum(courseWalkinSingleAmountSum);

        // 疗程消费，散客支付为false，otherPayMethodId为空，则是会员单次划扣
        BigDecimal courseMemberSingleAmountSum = financeSaleReportDTOList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return isCourse && e.getIzWalkin() == 0;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setCourseMemberSingleAmountSum(courseMemberSingleAmountSum);

        // 项目消费，散客支付为true或者otherPayMethodId不为空，则是散客单次现金
        BigDecimal projectWalkinSingleAmountSum = financeSaleReportDTOList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return !isCourse && e.getIzWalkin() == 1;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setProjectWalkinSingleAmountSum(projectWalkinSingleAmountSum);

        // 项目消费，散客支付为false，otherPayMethodId为空，则是会员单次划扣
        BigDecimal projectMemberSingleAmountSum = financeSaleReportDTOList.stream().filter(e -> {
            boolean isCourse = orderConsumeTypeMap.get(e.getOrderId());
            return !isCourse && e.getIzWalkin() == 0;
        }).map(FinanceSaleReportDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        resultVO.setProjectMemberSingleAmountSum(projectMemberSingleAmountSum);

//        resultVO.setCourseWalkinSingleAmount(courseWalkinSingleAmountSum);
//        resultVO.setCourseMemberSingleAmount(courseMemberSingleAmountSum);
//        resultVO.setProjectWalkinSingleAmountSum(projectWalkinSingleAmountSum);
//        resultVO.setCourseMemberSingleAmountSum(projectMemberSingleAmountSum);
    }

    // 统计日累计和月累积
    private void getDayTotalAmount(FinanceReportVO resultVO, List<FinanceSaleReportDTO> financeSaleReportDTOList,
            Date payDateEnd) {
        // 取日期范围内最后一天的数据，也就是日期等于queryPO.payDateEnd
        List<FinanceSaleReportDTO> lastDayDataList = financeSaleReportDTOList.stream()
                .filter(item -> {
                   // item.getPayDate().equals(payDateEnd)).collect(Collectors.toList();

                    LocalDate itemDate = item.getPayDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    LocalDate payDateEndDate = payDateEnd.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return itemDate.equals(payDateEndDate);
                }).collect(Collectors.toList());


        if (CollectionUtil.isEmpty(lastDayDataList)) {
            return;
        }
        // 日累计（最后一天的累积）
        BigDecimal dayTotalAmount = lastDayDataList.stream().map(FinanceSaleReportDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setDayTotalAmount(dayTotalAmount);
    }

    private void getMonthTotalAmount(FinanceReportVO resultVO, List<FinanceSaleReportDTO> financeSaleReportDTOList){
        List<FinanceSaleReportDTO> lastDayDataList = BeanUtil.copyToList(financeSaleReportDTOList, FinanceSaleReportDTO.class);
        // 月累积（所有的支付金额）
        BigDecimal monthTotalAmount = financeSaleReportDTOList.stream().map(FinanceSaleReportDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setMonthTotalAmount(monthTotalAmount);
    }

    // 处理商品实际数量 = 商品数量 -退款数量
    private void assemblyQuantity(SaleReportQueryPO queryPO,List<SaleReportVO> saleReportVOList){
        if(CollectionUtil.isEmpty(saleReportVOList)){
            return;
        }
        List<Long> orderItemIds = saleReportVOList.stream().map(SaleReportVO::getSaleReportItemVOList).flatMap(item -> item.stream()).map(SaleReportItemVO::getId).collect(Collectors.toList());
        List<ItemRefundCountDTO> itemRefundCountDTOList = refundBiz.getOrderItemRefundCount(orderItemIds);
        Map<Long, BigDecimal> itemRefundCountMap = itemRefundCountDTOList.stream().collect(Collectors.toMap(ItemRefundCountDTO::getOrderItemId, ItemRefundCountDTO::getNum));
        for (SaleReportVO saleReportVO : saleReportVOList){
            if(CollectionUtil.isEmpty(saleReportVO.getSaleReportItemVOList())){
                continue;
            }
            saleReportVO.getSaleReportItemVOList().stream().forEach(item -> {
                Long orderItemId = item.getId();
                //没退款之前的数量
                BigDecimal oldQuantity = item.getQuantity();
                //Integer refundCount = refundBiz.getOrderItemRefundCount(orderItemId);
                BigDecimal refundCount = itemRefundCountMap.getOrDefault(orderItemId, BigDecimal.ZERO);
                if (refundCount == null || BigDecimal.ZERO.compareTo(refundCount) >= 0) {
                    return;
                }
                //退款后数量
                BigDecimal newQuantity = oldQuantity.subtract(refundCount);
                item.setQuantity(newQuantity);
                //扩展子项的数量要响应的改
                List<SaleReportItemExtendVO> extendList = item.getExtendList();
                if (CollectionUtil.isNotEmpty(extendList)) {
                    extendList.stream().forEach(extend -> {
//                        Integer extendQuantity = (extend.getQuantity()/oldQuantity)*newQuantity;
                        extend.setQuantity(extend.getQuantity().divide(oldQuantity, 0, RoundingMode.DOWN).multiply(newQuantity));
                    });
                }
                //有时间参数的情况下，如果下单时间没在时间范围内，那就取退款数量
                if (queryPO.getOrderDateBegin() != null) {
                    Date createDate = item.getOrderDate();
                    if (createDate.before(queryPO.getOrderDateBegin()) || createDate.after(queryPO.getOrderDateEnd())) {
                        item.setQuantity(refundCount.negate());
                    }
                }
            });
        }
    }

    // 数据组装
    private void dataAssembly(List<SaleReportVO> saleReportVOList, SaleReportQueryPO queryPO) {
        Date orderDateBegin = queryPO.getOrderDateBegin();
        Date orderDateEnd = queryPO.getOrderDateEnd();
        List<Long> orderIdList = saleReportVOList.stream().map(SaleReportVO::getId).distinct()
                .collect(Collectors.toList());
        List<OrderItems> orderItemsList = orderItemsService.listByOrderIdsAndDate(orderIdList, orderDateBegin, orderDateEnd, queryPO.getProductName(), queryPO.getProjectDepartmentId());
        List<Long> tempOrderItemIds = orderItemsList.stream().map(OrderItems::getId).collect(Collectors.toList());
        List<OrderItemsExtend> orderItemsExtendListAll = orderItemsExtendService.listByOrderItemIds(tempOrderItemIds, queryPO.getProductName(), queryPO.getProjectDepartmentId());
        List<Long> orderSourceIdList = saleReportVOList.stream().map(SaleReportVO::getOrderSourceId).distinct()
                .collect(Collectors.toList());
        List<OrderSources> orderSourcesList = orderSourcesService.listByIds(orderSourceIdList);
        Map<Long, String> orderSourceMap = orderSourcesList.stream()
                .collect(Collectors.toMap(OrderSources::getId, OrderSources::getSourceName));
        // 人员id
        List<Long> userIdList = saleReportVOList.stream().map(SaleReportVO::getUserId).distinct()
                .collect(Collectors.toList());
        List<Long> saleUserIdList = saleReportVOList.stream().map(SaleReportVO::getSalesUserId).distinct()
                .collect(Collectors.toList());
        List<Long> assistUserIdList = saleReportVOList.stream().map(SaleReportVO::getAssistantUserId).distinct()
                .collect(Collectors.toList());
        List<Long> recommendUserIdList = saleReportVOList.stream().map(SaleReportVO::getRecommendedUserId).distinct()
                .collect(Collectors.toList());
        // adviserUserIds是,12,2,3,这种格式，需要根据,分割，然后过滤非空的用户id
        List<Long> adviserUserIdList = saleReportVOList.stream()
                .map(SaleReportVO::getAdviserUserIds)
                .filter(StrUtil::isNotBlank)
                .flatMap(e -> Arrays.stream(e.split(",")).filter(StrUtil::isNotBlank)).map(Long::parseLong)
                .collect(Collectors.toList());
        // 订单子项中的用户id
        List<Long> orderItemUserIdList = orderItemsList.stream().map(OrderItems::getDepartmentAdviserUserId)
                .distinct().collect(Collectors.toList());
        List<Long> orderItemAdviserUserIdList = new ArrayList<>();
        List<Long> extendProjectDepartmentIdList = new ArrayList<>();
        List<Long> extendProductIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderItemsExtendListAll)) {
            orderItemAdviserUserIdList = orderItemsExtendListAll.stream().map(OrderItemsExtend::getProjectDepartmentId).distinct().collect(Collectors.toList());
            extendProjectDepartmentIdList = orderItemsExtendListAll.stream()
                    .map(OrderItemsExtend::getProjectDepartmentId)
                    .distinct().collect(Collectors.toList());
            extendProductIdList = orderItemsExtendListAll.stream().map(OrderItemsExtend::getProductId).distinct().collect(Collectors.toList());
        }
        List<Long> allUserIdList = new ArrayList<>();
        allUserIdList.addAll(userIdList);
        allUserIdList.addAll(saleUserIdList);
        allUserIdList.addAll(assistUserIdList);
        allUserIdList.addAll(recommendUserIdList);
        allUserIdList.addAll(adviserUserIdList);
        allUserIdList.addAll(orderItemUserIdList);
        allUserIdList.addAll(orderItemAdviserUserIdList);
        List<Users> allUserList = usersService.listByIds(allUserIdList);
        Map<Long, Users> userMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(allUserList)) {
            userMap = allUserList.stream().collect(Collectors.toMap(Users::getId, Function.identity()));
        }
        // 部门
        List<Long> saleDepartmentIdList = saleReportVOList.stream().map(SaleReportVO::getSalesDepartmentId).distinct()
                .collect(Collectors.toList());
        List<Long> assistDepartmentIdList = saleReportVOList.stream().map(SaleReportVO::getAssistantDepartmentId)
                .distinct().collect(Collectors.toList());
        List<Long> recommendDepartmentIdList = saleReportVOList.stream().map(SaleReportVO::getRecommendedDepartmentId)
                .distinct().collect(Collectors.toList());
        // 项目所属部门id
        List<Long> projectDepartmentIdList = orderItemsList.stream().map(OrderItems::getProjectDepartmentId)
                .distinct().collect(Collectors.toList());
        List<Long> allDepartmentIdList = new ArrayList<>();
        allDepartmentIdList.addAll(saleDepartmentIdList);
        allDepartmentIdList.addAll(assistDepartmentIdList);
        allDepartmentIdList.addAll(recommendDepartmentIdList);
        allDepartmentIdList.addAll(projectDepartmentIdList);
        allDepartmentIdList.addAll(extendProjectDepartmentIdList);
        List<Department> allDepartmentList = departmentService.listByIds(allDepartmentIdList);
        Map<Long, String> departmentMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(allDepartmentList)) {
            departmentMap = allDepartmentList.stream()
                    .collect(Collectors.toMap(Department::getId, Department::getDepartmentName));
        }
        // 支付记录，存在查询时间的情况下，要根据时间范围查支付信息，查询条件中涉及到支付方式条件的，也要过滤
        List<Payments> paymentsList = paymentsService.listByOrderIdList(orderIdList, orderDateBegin, orderDateEnd, queryPO.getPayMethodId(), queryPO.getOrderCategory());
        Map<Long, List<Payments>> paymentMap = paymentsList.stream()
                .collect(Collectors.groupingBy(Payments::getOrderId));
        //性能优化，后面需要根据支付方式id获取List<PayMethodsVO>
        List<Long> tempPaymenthodIdList = paymentsList.stream().map(Payments::getPayMethodId).distinct().collect(Collectors.toList());
        List<PayMethodsVO> payMethodsVOListAll = payMethodsService.listAllByPayMethodIds(tempPaymenthodIdList);
        // -----------------------------------商品级别---------------------------------------
        Map<Long, List<OrderItems>> orderItemsMap = orderItemsList.stream()
                .collect(Collectors.groupingBy(OrderItems::getOrderId));
        // 商品分类
        List<Long> productIdList = orderItemsList.stream().map(OrderItems::getProductId).distinct()
                .collect(Collectors.toList());
        productIdList.addAll(extendProductIdList);
        List<Products> productsList = productsService.listByIds(productIdList);
        Map<Long, Products> productMap = productsList.stream()
                .collect(Collectors.toMap(Products::getId, Function.identity()));
        List<Long> categoryIdList = productsList.stream().map(Products::getCategoryId).distinct()
                .collect(Collectors.toList());
        // 第二层级分类
        List<ProductCategories> productCategoriesTwoList = productCategoriesService.listByIds(categoryIdList);
        //（注意餐饮订单的商品可以直接在一级分类下面，所有这个分类列表parentId=0的也是一级分类）
        List<ProductCategories> parentCategoryList = productCategoriesTwoList.stream().filter(e -> e.getParentCategoryId() == 0).collect(Collectors.toList());
        Map<Long, ProductCategories> categoryTwoMap = productCategoriesTwoList.stream()
                .filter(e -> e.getParentCategoryId() != 0)
                .collect(Collectors.toMap(ProductCategories::getId, Function.identity()));
        List<Long> parentCategoryIdList = productCategoriesTwoList.stream().map(ProductCategories::getParentCategoryId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(parentCategoryList)) {
            parentCategoryIdList.addAll(parentCategoryList.stream().map(ProductCategories::getId).collect(Collectors.toList()));
        }
        List<ProductCategories> productCategoriesOneList = productCategoriesService.listByIds(parentCategoryIdList);
        Map<Long, String> categoryOneMap = productCategoriesOneList.stream()
                .collect(Collectors.toMap(ProductCategories::getId, ProductCategories::getCategoryName));
        //所有订单子项对应的退款金额
        List<ItemRefundAmountDTO> refundAmountList = refundBiz.getOrderItemRefundAmount(tempOrderItemIds, orderDateBegin, orderDateEnd, queryPO.getProductName(), queryPO.getProjectDepartmentId());
        Map<Long, Date> orderItemRefundDateMap = refundBiz.getOrderItemRefundDateMap(orderIdList, orderDateBegin, orderDateEnd);
        Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(refundAmountList)) {
            refundAmountMap = refundAmountList.stream().collect(Collectors.toMap(ItemRefundAmountDTO::getOrderItemId, ItemRefundAmountDTO::getAmount));
        }
        for (SaleReportVO saleReportVO : saleReportVOList) {
            // 订单状态名称
            saleReportVO.setOrderStatusName(OrderStatusEnums.codeToMessage(saleReportVO.getOrderStatus()));
            // 订单来源名称
            saleReportVO.setOrderSourceName(orderSourceMap.get(saleReportVO.getOrderSourceId()));
            // 人员姓名
            Users user = userMap.get(saleReportVO.getUserId());
            if (Objects.nonNull(user)) {
                saleReportVO.setUserName(user.getName());
                saleReportVO.setPhone(user.getPhone());
            }
            if (saleReportVO.getSalesUserId() != null) {
                saleReportVO.setSalesUserName(userMap.get(saleReportVO.getSalesUserId()).getName());
            }
            if (saleReportVO.getAssistantUserId() != null) {
                saleReportVO.setAssistantUserName(userMap.get(saleReportVO.getAssistantUserId()).getName());
            }
            if (saleReportVO.getRecommendedUserId() != null) {
                saleReportVO.setRecommendedUserName(userMap.get(saleReportVO.getRecommendedUserId()).getName());
            }
            if (StrUtil.isNotBlank(saleReportVO.getAdviserUserIds())) {
                StringBuilder adviserName = new StringBuilder();
                List<Long> adviserIds = Arrays.stream(saleReportVO.getAdviserUserIds().split(","))
                        .filter(e -> StrUtil.isNotBlank(e))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                for (Long adviserId : adviserIds) {
                    Users users = userMap.get(adviserId);
                    if(Objects.nonNull(users)) {
                        adviserName.append(userMap.get(adviserId).getName() + ",");
                    }
                }
                String names = adviserName.toString();
                if(!names.isEmpty()) {
//                    saleReportVO.setAdviserUserNames(adviserName.toString().substring(0, adviserName.length() - 1));
                    names = names.substring(0, names.length() - 1);
                }
                saleReportVO.setAdviserUserNames(names);
            }
            // 部门名称
            if (saleReportVO.getSalesDepartmentId() != null) {
                saleReportVO.setSalesDepartmentName(departmentMap.get(saleReportVO.getSalesDepartmentId()));
            }
            if (saleReportVO.getAssistantDepartmentId() != null) {
                saleReportVO.setAssistantDepartmentName(departmentMap.get(saleReportVO.getAssistantDepartmentId()));
            }
            if (saleReportVO.getRecommendedDepartmentId() != null) {
                saleReportVO.setRecommendedDepartmentName(departmentMap.get(saleReportVO.getRecommendedDepartmentId()));
            }

            // 支付方式组合显示，用+合并支付方式名称
            List<Payments> payments = paymentMap.get(saleReportVO.getId());
            if (CollUtil.isEmpty(payments)) {
                Payments p = new Payments();
                p.setPayMethodId(-1L);
                payments = new ArrayList<>();
                payments.add(p);
            }
            StringBuilder paymentMethod = new StringBuilder();
            // 支付金额详情
            StringBuilder payDetail = new StringBuilder();
            List<SalesReportPayDetailDTO> payDetailDTOS = new ArrayList<>();
            List<Long> paymenthodIdList = payments.stream().map(Payments::getPayMethodId).distinct()
                    .collect(Collectors.toList());
            //List<PayMethodsVO> payMethodsVOList = payMethodsService.listAllByPayMethodIds(paymenthodIdList);
            List<PayMethodsVO> payMethodsVOList = payMethodsVOListAll.stream().filter(e -> paymenthodIdList.contains(e.getPayMethodId())).collect(Collectors.toList());
            Map<Long, PayMethodsVO> payMethodsMap = payMethodsVOList.stream()
                    .collect(Collectors.toMap(PayMethodsVO::getPayMethodId, Function.identity()));
            // 订单类别
            StringBuilder orderCategoryName = new StringBuilder();
            List<String> categoryNameList = new ArrayList<>();
            List<OrderItems> orderItems = orderItemsMap.get(saleReportVO.getId());
            payments.forEach(e -> {
                // 去重
                if (e.getPayMethod() != null && !paymentMethod.toString().contains(e.getPayMethod())) {
                    paymentMethod.append(e.getPayMethod()).append("+");
                }
                PayMethodsVO payMethods = payMethodsMap.get(e.getPayMethodId());
                if (payMethods == null) {
                    return;
                }
                // 进入销售报表的支付方式，才统计
                SalesReportPayDetailDTO dto = new SalesReportPayDetailDTO();
                dto.setPayMethodName(payMethods.getPayMethodName());
                // 安全处理 Boolean 值
                dto.setAmount(Boolean.TRUE.equals(payMethods.getIzSalesReport()) ? e.getAmount() : BigDecimal.ZERO);
                payDetailDTOS.add(dto);

//                if (payMethods.getIzSalesReport()) {
//                    dto.setAmount(e.getAmount());
//                    dto.setPayMethodName(payMethods.getPayMethodName());
//                    payDetailDTOS.add(dto);
//                } else {
//                    dto.setAmount(BigDecimal.ZERO);
//                    dto.setPayMethodName(payMethods.getPayMethodName());
//                    payDetailDTOS.add(dto);
//                }

                // 订单类别
                if(orderItems != null) {
                    for (OrderItems orderItem : orderItems) {
                        if(orderItem != null) {
                            Integer productType = orderItem.getProductType();
                            // 处理可能的 null productType
                            String sunOrderCategoryName = this.getOrderCategory(payMethods, productType);
                            categoryNameList.add(sunOrderCategoryName);
                        }
                    }
                }
            });
            saleReportVO.setPaymentMethod(paymentMethod.substring(0, paymentMethod.length() - 1));
            List<SalesReportPayDetailDTO> refundLists = payDetailDTOS.stream().filter(e -> e.getAmount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(refundLists)){
                saleReportVO.setIzRefund(false);
            }else{
                saleReportVO.setIzRefund(true);
            }

            // 设置支付方式
            Map<String, BigDecimal> pays = payDetailDTOS.stream()
                    .collect(Collectors.groupingBy(
                            SalesReportPayDetailDTO::getPayMethodName,
                            Collectors.collectingAndThen(
                                    Collectors.mapping(SalesReportPayDetailDTO::getAmount, Collectors.toList()),
                                    amounts -> amounts.stream()
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            )));
            pays.forEach((payMethodName, totalAmount) ->
                    payDetail.append(payMethodName).append(":").append(totalAmount).append("/")
            );
            saleReportVO.setPayDetail(payDetail.substring(0, payDetail.length() - 1));
            if (CollectionUtil.isNotEmpty(categoryNameList)) {
                List<String> cateList = categoryNameList.stream().distinct().collect(Collectors.toList());
                cateList.stream().forEach(item -> {
                    orderCategoryName.append(item+"/");
                });
                saleReportVO.setOrderCategoryName(orderCategoryName.substring(0, orderCategoryName.length() - 1));
            }

            // -------------------商品级别---------------------------
            List<SaleReportItemVO> saleReportItemVOList = BeanUtil.copyToList(orderItems, SaleReportItemVO.class);
            Map<Long, OrderItems> orderItemsPriceMap = orderItems.stream()
                    .collect(Collectors.toMap(OrderItems::getId, Function.identity()));
            List<Long> orderItemIdList = orderItems.stream().map(OrderItems::getId).collect(Collectors.toList());
            List<OrderItemsExtend> orderItemsExtendList = orderItemsExtendListAll.stream().filter(e -> orderItemIdList.contains(e.getOrderItemId())).collect(Collectors.toList());
            //List<OrderItemsExtend> orderItemsExtendList = orderItemsExtendService.listByOrderItemIds(orderItemIdList, queryPO.getProductName(), queryPO.getProjectDepartmentId());
            Map<Long, List<OrderItemsExtend>> orderItemsExtendMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(orderItemsExtendList)) {
                orderItemsExtendMap = orderItemsExtendList.stream()
                        .collect(Collectors.groupingBy(OrderItemsExtend::getOrderItemId));
            }
            for (SaleReportItemVO saleReportItemVO : saleReportItemVOList) {
                // 如果是项目，需要显示项目的原价，因为项目还要往下面拆商品，且要平铺显示，最终项目的原价格+商品的价格才是这个项目实际支付价格
                // 金额还要减去退款的，退款要根据时间范围查询（如果有时间参数的话）
                BigDecimal refundAmount = refundAmountMap.getOrDefault(saleReportItemVO.getId(), BigDecimal.ZERO);
                //性能优化
                //BigDecimal refundAmount = refundBiz.getOrderItemRefundAmount(saleReportItemVO.getId(), orderDateBegin, orderDateEnd, queryPO.getProductName(), queryPO.getProjectDepartmentId());
                //这里为了兼容跨月查询可以直接查询到负数的退款金额，需要根据时间判断是否要用得到订单子项的实付金额
                OrderItems orderItemPrice = orderItemsPriceMap.get(saleReportItemVO.getId());
                if (orderDateBegin != null && orderItemPrice.getPayDate().before(orderDateBegin)) {
                    //取负数
                    saleReportItemVO.setActualAmount(refundAmount.negate());
                }else {
                    BigDecimal actualPointsAmount = orderItemPrice.getActualPointsAmount();
                    saleReportItemVO.setActualAmount(orderItemPrice.getActualAmount().add(actualPointsAmount).subtract(refundAmount));
                }
                Products itemProduct = productMap.get(saleReportItemVO.getProductId());
                saleReportItemVO.setProductTypeName(ProductTypeEnums.codeToMessage(saleReportItemVO.getProductType()));
                // 分类
                if(Objects.nonNull(itemProduct)) {
                    ProductCategories productCategories = categoryTwoMap.get(itemProduct.getCategoryId());
                    if(Objects.nonNull(productCategories)){
                        saleReportItemVO.setTwoCategoryName(productCategories.getCategoryName());
                        saleReportItemVO.setOneCategoryName(categoryOneMap.get(productCategories.getParentCategoryId()));
                    }else {
                        //进入这里说明商品分类不是二级分类而是一级分类（餐饮商品会直接在一级分类下）
                        saleReportItemVO.setOneCategoryName(categoryOneMap.get(itemProduct.getCategoryId()));
                    }
                }
                //有时间入参的情况下，要判断下单时间是否在入参时间内，不在说明是退款的，要从退款表里找退款时间
                if(queryPO.getOrderDateBegin() != null && (saleReportVO.getOrderDate().before(queryPO.getOrderDateBegin()) || saleReportVO.getOrderDate().after(queryPO.getOrderDateEnd()))) {
                    saleReportItemVO.setOrderDate(orderItemRefundDateMap.get(saleReportItemVO.getId()));
                }else {
                    saleReportItemVO.setOrderDate(saleReportVO.getOrderDate());
                }
                saleReportItemVO.setProductDepartmentName(departmentMap.get(saleReportItemVO.getProjectDepartmentId()));
                if (saleReportItemVO.getDepartmentAdviserUserId() != null) {
                    Users departmentAdviserUser = userMap.get(saleReportItemVO.getDepartmentAdviserUserId());
                    if(Objects.nonNull(departmentAdviserUser)) {
                        saleReportItemVO.setDepartmentAdviserUserName(departmentAdviserUser.getName());
                    }
                }
                // 疗程、套餐、项目继续往下拆
                List<OrderItemsExtend> orderItemsExtends = orderItemsExtendMap.get(saleReportItemVO.getId());
                if (CollectionUtil.isNotEmpty(orderItemsExtends)) {
                    List<SaleReportItemExtendVO> saleReportItemExtendVOList = BeanUtil.copyToList(orderItemsExtends,
                            SaleReportItemExtendVO.class);
                    // 用来转换extend里的价格
                    Map<Long, OrderItemsExtend> extendPriceMap = orderItemsExtends.stream()
                            .collect(Collectors.toMap(OrderItemsExtend::getId, Function.identity()));
                    for (SaleReportItemExtendVO saleReportItemExtendVO : saleReportItemExtendVOList) {
                        // 转换实付金额
                        OrderItemsExtend extendPrice = extendPriceMap.get(saleReportItemExtendVO.getId());

                        if(saleReportItemExtendVO.getIzGift()){
                            saleReportItemExtendVO.setActualAmount(BigDecimal.ZERO);
                        }else{
                            //组装子商品的实付金额 = 实付金额+积分金额-退款金额
                            BigDecimal refundAmount1 = refundItemsExtendService.getRefundAmount(extendPrice.getOrderItemId(), extendPrice.getProductId());
                            //老代码，先注释
                            //BigDecimal price = extendPrice.getAmount().add(extendPrice.getPointAmount()).subtract(refundAmount1);
                            //BigDecimal actualAmountItem = price.add(extendPrice.getPointAmount()).subtract(refundAmount1);
                            BigDecimal totalAmount = extendPrice.getAmount().add(extendPrice.getPointAmount());
                            BigDecimal actualAmountItem = totalAmount.subtract(refundAmount1);
                            if(totalAmount.compareTo(BigDecimal.ZERO) > 0 && actualAmountItem.compareTo(BigDecimal.ZERO) < 0){
                                actualAmountItem = BigDecimal.ZERO;
                            }
                            saleReportItemExtendVO.setActualAmount(actualAmountItem);
                        }

                        Products itemExtendProduct = productMap.get(saleReportItemExtendVO.getProductId());
                        saleReportItemExtendVO.setProductTypeName(
                                ProductTypeEnums.codeToMessage(saleReportItemExtendVO.getProductType()));
                        // 分类
                        ProductCategories productCategoriesExtend = categoryTwoMap
                                .get(itemExtendProduct.getCategoryId());
                        if (productCategoriesExtend != null) {
                            saleReportItemExtendVO.setTwoCategoryName(productCategoriesExtend.getCategoryName());
                            saleReportItemExtendVO.setOneCategoryName(categoryOneMap.get(productCategoriesExtend.getParentCategoryId()));
                        }else {
                            saleReportItemExtendVO.setOneCategoryName(categoryOneMap.get(itemExtendProduct.getCategoryId()));
                        }
                        saleReportItemExtendVO.setOrderDate(saleReportVO.getOrderDate());
                        saleReportItemExtendVO.setProductDepartmentName(
                                departmentMap.get(saleReportItemExtendVO.getProjectDepartmentId()));
                        if (saleReportItemExtendVO.getDepartmentAdviserUserId() != null) {
                            Users departmentAdviserUser = userMap.get(saleReportItemExtendVO.getDepartmentAdviserUserId());
                            if(Objects.nonNull(departmentAdviserUser)) {
                                saleReportItemExtendVO.setDepartmentAdviserUserName(departmentAdviserUser.getName());
                            }
                        }
                    }
                    // 如果订单子项是项目，在扩展列表中需要包含当前项目
                    if (ProductTypeEnums.PROJECT.getCode().equals(saleReportItemVO.getProductType())) {
                        SaleReportItemExtendVO projectExtendVO = BeanUtil.copyProperties(saleReportItemVO,
                                SaleReportItemExtendVO.class);
                        // 求出订单子项扩张表中的金额
                        BigDecimal reduce = saleReportItemExtendVOList.stream().map(SaleReportItemExtendVO::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        projectExtendVO.setActualAmount(projectExtendVO.getActualAmount().subtract(reduce));
                        saleReportItemExtendVOList.add(projectExtendVO);
                    }
                    saleReportItemVO.setExtendList(saleReportItemExtendVOList);
                }
            }
            saleReportVO.setSaleReportItemVOList(saleReportItemVOList);
        }
    }


    /**
     * 根据支付方式，订单类别（1:会员单次项目抵扣、2:会员单次疗程抵扣、3：散客单次项目现金、4：散客单次疗程现金、5：赠送）
     * 1）类型定义如下
     * ● 会员单次项目抵扣：项目单，支付方式的【是否散客支付】为否
     * ● 会员单次疗程抵扣：疗程单，支付方式的【是否散客支付】为否
     * ● 散客单次项目现金：项目单，支付方式的【是否散客支付】为是
     * ● 散客单次疗程现金：疗程单，支付方式的【是否散客支付】为是
     * 2）是否散客支付=赠送
     */
    private String getOrderCategory(PayMethodsVO payMethods, Integer productType) {
        //是否散客支付 设置的不选择（2） 则表示赠送
        if (payMethods.getIzWalkin() == 2) {
            return OrderCategoryEnums.FIVE.getMessage();
        }
        if (ProductTypeEnums.COURSE.getCode().equals(productType)) {
            if (payMethods.getIzWalkin() !=null && payMethods.getIzWalkin() == 1) {
                // 散客单次疗程现金：疗程单，支付方式的【是否散客支付】为是
                return OrderCategoryEnums.FOUR.getMessage();
            } else {
                // 会员单次疗程抵扣：疗程单，支付方式的【是否散客支付】为否
                return OrderCategoryEnums.TWO.getMessage();
            }
        } else {
            // 非疗程商品，都属于项目
            if (payMethods.getIzWalkin() !=null && payMethods.getIzWalkin() ==1) {
                // 散客单次项目现金：项目单，支付方式的【是否散客支付】为是
                return OrderCategoryEnums.THREE.getMessage();
            } else {
                // 会员单次项目抵扣：项目单，支付方式的【是否散客支付】为否
                return OrderCategoryEnums.ONE.getMessage();
            }
        }
    }
}
