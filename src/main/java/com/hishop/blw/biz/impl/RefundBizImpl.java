package com.hishop.blw.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.blw.biz.*;
import com.hishop.blw.config.SnowPrefixConstants;
import com.hishop.blw.constants.BasicConstants;
import com.hishop.blw.enums.*;
import com.hishop.blw.model.po.card.CardPO;
import com.hishop.blw.model.po.order.OrderItemsPO;
import com.hishop.blw.model.po.order.OrdersCreatePO;
import com.hishop.blw.model.po.order.PayMethodPO;
import com.hishop.blw.model.po.order.SettlementCreatePO;
import com.hishop.blw.model.po.refund.*;
import com.hishop.blw.model.vo.order.OrderContentVO;
import com.hishop.blw.model.vo.order.OrderItemsVO;
import com.hishop.blw.model.vo.order.PaymentsVO;
import com.hishop.blw.model.vo.order.PointsDetailsVO;
import com.hishop.blw.model.vo.refund.*;
import com.hishop.blw.model.vo.store.StoreVO;
import com.hishop.blw.model.vo.user.UsersVO;
import com.hishop.blw.repository.dto.*;
import com.hishop.blw.repository.entity.*;
import com.hishop.blw.repository.service.*;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 售后表 业务逻辑实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Slf4j
@Service
public class RefundBizImpl implements RefundBiz {

    @Resource
    private RefundService refundService;
    @Resource
    private RefundItemsService refundItemsService;
    @Resource
    private RefundReasonsService refundReasonsService;
    @Resource
    private ProductsService productsService;
    @Resource
    private UserServicesService userServicesService;
    @Resource
    private UserPointsService userPointsService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private OrderItemsService orderItemsService;
    @Resource
    private OrderItemsExtendService orderItemsExtendService;
    @Resource
    private OrderSettingsService orderSettingsService;
    @Resource
    private PointsDetailsService pointsDetailsService;
    @Resource
    private PaymentsService paymentsService;
    @Resource
    private PayMethodsService payMethodsService;
    @Resource
    private MemberCardsService memberCardsService;
    @Resource
    private CardRecordsService cardRecordsService;
    @Resource
    private ReservationServicesService reservationServicesService;
    @Resource
    private CardsService cardsService;
    @Resource
    private UsersService usersService;

    @Resource
    private StoreBiz storeBiz;
    @Resource
    private DepartmentBiz departmentBiz;
    @Resource
    private OrdersBiz ordersBiz;

    @Resource
    private SnowflakeGenerator snowflakeGenerator;

    @Resource
    private UsersBiz usersBiz;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private AliSmsBiz aliSmsBiz;
    @Resource
    private RefundItemsExtendService refundItemsExtendService;
    @Resource
    private SplitOrderPriceService splitOrderPriceService;
    @Resource
    private ProductsBiz productsBiz;
    /**
     * 手续费商品ID
     */
    private final static Long COMMISSION_PRODUCT_ID = 0L;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(RefundCreatePO refundCreatePO) {
        Long handlerId = LoginUserUtil.getLoginUser().getUserId();

        CalculateRefundAmountPO calculateRefundAmountPO = new CalculateRefundAmountPO();
        calculateRefundAmountPO.setOrderItemId(refundCreatePO.getOrderItemId());
        calculateRefundAmountPO.setQuantity(refundCreatePO.getRefundQuantity());
        BigDecimal applyAmount = this.calculateRefundAmount(calculateRefundAmountPO);
        refundCreatePO.setApplyAmount(applyAmount);

        Orders order = this.getAndCheckByRefundOrder(refundCreatePO);
        OrderItems orderItem = this.getAndCheckByRefundOrderItem(refundCreatePO);
        Products product = this.getAndCheckByRefundProduct(refundCreatePO, order, orderItem);
        BigDecimal canRefundQuantity = this.getCanRefundQuantity(orderItem);

        ProductTypeEnums productType = ProductTypeEnums.codeToEnum(product.getProductType());
        if(refundCreatePO.getRefundQuantity().compareTo(canRefundQuantity) > 0){
            throw new BusinessException("申请退款数量超过可退数量，剩余可退数量：" + canRefundQuantity);
        }
        if(ProductTypeEnums.COURSE == productType){
            if(!refundCreatePO.getRefundQuantity().equals(canRefundQuantity)){
                throw new BusinessException("疗程不允许修改数量，可退数量：" + canRefundQuantity);
            }
            // 疗程默认退最大数量
            refundCreatePO.setRefundQuantity(canRefundQuantity);
        }
//        this.checkReservationServices(refundCreatePO);
//        this.checkRefundStatus(refundCreatePO);

//        Long refundId = snowflakeGenerator.next();
//        Long refundItemId = ;

        List<RefundItemsExtend> refundItemsExtends = null;
        if(ProductTypeEnums.COURSE == productType){
            refundItemsExtends = this.initRefundItemsExtendList(orderItem, refundCreatePO.getRefundQuantity());
        }

        // 判断是否为疗程、项目；如果是，扣减可用次数
        if (ProductTypeEnums.PROJECT == productType) {
            // 项目，直接扣减服务可用次数
            this.userServicesService.addRefundQuantity(order.getId(), orderItem.getId(), product.getId(),
                    refundCreatePO.getRefundQuantity());
        } else if (ProductTypeEnums.COURSE == productType) {
            // 在用户可用服务中，扣减可用次数，增加售后次数
            refundItemsExtends.stream()
                    .filter(rie -> ProductTypeEnums.PROJECT == ProductTypeEnums.codeToEnum(rie.getProductType()))
                    .forEach(rie -> this.userServicesService.addRefundQuantity(order.getId(), orderItem.getId(), rie.getProductId(), rie.getRefundQuantity()));

        }

        Refund refund = new Refund();
        refund.setRefundNo(SnowPrefixConstants.REFUND + snowflakeGenerator.next());
        refund.setUserId(order.getUserId() == null ? 0L : order.getUserId());
        refund.setApplyUserId(LoginUserUtil.getLoginUser().getUserId());
        refund.setOrderId(order.getId());
        refund.setOrderNo(order.getOrderNo());
        refund.setStoreId(order.getStoreId());
        refund.setStatus(RefundStatusEnums.PENDING_APPROVAL.getCode());
        refund.setType(handlerId.equals(order.getUserId()) ? RefundTypeEnums.USER.getCode()
                : RefundTypeEnums.EMPLOYEE.getCode());
        refund.setApplyAmount(refundCreatePO.getApplyAmount());
        // 退款金额等于申请金额
        // refund.setRefundAmount(refundCreatePO.getApplyAmount());
        refund.setApplyPoint(refundCreatePO.getApplyPoint());
        refund.setApplyTime(new Date());
        refund.setRefundReason(refundCreatePO.getRefundReason());
        refund.setAdditionalDescription(refundCreatePO.getAdditionalDescription());
        refund.setAdditionalEvidenceUrls(refundCreatePO.getAdditionalEvidenceUrls());
        refundService.save(refund);

        Long actualPoints = orderItem.getActualPoints();
        Integer quantity = orderItem.getQuantity();

        RoundingMode roundingMode = canRefundQuantity.compareTo(BigDecimal.valueOf(quantity)) == 0 ? RoundingMode.UP : RoundingMode.DOWN;
        Long refundablePoints = new BigDecimal(actualPoints).divide(new BigDecimal(quantity), 0, roundingMode)
                .longValue();

        RefundItems refundItem = new RefundItems();
        //生成唯一id，报表统计需要这个id全局唯一
        refundItem.setId(snowflakeGenerator.next());
        refundItem.setRefundId(refund.getId());
        refundItem.setOrderId(order.getId());
        refundItem.setOrderItemId(orderItem.getId());
        refundItem.setProductId(orderItem.getProductId());
        refundItem.setApplyAmount(refundCreatePO.getApplyAmount());
        refundItem.setRefundablePoints(refundablePoints);
        refundItem.setRefundQuantity(refundCreatePO.getRefundQuantity());
        //设置商品名称,类型和商品所属部门（后续统计用到）
        refundItem.setProductName(orderItem.getProductName());
        refundItem.setProjectDepartmentId(orderItem.getProjectDepartmentId());
        refundItem.setProductType(orderItem.getProductType());
        refundItemsService.save(refundItem);

        if(ProductTypeEnums.COURSE == productType){
            // 把售后ID和售后明细ID写进去
            for (RefundItemsExtend refundItemsExtend : refundItemsExtends) {
                refundItemsExtend.setRefundId(refund.getId());
                refundItemsExtend.setRefundItemId(refundItem.getId());
            }
            // 保存退款子项拓展信息
            refundItemsExtendService.saveBatch(refundItemsExtends);
        }

        try {
            operationLogService.saveOperationLog(refund.getId(),
                    String.format("退款申请：{%s}", refund.getRefundNo()),
                    "退款申请");
        } catch (Exception e) {
            log.error("退款申请保存日志失败！", e);
        }
    }


    private List<RefundItemsExtend> initRefundItemsExtendList(OrderItems orderItems, BigDecimal refundQuantity) {
        // 订单明细拓展信息
        List<OrderItemsExtend> orderItemsExtends = orderItemsExtendService.listByOrderItemId(orderItems.getId());

        // 子商品集合
        Map<Long, List<OrderItemsExtend>> childrenMap = orderItemsExtends.stream()
                .filter(oie -> Objects.nonNull(oie.getParentProductId()))
                .collect(Collectors.groupingBy(OrderItemsExtend::getParentProductId));

        // 父商品集合
        Map<Long, OrderItemsExtend> parentMap = orderItemsExtends.stream().collect(Collectors.toMap(OrderItemsExtend::getId, Function.identity()));

        // 用户服务信息
        List<UserServices> userServices = userServicesService.listByOrderItemId(orderItems.getId());

        // 用户服务信息转换为map
        Map<Long, UserServices> userServicesMap = userServices.stream()
               .collect(Collectors.toMap(UserServices::getProductId, Function.identity(), (a, b) -> {
                   if(a.getAvailableQuantity().compareTo(BigDecimal.ZERO) == 0){
                       return b;
                   }
                   return a;
               }));

        List<RefundItemsExtend> refundItemsExtends = new ArrayList<>();

        for (OrderItemsExtend orderItemsExtend : orderItemsExtends) {

            if(orderItemsExtend.getParentProductId() != null && orderItemsExtend.getParentProductId() != 0L){
                // 不处理第二级的数据，
                // 第二级的数据在遍历到一级数据之后，直接处理。
                continue;
            }

            // 这里先处理【订单明细拓展表】里面的一级项
            RefundItemsExtend rie = this.initRefundItemsExtend(orderItemsExtend, BigDecimal.valueOf(orderItemsExtend.getQuantity()));
            refundItemsExtends.add(rie);

            ProductTypeEnums productType = ProductTypeEnums.codeToEnum(orderItemsExtend.getProductType());
            if(ProductTypeEnums.PRODUCT == productType || ProductTypeEnums.DISH == productType){
                // 一级项是 商品 或者是 菜品
                // 拓展表的数量为：订单明细拓展表的数量 / 订单明细的数量 * 售后数量
                BigDecimal refundExtQuantity;
                Long parentProductId = orderItemsExtend.getParentProductId();
                if(parentProductId != null && parentProductId != 0L){
                    OrderItemsExtend parent = parentMap.get(parentProductId);
                    if(parent!= null){
                        refundExtQuantity = BigDecimal.valueOf(orderItemsExtend.getQuantity())
                               .divide(BigDecimal.valueOf(parent.getQuantity()), 0, RoundingMode.DOWN)
                               .multiply(refundQuantity);
                    }else{
                        refundExtQuantity = BigDecimal.ZERO;
                    }
                } else {
                    refundExtQuantity = BigDecimal.valueOf(orderItemsExtend.getQuantity())
                            .divide(BigDecimal.valueOf(orderItems.getQuantity()), 0 , RoundingMode.DOWN)
                            .multiply(refundQuantity);
                }
                rie.setRefundQuantity(refundExtQuantity);

            } else if(ProductTypeEnums.PROJECT == productType){
                // 一级项是 项目
                // 次数为用户服务表的可用次数

                UserServices userService = userServicesMap.get(orderItemsExtend.getProductId());
                BigDecimal projectRefundQty = userService.getAvailableQuantity();
                rie.setRefundQuantity(projectRefundQty);           // 退款数量

                // 处理项目下的商品
                List<OrderItemsExtend> childrenProductList = childrenMap.get(orderItemsExtend.getId());
                if(CollUtil.isNotEmpty(childrenProductList)){
                    for (OrderItemsExtend childrenProduct : childrenProductList) {

                        // 子商品的数量为：子商品的数量 / 父项目的数量 * 父项目的售后数量
                        BigDecimal childrenProductQty = BigDecimal.valueOf(childrenProduct.getQuantity())
                                .divide(BigDecimal.valueOf(orderItemsExtend.getQuantity()), 0, RoundingMode.DOWN)
                                .multiply(projectRefundQty);
                        refundItemsExtends.add(this.initRefundItemsExtend(childrenProduct, childrenProductQty));
                    }
                }
            }

        }

        return refundItemsExtends;




    }

    private RefundItemsExtend initRefundItemsExtend(OrderItemsExtend orderItemsExtend, BigDecimal refundQuantity){
        RefundItemsExtend childrenRefundItemExtend = new RefundItemsExtend();
        //生成唯一id，统计需要这个id全局唯一
        childrenRefundItemExtend.setId(snowflakeGenerator.next());
        childrenRefundItemExtend.setOrderId(orderItemsExtend.getOrderId());        // 订单ID
        childrenRefundItemExtend.setOrderItemId(orderItemsExtend.getOrderItemId());// 订单子项ID
        childrenRefundItemExtend.setOrderItemExtentId(orderItemsExtend.getId());   // 订单子项扩展ID
        childrenRefundItemExtend.setProductId(orderItemsExtend.getProductId());    // 商品ID
        childrenRefundItemExtend.setProductName(orderItemsExtend.getProductName());
        childrenRefundItemExtend.setProductType(orderItemsExtend.getProductType());
        childrenRefundItemExtend.setProjectDepartmentId(orderItemsExtend.getProjectDepartmentId());
        childrenRefundItemExtend.setRefundAmount(BigDecimal.ZERO);                 // 退款金额
        childrenRefundItemExtend.setRefundPoints(0L);                              // 退款积分
        childrenRefundItemExtend.setRefundPointsAmount(BigDecimal.ZERO);           // 退款积分金额
        childrenRefundItemExtend.setRefundQuantity(refundQuantity);           // 退款数量
        childrenRefundItemExtend.setIzGift(orderItemsExtend.getIzGift());           // 退款数量
        return childrenRefundItemExtend;
    }


    /**
     * 获取订单的同时，校验订单是否满足售后发起的条件
     * 1. 检查订单是否存在
     * 2. 检查订单状态
     * 3. 检查订单类型
     * 
     * @param refundCreatePO
     * @return
     */
    private Orders getAndCheckByRefundOrder(RefundCreatePO refundCreatePO) {
        Orders orders = ordersService.getById(refundCreatePO.getOrderId());
        if (Objects.isNull(orders)) {
            throw new BusinessException("订单不存在");
        }
        if (OrderTypeEnums.COMMISSION == OrderTypeEnums.codeToEnum(orders.getOrderType())) {
            throw new BusinessException("订单类型错误");
        }
        if (OrderStatusEnums.UN_PAY == OrderStatusEnums.codeToEnum(orders.getOrderStatus())) {
            throw new BusinessException("待结算的订单不能进行售后");
        }
        if (OrderStatusEnums.CANCEL == OrderStatusEnums.codeToEnum(orders.getOrderStatus())) {
            throw new BusinessException("已取消的订单不能进行售后");
        }
        if (OrderStatusEnums.CLOSE == OrderStatusEnums.codeToEnum(orders.getOrderStatus())) {
            throw new BusinessException("已关闭的订单不能进行售后");
        }
        return orders;
    }

    /**
     * 获取订单明细的同时，校验订单是否满足售后发起的条件
     * 1. 检查订单明显是否存在
     * 
     * @param refundCreatePO
     * @return
     */
    private OrderItems getAndCheckByRefundOrderItem(RefundCreatePO refundCreatePO) {
        OrderItems orderItems = orderItemsService.getById(refundCreatePO.getOrderItemId());
        if (Objects.isNull(orderItems)) {
            throw new BusinessException("订单明细不存在");
        }
        return orderItems;
    }

    /**
     * 获取商品的同时，校验商品是否满足售后发起的条件
     * 1. 如果是订单明细是商品，需要验证当前时间是否在 订单支付时间+商品有效期 之内
     * 2. 如果是订单明细是服务，疗程，需要验证当前时间是否在 订单支付时间+售后期（天） 之内
     * 3. 检查商品是否已经预约了项目
     * 4. 判断是订单内，是否足够数量的商品可以售后
     *
     * @param refundCreatePO
     * @param order
     * @param orderItem
     * @return
     */
    private Products getAndCheckByRefundProduct(RefundCreatePO refundCreatePO, Orders order, OrderItems orderItem) {
        Products products = productsService.getById(orderItem.getProductId());
        if (Objects.isNull(products)) {
            throw new BusinessException("商品不存在");
        }

        Date payDate = order.getPayDate();
        switch (ProductTypeEnums.codeToEnum(products.getProductType())) {
            case DISH:
                break;
            case SET_MENU:
                break;
            case PRODUCT:
                if (refundCreatePO.getIsMemberCreate()) {
                    // 会员端发起的售后，需要检查商品有效期
                    Date productDeadline = DateUtil.offsetDay(payDate, products.getValidityDays());
                    if (new Date().after(productDeadline)) {
                        throw new BusinessException("当前时间已超过商品允许退款的有效期");
                    }
                }
                break;
            case PROJECT:
            case COURSE:
                if (refundCreatePO.getIsMemberCreate()) {
                    // 会员端发起的售后，需要检查项目/疗程有效期
                    OrderSettings orderSettings = orderSettingsService.get();
                    Date projectDeadline = DateUtil.offsetDay(payDate, orderSettings.getRefundTime());
                    if (new Date().after(projectDeadline)) {
                        throw new BusinessException("当前时间已超过项目/疗程允许退款的有效期");
                    }
                }

              /*  List<ProjectTileDTO> projectTileList = productsService.listProject(products.getId());
                List<Long> projectIds = projectTileList.stream().map(ProjectTileDTO::getId)
                        .collect(Collectors.toList());
                List<UserServices> userServicesList = userServicesService.list(order.getId(), orderItem.getOrderId(),
                        projectIds);
                if (CollUtil.isNotEmpty(userServicesList)) {
                    boolean used = userServicesList.stream()
                            .anyMatch(userServices -> userServices.getUsedQuantity().compareTo(BigDecimal.ZERO) > 0);
                    if (used) {
                        throw new BusinessException("项目/疗程已预约或已使用，不能进行售后");
                    }
                }*/
                break;
            default:
                throw new BusinessException("不支持的商品类型");
        }

        return products;
    }

    /**
     * 检查可退数量
     *
     * @param orderItem
     * @return
     */
    private BigDecimal getCanRefundQuantity(OrderItems orderItem) {
        // 计算已退数量
        List<RefundItems> existingRefunds = refundItemsService.listByOrderItemId(orderItem.getId());
        BigDecimal totalRefunded = existingRefunds.stream()
                .map(RefundItems::getRefundQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (ProductTypeEnums.PROJECT == ProductTypeEnums.codeToEnum(orderItem.getProductType())) {
            // 计算项目可退数量
            List<UserServices> userServicesList = userServicesService.listByOrderItemId(orderItem.getId());
            BigDecimal totalUsed = userServicesList.stream()
                    .map(UserServices::getUsedQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 可退数量 = 商品总数 - 已退数量 - 已使用数量
            return BigDecimal.valueOf(orderItem.getQuantity())
                    .subtract(totalRefunded)
                    .subtract(totalUsed);


        } else if(ProductTypeEnums.COURSE == ProductTypeEnums.codeToEnum(orderItem.getProductType())){
            // 计算疗程可退数量
            List<UserServices> userServicesList = userServicesService.listByOrderItemId(orderItem.getId());
            return userServicesList
                    .stream()
                    .map(userServices -> {
                        BigDecimal totalQuantity = userServices.getAvailableQuantity()
                                .add(userServices.getUsedQuantity())
                                .add(userServices.getRefundQuantity());

                        BigDecimal singleQuantity = totalQuantity.divide(BigDecimal.valueOf(orderItem.getQuantity()), 0, RoundingMode.UP);
                        BigDecimal availableQuantity = userServices.getAvailableQuantity();
                        return availableQuantity.divide(singleQuantity, 0, RoundingMode.UP);
                    })
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
        } else {
            // 计算商品、菜品、套餐可退数量
            return BigDecimal.valueOf(orderItem.getQuantity()).subtract(totalRefunded);
        }


    }

    /**
     * 检查是否存在待处理的售后单
     * 
     * @param refundCreatePO
     */
    private void checkRefundStatus(RefundCreatePO refundCreatePO) {
        boolean exists = refundService.existsByOrderIdAndStatus(refundCreatePO.getOrderId(),
                RefundStatusEnums.PENDING_APPROVAL.getCode());
        if (exists) {
            throw new BusinessException("存在待处理的售后单，不能再次申请售后");
        }
    }

    /**
     * 检查预约的服务是否满足售后条件
     * 
     * @param refundCreatePO
     */
    private void checkReservationServices(RefundCreatePO refundCreatePO) {
        ReservationServices query = new ReservationServices();
        query.setOrderItemId(refundCreatePO.getOrderItemId());
        List<ReservationServices> list = reservationServicesService.listEntity(query);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        boolean pass = list.stream()
                .map(ReservationServices::getStatus)
                .map(ReservationServicesEnums.Status::getByCode)
                .anyMatch(rs -> ReservationServicesEnums.Status.PENDING == rs
                        || ReservationServicesEnums.Status.COMPLETED == rs);
        if (pass) {
            throw new BusinessException("存在待完成或已完成的预约，不能进行售后");
        }
    }

    @Override
    public void update(RefundUpdatePO refundUpdatePO) {
        Refund entity = refundService.getById(refundUpdatePO.getId());
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        Refund updateEntity = BeanUtil.copyProperties(refundUpdatePO, Refund.class);
        refundService.updateById(updateEntity);
    }

    @Override
    public void deleteById(Long id) {
        refundService.removeById(id);
    }

    @Override
    public List<RefundPageVO> list(RefundQueryPO refundQueryPO) {

        Refund entity = BeanUtil.copyProperties(refundQueryPO, Refund.class);
        List<Refund> list = refundService.listEntity(entity);
        return BeanUtil.copyToList(list, RefundPageVO.class);
    }

    @Override
    public PageResult<RefundPageVO> pageList(RefundQueryPO queryPO) {
        RefundQueryDTO query = BeanUtil.copyProperties(queryPO, RefundQueryDTO.class);
        Page<RefundResultDTO> result = refundService.pageList(queryPO.buildPage(), query);
        PageResult<RefundPageVO> pageResult = PageResultHelper.transfer(result, RefundPageVO.class);
        List<RefundPageVO> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        storeBiz.fullName(list, RefundPageVO::getStoreId, RefundPageVO::setStoreName);
        departmentBiz.fullName(list, RefundPageVO::getDepartmentId, RefundPageVO::setDepartmentName);

        List<Long> orderIds = list.stream().map(RefundPageVO::getOrderId).distinct().collect(Collectors.toList());
        //Map<Long, Orders> ordersMap = ordersService.listByIds(orderIds).stream()
        //        .collect(Collectors.toMap(Orders::getId, Function.identity()));
        List<Payments> orderPaymentList = paymentsService.listByOrderIdList(orderIds, false);
        Map<Long, List<Payments>> orderPaymentMap = orderPaymentList.stream()
                .filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.groupingBy(Payments::getOrderId));

        List<Long> refundIds = list.stream().map(RefundPageVO::getId).collect(Collectors.toList());
        List<Payments> refundPaymentList = paymentsService.listByRefundIds(refundIds);
        Map<Long, List<Payments>> refundPaymentMap = refundPaymentList.stream()
                .collect(Collectors.groupingBy(Payments::getRefundId));
        for (RefundPageVO refundPageVO : list) {
            List<Payments> curOrderPaymentList = orderPaymentMap.get(refundPageVO.getOrderId());
            //Orders order = ordersMap.get(refundPageVO.getOrderId());
            if (CollUtil.isNotEmpty(curOrderPaymentList)) {
                String payMethod = curOrderPaymentList.stream()
                        .map(Payments::getPayMethod)
                        .distinct()
                        .collect(Collectors.joining(","));
                //支付方式里面已经有积分支付，不需要在组装
                //if (order != null && (order.getPoints() != null && order.getPoints() > 0)) {
                //    if (payMethod.isEmpty()) {
                //        payMethod = "积分";
                //    } else {
                //        payMethod += ",积分";
                //    }
                //}
                refundPageVO.setPayMethod(payMethod);
            }

            List<Payments> curRefundPaymentList = refundPaymentMap.get(refundPageVO.getId());
            String refundPayMethod = "";
            if (CollUtil.isNotEmpty(curRefundPaymentList)) {
                refundPayMethod = curRefundPaymentList.stream()
                        .map(payments -> payments.getPayMethod() + ":" + payments.getAmount())
                        .collect(Collectors.joining(","));
            }

            if (refundPageVO.getRefundPoints() != null && refundPageVO.getRefundPoints() > 0) {
                if (refundPayMethod.isEmpty()) {
                    refundPayMethod = "积分:" + refundPageVO.getRefundPoints();
                } else {
                    refundPayMethod += ",积分:" + refundPageVO.getRefundPoints();
                }
            }
            //重新设置总的可退积分 = 可退单价*数量,数据库保存的RefundablePoints字段是单价
            Long refundablePoints = BigDecimal.valueOf(refundPageVO.getRefundablePoints())
                    .multiply(refundPageVO.getRefundQuantity())
                    .setScale(0, RoundingMode.DOWN)
                    .longValue();
            refundPageVO.setRefundablePoints(refundablePoints);
            refundPageVO.setRefundPayMethod(refundPayMethod);
        }

        return pageResult;
    }

    @Override
    public RefundPageVO getById(Long id) {
        RefundResultDTO result = refundService.getRefundDetailById(id);
        return BeanUtil.copyProperties(result, RefundPageVO.class);
    }

//    @Override
//    public List<OrderContentVO> filterRefunded(List<OrderContentVO> reservationOrder) {
//        if (CollUtil.isEmpty(reservationOrder)) {
//            return Collections.emptyList();
//        }
//        List<Long> orderIds = reservationOrder.stream().map(OrderContentVO::getOrderId).collect(Collectors.toList());
//        List<Refund> refunds = refundService.listByOrderIds(orderIds, RefundStatusEnums.PASSED.getCode());
//        if (CollUtil.isEmpty(refunds)) {
//            return Collections.emptyList();
//        }
//        List<Long> refundIds = refunds.stream().map(Refund::getId).collect(Collectors.toList());
//        List<RefundItems> refundItems = refundItemsService.listByRefundIds(refundIds);
//        if (CollUtil.isEmpty(refundItems)) {
//            return Collections.emptyList();
//        }
//
//        Map<Long, Refund> refundMap = refunds.stream().collect(Collectors.toMap(Refund::getOrderId, refund -> refund));
//        Map<Long, List<RefundItems>> refundItemsMap = refundItems.stream()
//                .collect(Collectors.groupingBy(RefundItems::getOrderItemId));
//
//        List<OrderContentVO> result = new ArrayList<>();
//        for (OrderContentVO orderContent : reservationOrder) {
//            Refund refund = refundMap.get(orderContent.getOrderId());
//            if (refund == null) {
//                result.add(orderContent);
//                continue;
//            }
//
//            List<OrderItemsVO> orderItems = orderContent.getItems().stream()
//                    .peek(item -> {
//                        List<RefundItems> refundItemList = refundItemsMap.get(item.getId());
//                        Integer refundQuantitySum = refundItemList.stream()
//                                .map(RefundItems::getRefundQuantity)
//                                .reduce(0, Integer::sum);
//                        item.setQuantity(item.getQuantity() - refundQuantitySum);
//                    })
//                    .filter(item -> item.getQuantity() > 0)
//                    .collect(Collectors.toList());
//            if (CollUtil.isEmpty(orderItems)) {
//                continue;
//            }
//            orderContent.setItems(orderItems);
//            result.add(orderContent);
//        }
//        return result;
//    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pass(RefundPassPO po) {
        Long points = po.getPoints();
        BigDecimal refundAmount = po.getRefundAmount();
        BigDecimal commission = po.getCommission();
        Refund refund = this.getRefund(po.getId());
        RefundStatusEnums refundStatus = RefundStatusEnums.getEnumByCode(refund.getStatus());
        if (RefundStatusEnums.PENDING_APPROVAL != refundStatus) {
            throw new BusinessException("此退款单已被处理，请刷新页面。");
        }

        BigDecimal totalRefundAmount = refundAmount;
        if (Objects.nonNull(commission)) {
            totalRefundAmount = totalRefundAmount.add(commission);
        }

        if (refund.getApplyAmount().compareTo(totalRefundAmount) < 0) {
            throw new BusinessException("手续费+退款金额，不能大于申请的金额");
        }

        refund.setCommission(commission);
        refund.setCommissionPayMethod(po.getPayMethodId());
        refund.setCommissionCardId(po.getCardId());
        refund.setRefundAmount(refundAmount);
        refund.setStatus(RefundStatusEnums.PASSED.getCode());
        refund.setRefundPoints(points);
        refund.setHandlerId(LoginUserUtil.getLoginUser().getUserId());
        refund.setHandlerTime(new Date());
        refund.setHandlerRemark(po.getHandlerRemark());
        refundService.updateById(refund);

        RefundItems query = new RefundItems();
        query.setRefundId(refund.getId());
        List<RefundItems> refundItemsList = refundItemsService.listEntity(query);

        List<Long> orderItemIds = refundItemsList.stream().map(RefundItems::getOrderItemId).distinct()
                .collect(Collectors.toList());
        List<OrderItems> oiList = orderItemsService.listByIds(orderItemIds);
        Map<Long, OrderItems> oiMap = oiList.stream().collect(Collectors.toMap(OrderItems::getId, oi -> oi));
        //总退款积分金额
        BigDecimal totalPointAmount = BigDecimal.ZERO;
        for (RefundItems refundItems : refundItemsList) {
            refundItems.setRefundAmount(refundAmount);

            // 如果存在退还积分，那么需要计算积分的价值
            if (po.getPoints() != null && po.getPoints() > 0) {
                refundItems.setRefundPoints(po.getPoints());

                OrderItems oi = oiMap.get(refundItems.getOrderItemId());
                Long actualPoints = oi.getActualPoints();
                BigDecimal actualPointsAmount = oi.getActualPointsAmount();
                if (actualPoints != null && actualPoints > 0 && actualPointsAmount != null
                        && actualPointsAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal refundPointsAmount = actualPointsAmount
                            .multiply(BigDecimal.valueOf(po.getPoints()))
                            .divide(BigDecimal.valueOf(actualPoints))
                            .setScale(4, RoundingMode.DOWN);
                    refundItems.setRefundPointsAmount(refundPointsAmount);
                    totalPointAmount = totalPointAmount.add(refundPointsAmount);
                }
            }
        }
        refundItemsService.updateBatchById(refundItemsList);

        for (RefundItems refundItems : refundItemsList) {
            OrderItems orderItems = oiMap.get(refundItems.getOrderItemId());
            int refundedStock = refundItems.getRefundQuantity().setScale(0, RoundingMode.DOWN).intValue();
            productsService.addStock(orderItems.getProductId(), refundedStock);
        }


        refundItemsList.forEach(this::handleRefundItemExtendAmount);

        for (RefundItems refundItems : refundItemsList) {
            OrderItems orderItems = oiMap.get(refundItems.getOrderItemId());
            orderItems.setRefundAmount(orderItems.getRefundAmount().add(refundItems.getRefundAmount()).add(refundItems.getRefundPointsAmount()));// 退款金额，包含积分退的金额
        }
        orderItemsService.updateBatchById(oiList);

       /* // 在订单明细中，记录售后金额
        for (RefundItems refundItems : refundItemsList) {
            OrderItems orderItems = oiMap.get(refundItems.getOrderItemId());
            orderItems.setRefundAmount(orderItems.getRefundAmount().add(refundItems.getRefundAmount()).add(refundItems.getRefundPointsAmount()));// 退款金额，包含积分退的金额

            // 计算订单子项扩张表中退款金额和退款积分
            Long orderItemId = refundItems.getOrderItemId();          // 订单子项ID
            Long orderId = refundItems.getOrderId();                  // 订单ID
            Long refundId = refundItems.getRefundId();                // 退款单ID
            Long refundItemsId = refundItems.getId();                 // 退款单子项ID
            Integer refundQuantity = refundItems.getRefundQuantity(); // 退款数量

            List<OrderItemsExtend> orderItemsExtends = orderItemsExtendService.listByOrderItemId(orderItemId);
            if(CollectionUtil.isNotEmpty(orderItemsExtends) && Objects.nonNull(orderItems)){
                //赠品单独处理
                List<OrderItemsExtend> giftOrderItemsExtends = orderItemsExtends.stream()
                        .filter(orderItemsExtend -> orderItemsExtend.getIzGift() == null || orderItemsExtend.getIzGift())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(giftOrderItemsExtends)) {
                    for (OrderItemsExtend orderItemsExtend : giftOrderItemsExtends) {
                        // refund_amount 退款金额，包括了积分退的金额
                        Integer quantity = orderItems.getQuantity();
                        Integer refundItemsQuantity = (orderItemsExtend.getQuantity() / quantity) * refundQuantity;
                        RefundItemsExtend refundItemsExtend = new RefundItemsExtend();
                        //生成唯一id，统计需要这个id全局唯一
                        refundItemsExtend.setId(snowflakeGenerator.next());
                        refundItemsExtend.setRefundId(refundId);                            // 退款单ID
                        refundItemsExtend.setRefundItemId(refundItemsId);                   // 退款单子项ID
                        refundItemsExtend.setOrderId(orderId);                              // 订单ID
                        refundItemsExtend.setOrderItemId(orderItemId);                      // 订单子项ID
                        refundItemsExtend.setOrderItemExtentId(orderItemsExtend.getId());   // 订单子项扩展ID
                        refundItemsExtend.setProductId(orderItemsExtend.getProductId());    // 商品ID
                        refundItemsExtend.setProductName(orderItemsExtend.getProductName());
                        refundItemsExtend.setProductType(orderItemsExtend.getProductType());
                        refundItemsExtend.setProjectDepartmentId(orderItemsExtend.getProjectDepartmentId());
                        refundItemsExtend.setRefundAmount(BigDecimal.ZERO);                 // 退款金额
                        refundItemsExtend.setRefundPoints(0L);                              // 退款积分
                        refundItemsExtend.setRefundPointsAmount(BigDecimal.ZERO);           // 退款积分金额
                        refundItemsExtend.setRefundQuantity(BigDecimal.valueOf(refundItemsQuantity));           // 退款数量
                        refundItemsExtendService.add(refundItemsExtend);
                        orderItemsExtend.setRefundAmount(BigDecimal.ZERO);
                    }
                    orderItemsExtendService.updateBatchById(giftOrderItemsExtends);
                }
                //非赠品处理
                List<OrderItemsExtend> normalOrderItemsExtends = orderItemsExtends.stream()
                        .filter(orderItemsExtend -> orderItemsExtend.getIzGift() != null && !orderItemsExtend.getIzGift())
                        .collect(Collectors.toList());
                BigDecimal leftRefundAmount = refundItems.getRefundAmount()==null?BigDecimal.ZERO:refundItems.getRefundAmount();
                BigDecimal leftRefundPointsAmount = refundItems.getRefundPointsAmount()==null?BigDecimal.ZERO:refundItems.getRefundPointsAmount();
                Long leftRefundPoints = refundItems.getRefundPoints()==null?0L:refundItems.getRefundPoints();
                for (int i = 0 ;i <  normalOrderItemsExtends.size() ; i++) {
                    OrderItemsExtend orderItemsExtend = normalOrderItemsExtends.get(i);
                    // refund_amount 退款金额，包括了积分退的金额
                    Integer quantity = orderItems.getQuantity();
                    Integer refundItemsQuantity = (orderItemsExtend.getQuantity() / quantity) * refundQuantity;
                    RefundItemsExtend refundItemsExtend = new RefundItemsExtend();
                    refundItemsExtend.setId(snowflakeGenerator.next());
                    refundItemsExtend.setRefundId(refundId);                            // 退款单ID
                    refundItemsExtend.setRefundItemId(refundItemsId);                   // 退款单子项ID
                    refundItemsExtend.setOrderId(orderId);                              // 订单ID
                    refundItemsExtend.setOrderItemId(orderItemId);                      // 订单子项ID
                    refundItemsExtend.setOrderItemExtentId(orderItemsExtend.getId());   // 订单子项扩展ID
                    refundItemsExtend.setProductId(orderItemsExtend.getProductId());    // 商品ID
                    refundItemsExtend.setProductName(orderItemsExtend.getProductName());
                    refundItemsExtend.setProductType(orderItemsExtend.getProductType());
                    refundItemsExtend.setProjectDepartmentId(orderItemsExtend.getProjectDepartmentId());
                    refundItemsExtend.setRefundQuantity(BigDecimal.valueOf(refundItemsQuantity));           // 退款数量
                    if (i == normalOrderItemsExtends.size() - 1) {
                        //最后一项直接减
                        refundItemsExtend.setRefundAmount(leftRefundAmount);
                        refundItemsExtend.setRefundPointsAmount(leftRefundPointsAmount);
                        refundItemsExtend.setRefundPoints(leftRefundPoints);
                        BigDecimal oldRefundAmount = orderItemsExtend.getRefundAmount() == null ? BigDecimal.ZERO : orderItemsExtend.getRefundAmount();
                        orderItemsExtend.setRefundAmount(oldRefundAmount.add(leftRefundAmount));
                        refundItemsExtendService.add(refundItemsExtend);
                        break;
                    }
                    // 平摊金额 = 退款金额 * 订单扩展子项的实付金额 / 订单子项实付金额
                    if (leftRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal refundItemAmount = orderItemsExtend.getAmount()
                                .multiply(refundItems.getRefundAmount())
                                .divide(orderItems.getActualAmount())
                                .setScale(4, RoundingMode.DOWN);
                        //由于一个子项可以退多次，这里要加上以前退的（如果有）
                        BigDecimal oldRefundItemAmount = orderItemsExtend.getRefundAmount() == null ? BigDecimal.ZERO : orderItemsExtend.getRefundAmount();
                        BigDecimal refundItemTotalAmount = oldRefundItemAmount.add(refundItemAmount);
                        refundItemsExtend.setRefundAmount(refundItemAmount);                 // 退款金额
                        orderItemsExtend.setRefundAmount(refundItemTotalAmount);             // 退款金额要加上之前退的（可多次退）
                        //总退款金额实时递减
                        leftRefundAmount = leftRefundAmount.subtract(refundItemAmount);
                    }
                    if (leftRefundPoints > 0) {
                        // 处理平摊积分
                        Long refundItemPoints = orderItemsExtend.getAmount()
                                .multiply(BigDecimal.valueOf(refundItems.getRefundPoints()))
                                .divide(orderItems.getActualAmount())
                                .setScale(4, RoundingMode.DOWN).longValue();
                        refundItemsExtend.setRefundPoints(refundItemPoints);
                        //剩余积分
                        leftRefundPoints = leftRefundPoints - refundItemPoints;

                        BigDecimal refundItemPointAmount = orderItemsExtend.getAmount()
                                .multiply(refundItems.getRefundPointsAmount())
                                .divide(orderItems.getActualAmount())
                                .setScale(4, RoundingMode.DOWN);
                        refundItemsExtend.setRefundPointsAmount(refundItemPointAmount);           // 退款积分金额
                        BigDecimal oldRefundAmount = orderItemsExtend.getRefundAmount()==null?BigDecimal.ZERO:orderItemsExtend.getRefundAmount();
                        orderItemsExtend.setRefundAmount(oldRefundAmount.add(refundItemPointAmount));
                        //剩余积分金额
                        leftRefundPointsAmount  = leftRefundPointsAmount.subtract(refundItemPointAmount);
                    }
                    refundItemsExtendService.add(refundItemsExtend);
                }
            }
            orderItemsExtendService.updateBatchById(orderItemsExtends);
        }
        orderItemsService.updateBatchById(oiList);
        */
        // 处理退还金额和积分
        if (po.existsCommission()) {
            // 存在手续费 创建手续费订单
            this.createCommissionOrder(refund, po);
        }
        if (points != null && points > 0) {
            // 需要退还积分，同时积分金额要写一条逆向的支付记录
            this.refundPoint(refund, points, totalPointAmount);
        }
        // 退还金额
        this.refundAmount(po, refund);

        // 退还订单赠送的积分
        for (RefundItems refundItems : refundItemsList) {
            this.refundOrderPoint(refund, refundItems);
        }

        // 检查订单状态
        this.checkOrderStatus(refund);

        // 处理订单总退款金额 退款总金额 = 退款金额 + 退款积分金额
        List<RefundItems> refundItems = refundItemsService.listByOrderId(refund.getOrderId(), RefundStatusEnums.PASSED.getCode());
        BigDecimal orderRefundAmount = refundItems.stream()
                .map(RefundItems::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderRefundPointsAmount = refundItems.stream()
                .map(RefundItems::getRefundPointsAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Orders::getId, refund.getOrderId())
                .set(Orders::getRefundTotalAmount, BigDecimal.ZERO.add(orderRefundAmount).add(orderRefundPointsAmount));
        ordersService.update(updateWrapper);
        //支付方式分摊
        splitOrderPriceService.splitRefundOrderPrice(refund.getId());
        try {
            UsersVO user = usersBiz.getDetailById(refund.getUserId());
            StoreVO storeVO = storeBiz.getById(refund.getStoreId());
            aliSmsBiz.returnOrderStatusSms(user.getPhone(), storeVO.getStoreName(), refundAmount,
                    storeVO.getContactPhone());
        } catch (Exception e) {
            log.error("发送【退单状态提醒】短信发生异常", e);
        }

        try {
            operationLogService.saveOperationLog(po.getId(),
                    String.format("退款审核：{%s}", refund.getRefundNo()),
                    "退款审核");
        } catch (Exception e) {
            log.error("退款审核保存日志失败！", e);
        }

    }

    public void handleRefundItemExtendAmount(RefundItems refundItem){
        ProductTypeEnums productType = ProductTypeEnums.codeToEnum(refundItem.getProductType());
        if(ProductTypeEnums.COURSE != productType){
            // 非疗程，不需要处理
            return;
        }

        BigDecimal refundAmount = refundItem.getRefundAmount();
        Long refundPoints = refundItem.getRefundPoints();


        List<RefundItemsExtend> refundExtendList = refundItemsExtendService.listByRefundItemId(refundItem.getId(), Boolean.FALSE);
        List<Long> orderItemExtentIds = refundExtendList.stream().map(RefundItemsExtend::getOrderItemExtentId).collect(Collectors.toList());
        List<OrderItemsExtend> orderItemsExtends = orderItemsExtendService.listByIds(orderItemExtentIds);
        Map<Long, OrderItemsExtend> orderItemsExtendMap = orderItemsExtends.stream().collect(Collectors.toMap(OrderItemsExtend::getId, Function.identity()));


        Map<Long, BigDecimal> qtyPropMap = new HashMap<>();// 每条记录的数量占比，精确到4位
        Map<Long, BigDecimal> shouldRefundAmountMap = new HashMap<>();// 应退金额，精确到2位
        Map<Long, BigDecimal> shouldRefundPointMap = new HashMap<>();// 应退积分，精确到2位
        Map<Long, BigDecimal> singlePorintAmountMap = new HashMap<>();// 单积分价值，精确到2位


        for (RefundItemsExtend refundItemsExtend : refundExtendList) {
            OrderItemsExtend orderItemsExtend = orderItemsExtendMap.get(refundItemsExtend.getOrderItemExtentId());

            // 实退数量对应下单数量的占比
            BigDecimal qtyProp = refundItemsExtend.getRefundQuantity().divide(BigDecimal.valueOf(orderItemsExtend.getQuantity()), 8, RoundingMode.DOWN);
            qtyPropMap.put(refundItemsExtend.getId(), qtyProp);

            // 应退金额
            if(BigDecimal.ZERO.compareTo(orderItemsExtend.getAmount()) >= 0){
                shouldRefundAmountMap.put(refundItemsExtend.getId(), BigDecimal.ZERO);
            }else {
                BigDecimal shouldRefundAmount = orderItemsExtend.getAmount().multiply(qtyProp).setScale(2, RoundingMode.UP);
                shouldRefundAmountMap.put(refundItemsExtend.getId(), shouldRefundAmount);
            }

            // 应退积分
            if(0 >= orderItemsExtend.getPoint()){
                shouldRefundPointMap.put(refundItemsExtend.getId(), BigDecimal.ZERO);
                singlePorintAmountMap.put(refundItemsExtend.getId(), BigDecimal.ZERO);
            }else {
                BigDecimal shouldRefundPoint = BigDecimal.valueOf(orderItemsExtend.getPoint()).multiply(qtyProp).setScale(2, RoundingMode.UP);
                shouldRefundPointMap.put(refundItemsExtend.getId(), shouldRefundPoint);

                // 单积分价值
                BigDecimal singlePointAmount = orderItemsExtend.getPointAmount().divide(BigDecimal.valueOf(orderItemsExtend.getPoint()), 2, RoundingMode.DOWN);
                singlePorintAmountMap.put(refundItemsExtend.getId(), singlePointAmount);
            }

        }
        // 应退总金额
        BigDecimal shouldRefundTotalAmount = shouldRefundAmountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal shouldRefundTotalPoint = shouldRefundPointMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal leftRefundAmount = refundAmount;
        Long leftRefundPoints = refundPoints;

        Iterator<RefundItemsExtend> refundExtendIterator = refundExtendList.iterator();

        while (refundExtendIterator.hasNext()) {
            RefundItemsExtend rie = refundExtendIterator.next();

            BigDecimal singlePorintAmount = singlePorintAmountMap.get(rie.getId());


            if(!refundExtendIterator.hasNext()){
                // 不存在下一个，代表当前为最后一项了，所以用减法赋值。

                if(leftRefundAmount == null || BigDecimal.ZERO.compareTo(leftRefundAmount) >= 0){
                    rie.setRefundAmount(BigDecimal.ZERO);
                } else {
                    rie.setRefundAmount(leftRefundAmount);
                }

                if(leftRefundPoints == null || refundPoints <= 0){
                    rie.setRefundPoints(0L);
                    rie.setRefundPointsAmount(BigDecimal.ZERO);
                } else {
                    rie.setRefundPoints(leftRefundPoints);
                    rie.setRefundPointsAmount(singlePorintAmount.multiply(BigDecimal.valueOf(leftRefundPoints)));
                }
                continue;
            }

            // 计算实际退款金额
            BigDecimal shouldRefundAmount = shouldRefundAmountMap.get(rie.getId());
            if(BigDecimal.ZERO.compareTo(shouldRefundAmount) >= 0){
                rie.setRefundAmount(BigDecimal.ZERO);
            } else {
                BigDecimal actualRefundAmount = shouldRefundAmount.divide(shouldRefundTotalAmount, 8, RoundingMode.DOWN).multiply(refundAmount).setScale(2, RoundingMode.DOWN);
                rie.setRefundAmount(actualRefundAmount);
                leftRefundAmount = leftRefundAmount.subtract(actualRefundAmount);
            }

            // 计算实际退款积分
            BigDecimal shouldRefundPoint = shouldRefundPointMap.get(rie.getId());
            if(BigDecimal.ZERO.compareTo(shouldRefundPoint) >= 0){
                rie.setRefundPoints(0L);
                rie.setRefundPointsAmount(BigDecimal.ZERO);
            } else {
                Long actualRefundPoint = shouldRefundPoint.divide(shouldRefundTotalPoint, 8, RoundingMode.DOWN).multiply(BigDecimal.valueOf(refundPoints)).setScale(0, RoundingMode.DOWN).longValue();
                rie.setRefundPoints(actualRefundPoint);
                leftRefundPoints = leftRefundPoints - actualRefundPoint;

                // 计算积分的价值
                rie.setRefundPointsAmount(singlePorintAmount.multiply(BigDecimal.valueOf(leftRefundPoints)));
            }
        }

        for (RefundItemsExtend refundItemsExtend : refundExtendList) {
            OrderItemsExtend oie = orderItemsExtendMap.get(refundItemsExtend.getOrderItemExtentId());
            BigDecimal ra = Optional.ofNullable(oie.getRefundAmount()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(refundItemsExtend.getRefundAmount()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(refundItemsExtend.getRefundPointsAmount()).orElse(BigDecimal.ZERO));
            oie.setRefundAmount(ra);
        }

        this.refundItemsExtendService.updateBatchById(refundExtendList);
        this.orderItemsExtendService.updateBatchById(orderItemsExtends);
    }



    /**
     * 检查订单状态
     * 如果订单的所有项目都已经完成了售后，则订单改为已关闭
     */
    private void checkOrderStatus(Refund refund) {
        Long orderId = refund.getOrderId();
        List<OrderItems> orderItems = orderItemsService.listByOrderId(orderId);
        List<RefundItems> refundItems = refundItemsService.listByOrderId(orderId, RefundStatusEnums.PASSED.getCode());
        Map<Long, BigDecimal> refundItemMap = orderItems.stream()
                .collect(Collectors.toMap(OrderItems::getId, oi -> BigDecimal.valueOf(oi.getQuantity())));
        for (RefundItems refundItem : refundItems) {
            BigDecimal qty = refundItemMap.get(refundItem.getOrderItemId());
            qty = qty.subtract(refundItem.getRefundQuantity());
            refundItemMap.put(refundItem.getOrderItemId(), qty);
        }
        boolean allRefunded = refundItemMap.values().stream().allMatch(qty -> BigDecimal.ZERO.compareTo(qty) >= 0);
        if (allRefunded) {
            ordersService.closeOrder(orderId);
        }else {
            //如果子项是项目的话，如果数量不为0，还需要判断可用数量是否为0
            List<Long> orderIds = new ArrayList<>();
            orderIds.add(orderId);
            List<UserServices> userServicesList = userServicesService.listByOrderIds(orderIds);
            //可用数量小于0，状态改为已完成
            BigDecimal availableQuantity = userServicesList.stream().map(UserServices::getAvailableQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                ordersService.updateOrderStatus(orderId, OrderStatusEnums.COMPLETE);
            }
        }
    }

    /**
     * 退还积分
     * 
     * @param refund
     * @param refundPoints
     */
    private void refundPoint(Refund refund, Long refundPoints, BigDecimal totalPointAmount) {
        if (refund.getApplyPoint() > refundPoints) {
            throw new BusinessException("实际退还的积分不能大于申请的积分");
        }

        // 新增积分明细记录
        PointsDetails pointsDetails = new PointsDetails();
        pointsDetails.setUserId(refund.getUserId());
        pointsDetails.setType(PointsTypeEnums.REFUND.getCode());
        pointsDetails.setOrderId(refund.getOrderId());
        pointsDetails.setOrderNo(refund.getOrderNo());
        pointsDetails.setRefundId(refund.getId());
        pointsDetails.setStoreId(refund.getStoreId());
        pointsDetails.setPoints(refundPoints);
        pointsDetailsService.save(pointsDetails);
        //退积分，要写一条逆向的支付记录
        // 新增支付记录，统计的时候需要用到
        PayMethods payMethods = payMethodsService.getById(BasicConstants.POINT_PAYMENT_METHOD_ID);
        Payments payments = new Payments();
        payments.setUserId(refund.getUserId());
        payments.setRefundId(refund.getId());
        payments.setOrderId(refund.getOrderId());
        payments.setPayMethodId(payMethods.getId());
        payments.setPayMethod(payMethods.getPayMethod());
        //积分金额要取负数
        payments.setAmount(totalPointAmount.negate());
        payments.setPaymentTime(new Date());
        paymentsService.save(payments);
        // 退还积分
        userPointsService.addPoints(refund.getUserId(), refund.getStoreId(), refundPoints);
    }

    /**
     * 退还订单赠送的积分
     * 
     * @param refund
     */
    private void refundOrderPoint(Refund refund, RefundItems refundItem) {
        List<PointsDetails> pointsDetailsList = pointsDetailsService.listByOrderIdAndType(refund.getOrderId(),
                PointsTypeEnums.CONSUME_RETURN.getCode());
        if (CollUtil.isEmpty(pointsDetailsList)) {
            return;
        }
        List<OrderItems> orderItemsList = orderItemsService.listByOrderId(refund.getOrderId());

        Optional<BigDecimal> singleAmountOptional = orderItemsList.stream()
                .filter(oi -> oi.getId().equals(refundItem.getOrderItemId()))
                .filter(oi -> oi.getActualAmount() != null)
                .filter(oi -> oi.getQuantity() != null)
                .map(oi -> oi.getActualAmount().divide(new BigDecimal(oi.getQuantity()), 2, RoundingMode.HALF_UP))
                .findFirst();

        if (!singleAmountOptional.isPresent()) {
            return;
        }

        // 订单赠送的总积分
        long totalPoints = pointsDetailsList.stream().mapToLong(PointsDetails::getPoints).filter(Objects::nonNull)
                .sum();
        // 订单的总实付金额
        BigDecimal totalActualAmount = orderItemsList.stream().map(OrderItems::getActualAmount).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 退款商品的实付单价
        BigDecimal singleAmount = singleAmountOptional.get();
        // 退款商品的数量
        BigDecimal refundQuantity = refundItem.getRefundQuantity();
        // 应退商品金额
        BigDecimal refundAmount = singleAmount.multiply(refundQuantity);
        // 实际应该扣除用户的积分量，根据退还商品实付金额，占比整个订单的实付金额的比例，乘以总积分
        long refundPoints = refundAmount.divide(totalActualAmount, 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(totalPoints))
                .setScale(0, RoundingMode.HALF_UP)
                .longValue();

        if (refundPoints <= 0) {
            return;
        }
        userPointsService.subPoints(refund.getUserId(), refund.getStoreId(), refundPoints);
        // 新增积分明细记录
        PointsDetails pointsDetails = new PointsDetails();
        pointsDetails.setUserId(refund.getUserId());
        pointsDetails.setType(PointsTypeEnums.CONSUME_RETURN_REFUND.getCode());
        pointsDetails.setOrderId(refund.getOrderId());
        pointsDetails.setOrderNo(refund.getOrderNo());
        pointsDetails.setRefundId(refund.getId());
        pointsDetails.setStoreId(refund.getStoreId());
        pointsDetails.setPoints(-refundPoints);
        pointsDetailsService.save(pointsDetails);

    }

    private void refundAmount(RefundPassPO po, Refund refund) {

        // 验证实际退款的金额，是否大于0，并且小于申请退款的金额
        BigDecimal amountSum = BigDecimal.ZERO;
        List<CardPO> cardList = po.getCardList();
        List<PayMethodPO> payMethodList = po.getPayMethodList();
        if(refund.getRefundAmount().compareTo(BigDecimal.ZERO) > 0){
            if (CollUtil.isEmpty(cardList) && CollUtil.isEmpty(payMethodList)) {
                throw new BusinessException("必须选择一种金额的退款方式");
            }
        }
        if (CollUtil.isNotEmpty(cardList)) {
            amountSum = cardList.stream()
                    .map(CardPO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (CollUtil.isNotEmpty(payMethodList)) {
            amountSum = amountSum.add(payMethodList.stream()
                    .map(PayMethodPO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (refund.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
            if (amountSum.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessException("实际退款金额必须大于0");
            }
        }


        // 非卡支付的，只需要生成payment记录即可
        if (CollUtil.isNotEmpty(payMethodList)) {
            for (PayMethodPO payMethodPO : payMethodList) {
                // 新增支付记录
                Payments payments = new Payments();
                payments.setUserId(refund.getUserId());
                payments.setOrderId(refund.getOrderId());
                payments.setRefundId(refund.getId());
                payments.setPayMethodId(payMethodPO.getPayMethodId());
                payments.setPayMethod(payMethodPO.getPayMethod());
                payments.setAmount(BigDecimal.ZERO.subtract(payMethodPO.getAmount()));
                payments.setPaymentTime(new Date());
                payments.setRefundId(refund.getId());
                paymentsService.save(payments);
            }
        }
        // 卡支付的，需要先退还金额，再补充卡的流水记录
        if (CollUtil.isNotEmpty(cardList)) {

            List<Long> cardIds = cardList.stream().map(CardPO::getCardId).collect(Collectors.toList());
            List<MemberCardsDTO> list = memberCardsService.listByCardIds(cardIds, refund.getUserId());
            // Map<Long, Long> cardMap = list.stream()
            // .collect(Collectors.toMap(MemberCardsDTO::getCardId, MemberCardsDTO::getId));

            for (CardPO cardPO : cardList) {
                MemberCardsDTO memberCardsDTO = list.stream()
                        .filter(item -> item.getCardId().equals(cardPO.getCardId()) &&
                                item.getStoreId().equals(refund.getStoreId()))
                        .findFirst().orElse(null);
                if (memberCardsDTO == null) {
                    throw new BusinessException("卡信息错误");
                }
                this.addAmount(memberCardsDTO.getId(), cardPO.getCardType(), refund, cardPO.getAmount());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reject(RefundRejectPO po) {
        Refund refund = this.getRefund(po.getId());
        RefundStatusEnums refundStatus = RefundStatusEnums.getEnumByCode(refund.getStatus());
        if (RefundStatusEnums.PENDING_APPROVAL != refundStatus) {
            throw new BusinessException("此退款单已被处理，请刷新页面。");
        }

        refund.setStatus(RefundStatusEnums.REJECTED.getCode());
        refund.setHandlerRemark(po.getHandlerRemark());
        refund.setHandlerId(LoginUserUtil.getLoginUser().getUserId());
        refund.setHandlerTime(new Date());
        refundService.updateById(refund);
        this.rollBackRefund(refund);

        try {
            operationLogService.saveOperationLog(po.getId(),
                    String.format("退款审核：{%s}", refund.getRefundNo()),
                    "退款审核");
        } catch (Exception e) {
            log.error("退款审核保存日志失败！", e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void close(Long id) {
        Refund refund = this.getRefund(id);
        RefundStatusEnums refundStatus = RefundStatusEnums.getEnumByCode(refund.getStatus());
        if (RefundStatusEnums.PENDING_APPROVAL != refundStatus) {
            throw new BusinessException("此退款单已被处理，请刷新页面。");
        }

        refund.setStatus(RefundStatusEnums.CLOSED.getCode());
        refund.setHandlerId(LoginUserUtil.getLoginUser().getUserId());
        refund.setHandlerTime(new Date());
        refundService.updateById(refund);
        this.rollBackRefund(refund);

        try {
            operationLogService.saveOperationLog(id,
                    String.format("退款审核：{%s}", refund.getRefundNo()),
                    "退款审核");
        } catch (Exception e) {
            log.error("退款审核保存日志失败！", e);
        }
    }

    @Override
    public RefundDetailVO getDetail(Long id) {
        Refund refund = this.getRefund(id);
        RefundDetailVO result = BeanUtil.copyProperties(refund, RefundDetailVO.class);
        if (result.getApplyUserId() == null) {
            result.setApplyUserId(refund.getCreateBy());
        }
        UsersVO applyUser = usersBiz.getDetailById(result.getApplyUserId());
        result.setApplyName(applyUser.getName());
        result.setApplyPhone(applyUser.getPhone());
        result.setApplyUserCode(applyUser.getUserCode());

        UsersVO usersVO = usersBiz.getDetailById(result.getUserId());
        if(Objects.nonNull(usersVO)) {
            result.setUserName(usersVO.getName());
            result.setUserPhone(usersVO.getPhone());
            result.setUserCode(usersVO.getUserCode());
        }
        RefundItems queryItems = new RefundItems();
        queryItems.setRefundId(refund.getId());
        List<RefundItems> refundItems = refundItemsService.listEntity(queryItems);
        RefundItems refundItem = refundItems.get(0);
        result.setRefundQuantity(refundItem.getRefundQuantity());
        Products products = productsService.getByOrderItemId(refundItem.getOrderItemId());
        result.setProductMainImage(products.getProductMainImage());
        result.setProductName(products.getProductName());
        result.setProductType(products.getProductType());


        if (Objects.nonNull(refund.getRefundReason())) {
            RefundReasons refundReasons = refundReasonsService.getById(refund.getRefundReason());
            if (Objects.nonNull(refundReasons)) {
                result.setRefundReasonStr(refundReasons.getRefundReason());
            }
        }

        List<Payments> paymentList = paymentsService.listByRefundId(refund.getId());
        if (CollUtil.isNotEmpty(paymentList)) {
            result.setPaymentsVOList(BeanUtil.copyToList(paymentList, PaymentsVO.class));
        }

        List<PointsDetails> pointsDetailList = pointsDetailsService.listByRefundId(refund.getId());
        if (CollUtil.isNotEmpty(pointsDetailList)) {
            result.setPointsDetailsVOList(BeanUtil.copyToList(pointsDetailList, PointsDetailsVO.class));
        }

        // 手续费支付方式
        if(Objects.nonNull(result.getCommissionPayMethod())){
            List<Long> ids = new ArrayList<>();
            ids.add(result.getCommissionPayMethod());
            Map<Long, PayMethods> longPayMethodsMap = payMethodsService.listByPayMethodIds(ids);
            PayMethods methods = longPayMethodsMap.get(result.getCommissionPayMethod());
            if(Objects.nonNull(methods)){
                result.setCommissionPayMethodName(methods.getPayMethod());
            }
        }

        return result;
    }

    /**
     * 回滚售后单
     * 
     * @param refund
     */
    private void rollBackRefund(Refund refund) {
        // 如果存在服务，需要还原服务可用次数


        List<RefundItems> riList = refundItemsService.listByRefundIds(Collections.singletonList(refund.getId()));

        for (RefundItems ri : riList) {
            ProductTypeEnums productType = ProductTypeEnums.codeToEnum(ri.getProductType());
            if(ProductTypeEnums.PROJECT == productType){
                this.userServicesService.subRefundQuantity(ri.getOrderId(), ri.getOrderItemId(), ri.getProductId(), ri.getRefundQuantity());
            }
            if(ProductTypeEnums.COURSE == productType){
                List<RefundItemsExtend> rieList =refundItemsExtendService.listByRefundItemId(ri.getId());
                rieList.stream()
                        .filter(rie -> ProductTypeEnums.PROJECT == ProductTypeEnums.codeToEnum(rie.getProductType()))
                        .forEach(rie -> {
                            this.userServicesService.subRefundQuantity(rie.getOrderId(), rie.getOrderItemId(), rie.getProductId(), rie.getRefundQuantity());
                        });
            }


        }




//        List<RefundItems> riList = refundItemsService.listByRefundIds(Collections.singletonList(refund.getId()));
//        List<Long> oiIdList = riList.stream().map(RefundItems::getOrderItemId).collect(Collectors.toList());
//        List<OrderItems> oiList = orderItemsService.listByIds(oiIdList);
//        Map<Long, OrderItems> oiMap = oiList.stream().collect(Collectors.toMap(OrderItems::getId, Function.identity()));
//        for (RefundItems ri : riList) {
//            OrderItems oi = oiMap.get(ri.getOrderItemId());
//            List<ProjectTileDTO> projectList = productsService.listProject(oi.getProductId());
//            Integer refundQuantity = ri.getRefundQuantity();
//            for (ProjectTileDTO projectTileDTO : projectList) {
//                Long projectId = projectTileDTO.getId();
//                Integer qty = refundQuantity * projectTileDTO.getQuantity();
//                this.userServicesService.subRefundQuantity(refund.getOrderId(), ri.getOrderItemId(), projectId, BigDecimal.valueOf(qty));
//            }
//        }
    }

    /**
     * 创建手续费订单
     */
    private void createCommissionOrder(Refund refund, RefundPassPO po) {
        Orders order = ordersService.getById(refund.getOrderId());

        OrderItemsPO oi = new OrderItemsPO();
        oi.setProductId(COMMISSION_PRODUCT_ID);
        oi.setQuantity(1);
        oi.setModifyAmount(po.getCommission());
        //
        OrdersCreatePO ordersCreatePO = new OrdersCreatePO();
        ordersCreatePO.setUserId(refund.getUserId());
        ordersCreatePO.setRemark("售后手续费，金额以实收为准。售后单号：" + refund.getRefundNo());
        ordersCreatePO.setOrderItems(Collections.singletonList(oi));
        ordersCreatePO.setOrderSourceId(0L);
        ordersCreatePO.setStoreId(order.getStoreId());
        ordersCreatePO.setDepartmentId(order.getDepartmentId());
        Long orderId = ordersBiz.createOrder(ordersCreatePO, OrderTypeEnums.COMMISSION.getCode());

        SettlementCreatePO settlementCreatePO = new SettlementCreatePO();
        settlementCreatePO.setOrderId(orderId);
        settlementCreatePO.setRecommendedUserId(order.getRecommendedUserId());
        settlementCreatePO.setSalesUserId(order.getSalesUserId());
        settlementCreatePO.setRecommendedDepartmentId(order.getRecommendedDepartmentId());
        settlementCreatePO.setSalesDepartmentId(order.getSalesDepartmentId());

        if (po.isCardCommission()) {
            CardPO cardPO = new CardPO();
            cardPO.setCardId(po.getCardId());
            cardPO.setCardType(po.getCardType());
            cardPO.setAmount(po.getCommission());
            settlementCreatePO.setSettlementCardList(Collections.singletonList(cardPO));
        } else {
            PayMethodPO payMethodPO = new PayMethodPO();
            payMethodPO.setPayMethod(po.getPayMethod());
            payMethodPO.setPayMethodId(po.getPayMethodId());
            payMethodPO.setAmount(po.getCommission());
            settlementCreatePO.setPayMethodPOList(Collections.singletonList(payMethodPO));
        }
        ordersBiz.orderSettlement(settlementCreatePO);

    }

    /**
     * 售后退还金额
     * 
     * @param memberCardId 会员卡id
     * @param cardType     卡类型
     * @param refund       退款单
     * @param addAmount    退还金额
     */
    private void addAmount(Long memberCardId, Integer cardType, Refund refund, BigDecimal addAmount) {
        int ret = memberCardsService.addAmount(memberCardId, addAmount);
        if (ret <= 0) {
            throw new BusinessException("会员卡不存在");
        }
        BigDecimal amount = BigDecimal.ZERO.subtract(addAmount);
        // 新增消费记录
        MemberCards tempCard = memberCardsService.getById(memberCardId);
        CardRecords cardRecord = new CardRecords();
        cardRecord.setUserId(refund.getUserId());
        cardRecord.setMemberCardId(memberCardId);
        cardRecord.setCardNumber(tempCard.getCardNumber());
        cardRecord.setCardType(cardType);
        cardRecord.setType(CardRecordsTypeEnums.REFUND.getCode());
        cardRecord.setAmount(amount.abs());
        cardRecord.setAvailableAmount(tempCard.getAvailableAmount());
        cardRecord.setStoreId(tempCard.getStoreId());
        // 支付方式id,储值卡属于余额支付
        cardRecord.setPayMethodId(tempCard.getCardType().longValue());
        cardRecordsService.save(cardRecord);
        // 新增支付记录
        Payments payment = new Payments();
        payment.setAmount(amount);
        payment.setOrderId(refund.getOrderId());
        payment.setRefundId(refund.getId());
        payment.setUserId(refund.getUserId());
        // 支付方式id
        List<Long> tempCardIdList = new ArrayList<>();
        tempCardIdList.add(tempCard.getTemplateCardId());
        CardDTO cardDTO = cardsService.getByTemplateCardId(tempCardIdList).get(0);
        payment.setPayMethodId(cardDTO.getCardId());
        payment.setPayMethod(cardDTO.getCardName());
        payment.setCardNumber(tempCard.getCardNumber());
        payment.setPaymentTime(new Date());
        paymentsService.save(payment);

        // 增加短信通知
        try {
            Users users = usersService.getById(cardRecord.getUserId());
            StoreVO store = storeBiz.getById(cardRecord.getStoreId());
            if (Objects.nonNull(users)) {
                LoginUser loginUser = LoginUserUtil.getLoginUser();
                String phone = usersService.getStoreHeadPhone(loginUser.getStoreId());
                MemberCards memberCards = memberCardsService.getById(memberCardId);
                aliSmsBiz.balanceChangesSms(users.getPhone(), store.getStoreName(), addAmount,
                        memberCards.getAvailableAmount(), phone);
            }
        } catch (Exception e) {
            log.error("退款流程，余额变更之后，短信通知发生异常, refundId:" + refund.getId(), e);
        }

    }

    private Refund getRefund(Long id) {
        Refund refund = refundService.getById(id);
        if (refund == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        return refund;
    }

    @Override
    public List<ItemRefundAmountDTO> getOrderItemRefundAmount(List<Long> orderItemIdList, Date startTime, Date endTime, String productName, Long projectDepartmentId) {
        return refundService.getOrderItemRefundAmount(orderItemIdList, startTime, endTime, productName, projectDepartmentId);
    }

    @Override
    public List<ItemRefundCountDTO> getOrderItemRefundCount(List<Long> orderItemIds) {
        if (CollUtil.isEmpty(orderItemIds)) {
            return new ArrayList<>();
        }
        return refundService.getOrderItemRefundCount(orderItemIds);
    }
    @Override
    public List<RefundQuantityDTO> listOrderItemRefundCount(List<Long> orderItemIds) {
        return refundService.listOrderItemRefundCount(orderItemIds);
    }

    @Override
    public BigDecimal calculateRefundAmount(CalculateRefundAmountPO po) {

        BigDecimal refundedQuantity = BigDecimal.ZERO;
        List<RefundItems> list = refundItemsService.listByOrderItemId(po.getOrderItemId());
        if (CollUtil.isNotEmpty(list)) {
            refundedQuantity = list.stream()
                    .map(RefundItems::getRefundQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        OrderItems oi = orderItemsService.getById(po.getOrderItemId());
        BigDecimal totalQuantity = BigDecimal.valueOf(oi.getQuantity());

//        if (po.getQuantity() + refundedQuantity > totalQuantity) {
        if(po.getQuantity().add(refundedQuantity).compareTo(totalQuantity) > 0){
            throw new BusinessException("只能申请未退款的数量");
        }

        ProductTypeEnums productType = ProductTypeEnums.codeToEnum(oi.getProductType());

        if(ProductTypeEnums.COURSE == productType){
            List<CalcRefundAmount> calcRefundAmountList = this.getCalcRefundAmountList(po);
            return calcRefundAmountList.stream().map(CalcRefundAmount::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal actualAmount = oi.getActualAmount();
        Integer quantity = oi.getQuantity();
        BigDecimal singleAmount = actualAmount.divide(new BigDecimal(quantity), 2, RoundingMode.DOWN);
        if (refundedQuantity.add(po.getQuantity()).compareTo(totalQuantity) == 0) {
            // 是最后一件
            BigDecimal refundedAmount = singleAmount.multiply(refundedQuantity);
            return oi.getActualAmount().subtract(refundedAmount);
        } else {
            return singleAmount.multiply(po.getQuantity()).setScale(2, RoundingMode.DOWN);
        }
    }

    private List<CalcRefundAmount> getCalcRefundAmountList(CalculateRefundAmountPO po){
        List<OrderItemsExtend> oieList = orderItemsExtendService.listByOrderItemId(po.getOrderItemId());
        // 子商品集合
        Map<Long, List<OrderItemsExtend>> childrenMap = oieList.stream()
                .filter(oie -> Objects.nonNull(oie.getParentProductId()))
                .collect(Collectors.groupingBy(OrderItemsExtend::getParentProductId));

        List<UserServices> userServicesList = userServicesService.listByOrderItemId(po.getOrderItemId());
        Map<Long, UserServices> userServicesMap = userServicesList.stream()
                .collect(Collectors.toMap(UserServices::getProductId, Function.identity(), (a, b) -> {
                    if(a.getAvailableQuantity().compareTo(BigDecimal.ZERO) == 0){
                        b.setUsedQuantity(a.getUsedQuantity().add(b.getUsedQuantity()));
                        return b;
                    }
                    return a;
                }));

        List<CalcRefundAmount> calcRefundAmountList = new ArrayList<>();
        for (OrderItemsExtend first : oieList) {

            if(first.getParentProductId() != null && first.getParentProductId() != 0L){
                // 不处理第二级的数据，
                // 第二级的数据在遍历到一级数据之后，直接处理。
                continue;
            }

            ProductTypeEnums productType = ProductTypeEnums.codeToEnum(first.getProductType());

            // 处理一级
            CalcRefundAmount firstCalc = new CalcRefundAmount(first.getAmount(),
                    BigDecimal.valueOf(first.getQuantity()),
                    first.getProductName(),
                    first.getProductType(),
                    BigDecimal.ZERO,
                    BigDecimal.ZERO);
            calcRefundAmountList.add(firstCalc);

            if(productType == ProductTypeEnums.PROJECT){
                UserServices userServices = userServicesMap.get(first.getProductId());
                firstCalc.setTotalUseQuantity(userServices.getUsedQuantity());
                firstCalc.setRefundedQuantity(userServices.getRefundQuantity());

                List<OrderItemsExtend> goodsList = childrenMap.get(first.getId());
                if(CollUtil.isEmpty(goodsList)){
                    continue;
                }
                // 处理项目下的商品
                for (OrderItemsExtend second : goodsList) {
                    BigDecimal singleProjectGoodsQuantity = BigDecimal.valueOf(second.getQuantity())
                            .divide(BigDecimal.valueOf(first.getQuantity()), 2, RoundingMode.HALF_UP);

                    CalcRefundAmount goods2 = new CalcRefundAmount(second.getAmount(),
                            BigDecimal.valueOf(second.getQuantity()),
                            second.getProductName(),
                            second.getProductType(),
                            singleProjectGoodsQuantity.multiply(firstCalc.getTotalUseQuantity()),
                            singleProjectGoodsQuantity.multiply(firstCalc.getRefundedQuantity()));
                    calcRefundAmountList.add(goods2);
                }
            }
        }
        return calcRefundAmountList;
    }


    @Override
    public OrderItemRefundVO getOrderItemRefundAmount2(Long orderItemId, Date startTime, Date endTime) {
        return refundService.getOrderItemRefundAmount2(orderItemId, startTime, endTime);
    }

    @Override
    public RefundPreCheckVO preCheck(Long orderItemId) {
        OrderItems orderItems = orderItemsService.getById(orderItemId);
        if (orderItems == null) {
            throw new BusinessException("订单明细不存在");
        }

        Long cnt = reservationServicesService.countByOrderItemId(orderItemId, ReservationServicesEnums.Status.PENDING.getCode());

        return RefundPreCheckVO.builder()
                .canRefundQuantity(this.getCanRefundQuantity(orderItems))
                .hasPendingServices(cnt > 0)
                .build();
    }

    @Override
    public List<RefundItemsExtendVO> listExtendByRefund(Long refundId) {
        RefundItems query = new RefundItems();
        query.setRefundId(refundId);
        List<RefundItems> refundItemsList = refundItemsService.listEntity(query);
        if (CollUtil.isEmpty(refundItemsList)) {
            return Collections.emptyList();
        }
        RefundItems ri = refundItemsList.get(0);
        List<RefundItemsExtend> list = refundItemsExtendService.listByRefundItemId(ri.getId());
        List<RefundItemsExtendVO> result = BeanUtil.copyToList(list, RefundItemsExtendVO.class);
        productsBiz.fullData(result, RefundItemsExtendVO::getProductId, (res, products) -> res.setProductMainImage(products.getProductMainImage()));
        return result;
    }

    @Override
    public Map<Long, Date> getOrderItemRefundDateMap(List<Long> orderIdList, Date startTime, Date endTime) {
        List<RefundItemsRefundDateDTO> refundItemRefundDateList = refundService.getOrderItemRefundDate(orderIdList, startTime, endTime);
        if(CollUtil.isEmpty(refundItemRefundDateList)) {
            return new HashMap<>();
        }
        Map<Long, Date> result = refundItemRefundDateList.stream()
                .collect(Collectors.toMap(
                        RefundItemsRefundDateDTO::getOrderItemId,  // key
                        RefundItemsRefundDateDTO::getHandlerTime,  // value
                        (existing, replacement) -> existing.after(replacement) ? existing : replacement  // merge function
                ));
        return result;
    }
}