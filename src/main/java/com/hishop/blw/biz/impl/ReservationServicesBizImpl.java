package com.hishop.blw.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.blw.biz.*;
import com.hishop.blw.config.SnowPrefixConstants;
import com.hishop.blw.constants.BasicConstants;
import com.hishop.blw.enums.OrderStatusEnums;
import com.hishop.blw.enums.ReservationServicesEnums;
import com.hishop.blw.enums.UsersStatusEnums;
import com.hishop.blw.enums.UsersTypeEnums;
import com.hishop.blw.model.po.reservation.*;
import com.hishop.blw.model.vo.order.ReservationServicesVO;
import com.hishop.blw.model.vo.order.VerifyVO;
import com.hishop.blw.model.vo.product.ProductsVO;
import com.hishop.blw.model.vo.reservation.ReservationBoardVO;
import com.hishop.blw.model.vo.reservation.ReservationServicesDetailVO;
import com.hishop.blw.model.vo.reservation.UserServiceOptionVO;
import com.hishop.blw.model.vo.store.DepartmentUserVO;
import com.hishop.blw.model.vo.store.DepartmentVO;
import com.hishop.blw.model.vo.store.StoreVO;
import com.hishop.blw.model.vo.order.UserServicesVO;
import com.hishop.blw.model.vo.user.UsersVO;
import com.hishop.blw.repository.dto.AmontAndPointDTO;
import com.hishop.blw.repository.dto.ReservationServicesPageQueryDTO;
import com.hishop.blw.repository.dto.ReservationServicesPageResultDTO;
import com.hishop.blw.repository.entity.*;
import com.hishop.blw.repository.service.*;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 预约服务表 业务逻辑实现类
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Slf4j
@Service
public class ReservationServicesBizImpl implements ReservationServicesBiz {

    @Resource
    private SnowflakeGenerator snowflakeGenerator;

    @Resource
    private ReservationServicesService reservationServicesService;
    @Resource
    private UserServicesService userServicesService;
    @Resource
    private UserStoresService userStoresService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private VerifyService verifyService;
    @Resource
    private OrderItemsService orderItemsService;
    @Resource
    private ProductsService productsService;
    @Resource
    private UsersService usersService;
    @Resource
    private RefundService refundService;
    @Resource
    private OrderSettingsService ordersettingsService;

    @Resource
    private UserServicesBiz userServicesBiz;
    @Resource
    private ProductsBiz productsBiz;
    @Resource
    private UsersBiz usersBiz;
    @Resource
    private StoreBiz storeBiz;
    @Resource
    private DepartmentBiz departmentBiz;
    @Resource
    private RoomsBiz roomsBiz;
    @Resource
    private DevicesBiz devicesBiz;

    @Resource
    private OperationLogService operationLogService;
    @Resource
    private AliSmsBiz aliSmsBiz;

    @Override
    public ReservationServicesVO getById(Long id) {
        ReservationServices entity = reservationServicesService.getById(id);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        ReservationServicesVO res = BeanUtil.copyProperties(entity, ReservationServicesVO.class);
        return res;
    }

    @Override
    public void check(ReservationServicesCreatePO reservationServicesCreatePO) {
        Integer reservationNumber = reservationServicesCreatePO.getReservationNumber();
        if (reservationNumber <= 0) {
            throw new BusinessException("预约人数不能小于0");
        }
        // 检查是否可以预约
        Long userServiceId = reservationServicesCreatePO.getUserServiceId();
        UserServicesVO userServices = userServicesBiz.getById(userServiceId);
        // DD 说不需要这个提示
//        if (userServices.getAvailableQuantity().compareTo(BigDecimal.ZERO) <= 0) {
//            throw new BusinessException("服务已经预约完了");
//        }
        BigDecimal oldQuantity = BigDecimal.ZERO;
        if(reservationServicesCreatePO.getId() != null && reservationServicesCreatePO.getId() > 0){
            // 修改预约单
            ReservationServices reservationServices = reservationServicesService.getById(reservationServicesCreatePO.getId());
            if(Objects.nonNull(reservationServices)){
                oldQuantity = reservationServices.getQuantity().multiply(BigDecimal.valueOf(reservationServices.getReservationNumber()));
            }
        }

        if (UsersTypeEnums.MEMBER == UsersTypeEnums.codeToEnum(LoginUserUtil.getLoginUser().getUserType())
                && userServices.getValidTo().before(new Date())) {
            throw new BusinessException("服务已经过期");
        }
        Users users = usersService.getById(userServices.getUserId());
        UsersStatusEnums usersStatus = UsersStatusEnums.codeToEnum(users.getStatus());
        if (UsersStatusEnums.STOP == usersStatus) {
            throw new BusinessException("用户已被停用,不能进行预约");
        }
        if (UsersStatusEnums.MERGE == usersStatus) {
            throw new BusinessException("用户已被合并,不能进行预约");
        }
        // 计算扣除次数
        Date serviceStartTime = reservationServicesCreatePO.getServiceStartTime();
        Date serviceEndTime = reservationServicesCreatePO.getServiceEndTime();
        // 将 Date 转换为 Instant
        Instant startInstant = serviceStartTime.toInstant();
        Instant endInstant = serviceEndTime.toInstant();
        // 计算两个 Instant 之间的 Duration
        Duration duration = Duration.between(startInstant, endInstant);
        // 获取总分钟数
        long minutesDifference = duration.toMinutes();

        // 获取项目/疗程的服务总时长
        Products products = productsService.getById(userServices.getProductId());
        if(Objects.isNull(products)){
            throw new BusinessException("服务预约异常，服务项目不存在或已被删除，请联系管理员！");
        }
        // 单次服务时长
        Integer serviceDuration = products.getServiceDuration();
        // 预约服务次数
        BigDecimal quantity = BigDecimal.ZERO;
        if(serviceDuration == 0){
            quantity = BigDecimal.valueOf(1);
        }else{
            BigDecimal quotient = BigDecimal.valueOf(minutesDifference).divide(BigDecimal.valueOf(serviceDuration),3, RoundingMode.HALF_UP);
            // 提取整数部分（直接截断）
            BigDecimal integerPart = quotient.setScale(0, RoundingMode.DOWN);

            // 计算小数部分
            BigDecimal fractionalPart = quotient.subtract(integerPart);
            // 预约服务次数
            quantity = integerPart;
            // 判断小数部分是否≥0.5
            if (fractionalPart.compareTo(new BigDecimal("0.5")) >= 0) {
                quantity = integerPart.add(new BigDecimal("0.5"));
            }
        }
        BigDecimal multiply = BigDecimal.valueOf(reservationNumber).multiply(quantity);
        if(userServices.getAvailableQuantity().add(oldQuantity).subtract(multiply).compareTo(BigDecimal.ZERO) < 0){
//            BigDecimal subtract = multiply.subtract(userServices.getAvailableQuantity());
            throw new BusinessException("本次预约总服务次数"+multiply+"大于现可用服务数"+userServices.getAvailableQuantity().add(oldQuantity) +"次！请确认，确认后可预约成功。");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ReservationServicesVO create(ReservationServicesCreatePO reservationServicesCreatePO) {
        if(reservationServicesCreatePO.getReservationNumber() == null || reservationServicesCreatePO.getReservationNumber() <= 0){
            throw new BusinessException("预约人数不能小于0");
        }

        // 检查是否可以预约
        Long userServiceId = reservationServicesCreatePO.getUserServiceId();
        UserServicesVO userServices = userServicesBiz.getById(userServiceId);
        if (userServices.getAvailableQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("服务已经预约完了");
        }
        if (UsersTypeEnums.MEMBER == UsersTypeEnums.codeToEnum(LoginUserUtil.getLoginUser().getUserType())
                && userServices.getValidTo().before(new Date())) {
            throw new BusinessException("服务已经过期");
        }
        Users users = usersService.getById(userServices.getUserId());
        UsersStatusEnums usersStatus = UsersStatusEnums.codeToEnum(users.getStatus());
        if (UsersStatusEnums.STOP == usersStatus) {
            throw new BusinessException("用户已被停用,不能进行预约");
        }
        if (UsersStatusEnums.MERGE == usersStatus) {
            throw new BusinessException("用户已被合并,不能进行预约");
        }
        // 计算扣除次数
        Date serviceStartTime = reservationServicesCreatePO.getServiceStartTime();
        Date serviceEndTime = reservationServicesCreatePO.getServiceEndTime();
        // 将 Date 转换为 Instant
        Instant startInstant = serviceStartTime.toInstant();
        Instant endInstant = serviceEndTime.toInstant();
        // 计算两个 Instant 之间的 Duration
        Duration duration = Duration.between(startInstant, endInstant);
        // 获取总分钟数
        long minutesDifference = duration.toMinutes();

        // 获取项目/疗程的服务总时长
        Products products = productsService.getById(userServices.getProductId());
        if(Objects.isNull(products)){
            throw new BusinessException("服务预约异常，服务项目不存在或已被删除，请联系管理员！");
        }
        // 单次服务时长
        Integer serviceDuration = products.getServiceDuration();
        // 预约服务次数
        BigDecimal quantity = BigDecimal.ZERO;
        if(serviceDuration == 0){
            quantity = BigDecimal.valueOf(1);
        }else{
            BigDecimal quotient = BigDecimal.valueOf(minutesDifference).divide(BigDecimal.valueOf(serviceDuration),3, RoundingMode.HALF_UP);
            // 提取整数部分（直接截断）
            BigDecimal integerPart = quotient.setScale(0, RoundingMode.DOWN);

            // 计算小数部分
            BigDecimal fractionalPart = quotient.subtract(integerPart);
            // 预约服务次数
            quantity = integerPart;
            // 判断小数部分是否≥0.5
            if (fractionalPart.compareTo(new BigDecimal("0.5")) >= 0) {
                quantity = integerPart.add(new BigDecimal("0.5"));
            }
        }
        BigDecimal quantityNew = quantity.multiply(BigDecimal.valueOf(reservationServicesCreatePO.getReservationNumber()));
        BigDecimal rsQuantity = quantityNew;
        if(quantityNew.subtract(userServices.getAvailableQuantity()).compareTo(BigDecimal.ZERO) > 0){
            quantityNew = userServices.getAvailableQuantity();
        }
        // 扣除预约次数
        boolean deductionSuccess = userServicesBiz.deductionQuantity(userServiceId,quantityNew);
        if (!deductionSuccess) {
            throw new BusinessException("预约服务失败，预约次数大于购买次数或系统异常");
        }
        ProductsVO productsVO = productsBiz.getById(userServices.getProductId());
        if (productsVO.getIzSpecialProject() && Objects.isNull(reservationServicesCreatePO.getRoomId())
                && !reservationServicesCreatePO.getIsOnwKeyVerify()) {
            throw new BusinessException("特殊项目，必须选择房间");
        }
        if (reservationServicesCreatePO.getIsMemberCreate()) {
            // 会员下单验证风险商品
            if (productsVO.getIzRiskyProduct()) {
                List<DepartmentUserVO> departmentUsers = userStoresService.getDepartmentUser(productsVO.getStoreId(),
                        productsVO.getDepartmentId());
                if (CollUtil.isNotEmpty(departmentUsers)) {
                    DepartmentUserVO departmentUser = departmentUsers.stream()
                            .filter(e -> e.getIdentity().equals("1") && e.getStatus().equals(1))
                            .findFirst().orElse(null);
                    if (departmentUser != null) {
                        throw new BusinessException("此项目请联系【" + departmentUser.getPhone() + "】进行预约");
                    } else {
                        throw new BusinessException("此项目不支持在线预约，请联系门店管理员");
                    }
                }
            }
        }

        // boolean existsRefund =
        // refundService.existsRefundByOrderItemId(userServices.getOrderItemId());
        // if(existsRefund){
        // throw new BusinessException("存在退款，不能进行预约");
        // }

        AmontAndPointDTO amontAndPointDTO = this.getAvgPrice(userServices,quantityNew);

        ReservationServices entity = BeanUtil.copyProperties(reservationServicesCreatePO, ReservationServices.class);
        entity.setReservationNo(SnowPrefixConstants.RESERVATION + snowflakeGenerator.next());
        entity.setUserId(userServices.getUserId());
        entity.setOrderId(userServices.getOrderId());
        entity.setOrderItemId(userServices.getOrderItemId());
        entity.setProductId(userServices.getProductId());
        entity.setProductName(userServices.getProductName());
        entity.setProductMainImage(productsVO.getProductMainImage());
        entity.setAmount(amontAndPointDTO.getAmount());
        entity.setPoint(amontAndPointDTO.getPoint());
        entity.setStatus(ReservationServicesEnums.Status.PENDING.getCode());
        // pointAmount 核销积分金额
        entity.setPointAmount(amontAndPointDTO.getPointAmount());
        entity.setQuantity(rsQuantity);
        entity.setReservationNumber(reservationServicesCreatePO.getReservationNumber());
        reservationServicesService.save(entity);

        // 根据订单状态为使用中
        Long orderId = userServices.getOrderId();
        ordersService.updateOrderStatus(orderId, OrderStatusEnums.USING);
        try {
            Users user = usersService.getById(userServices.getUserId());
            Users employee = usersService.getById(entity.getEmployeeId());
            StoreVO storeVO = storeBiz.getById(userServices.getStoreId());
            DepartmentVO departmentVO = departmentBiz.getById(userServices.getDepartmentId());

            aliSmsBiz.reservationSms(user.getPhone(),
                    storeVO.getStoreName(),
                    departmentVO.getDepartmentName(),
                    DateUtil.format(entity.getReservationDate(), "yyyy-MM-dd"),
                    DateUtil.format(entity.getServiceStartTime(), "HH:mm"),
                    DateUtil.format(entity.getServiceEndTime(), "HH:mm"),
                    employee.getName(), storeVO.getContactPhone());
        } catch (Exception e) {
            log.error("服务预约发送短信失败！", e);
        }

        try {
            operationLogService.saveOperationLog(entity.getId(),
                    String.format("代客预约：{%s}", entity.getId()),
                    "代客预约");
        } catch (Exception e) {
            log.error("代客预约保存日志失败！", e);
        }
        return BeanUtil.copyProperties(entity, ReservationServicesVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ReservationServicesUpdatePO reservationServicesUpdatePO) {
        ReservationServices entity = reservationServicesService.getById(reservationServicesUpdatePO.getId());
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        // 原来的预约人数
        Integer oldReservationNumber = entity.getReservationNumber();
        // 修改后的预约人数
        Integer newReservationNumber = reservationServicesUpdatePO.getReservationNumber();
        ReservationServices updateEntity = BeanUtil.copyProperties(reservationServicesUpdatePO,
                ReservationServices.class);
        updateEntity.setReservationNumber(newReservationNumber);
        BigDecimal newQuantityTime = this.getQuantity(reservationServicesUpdatePO.getServiceStartTime(),
                reservationServicesUpdatePO.getServiceEndTime(), entity.getProductId());
        //修改后的预约次数 = 根据时间算出来的次数*预约人数
        BigDecimal newQuantity = newQuantityTime.multiply(BigDecimal.valueOf(newReservationNumber));
        updateEntity.setQuantity(newQuantity);
        reservationServicesService.updateById(updateEntity);
        Long userServiceId = entity.getUserServiceId();
        //可以修改预约时间和预约人数，所以预约数量和预约人数都可能会变
        if (oldReservationNumber != newReservationNumber || newQuantity.compareTo(entity.getQuantity()) != 0) {
            // 用户服务
            UserServices userServices = userServicesService.getById(userServiceId);
            // 把原来的预约总次数还原回去
            userServices.setAvailableQuantity(userServices.getAvailableQuantity().add(entity.getQuantity()));
            userServices.setUsedQuantity(userServices.getUsedQuantity().subtract(entity.getQuantity()));
            BigDecimal availableQuantity = userServices.getAvailableQuantity();
            BigDecimal usedQuantity = userServices.getUsedQuantity();
            if(availableQuantity.subtract(newQuantity).compareTo(BigDecimal.ZERO) < 0){
                usedQuantity = usedQuantity.add(availableQuantity);
                availableQuantity = BigDecimal.ZERO;
            }else{
                availableQuantity = availableQuantity.subtract(newQuantity);
                usedQuantity = usedQuantity.add(newQuantity);
            }

            LambdaUpdateWrapper<UserServices> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserServices::getId, userServiceId)
                    .set(UserServices::getAvailableQuantity, availableQuantity)
                    .set(UserServices::getUsedQuantity, usedQuantity);
            userServicesService.update(updateWrapper);
        }

        try {
            operationLogService.saveOperationLog(entity.getId(),
                    BeanUtil.copyProperties(entity, ReservationServicesUpdatePO.class), reservationServicesUpdatePO,
                    "修改预约");
        } catch (Exception e) {
            log.error("修改预约保存日志失败！", e);
        }
    }

    private BigDecimal getQuantity(Date serviceStartTime, Date serviceEndTime, Long productId) {
        // 将 Date 转换为 Instant
        Instant startInstant = serviceStartTime.toInstant();
        Instant endInstant = serviceEndTime.toInstant();
        // 计算两个 Instant 之间的 Duration
        Duration duration = Duration.between(startInstant, endInstant);
        // 获取总分钟数
        long minutesDifference = duration.toMinutes();

        // 获取项目/疗程的服务总时长
        Products products = productsService.getById(productId);
        if(Objects.isNull(products)){
            throw new BusinessException("服务预约异常，服务项目不存在或已被删除，请联系管理员！");
        }
        // 单次服务时长
        Integer serviceDuration = products.getServiceDuration();
        // 预约服务次数
        BigDecimal quantity = BigDecimal.ZERO;
        if(serviceDuration == 0){
            quantity = BigDecimal.valueOf(1);
        }else{
            BigDecimal quotient = BigDecimal.valueOf(minutesDifference).divide(BigDecimal.valueOf(serviceDuration),3, RoundingMode.HALF_UP);
            // 提取整数部分（直接截断）
            BigDecimal integerPart = quotient.setScale(0, RoundingMode.DOWN);

            // 计算小数部分
            BigDecimal fractionalPart = quotient.subtract(integerPart);
            // 预约服务次数
            quantity = integerPart;
            // 判断小数部分是否≥0.5
            if (fractionalPart.compareTo(new BigDecimal("0.5")) >= 0) {
                quantity = integerPart.add(new BigDecimal("0.5"));
            }
        }
        return quantity;
    }

    @Override
    public void deleteById(Long id) {
        reservationServicesService.removeById(id);
    }

    /**
     * 获取预约面板的员工列表
     * 
     * @param storeId 门店ID
     * @return 返回预约面板的员工列表
     */
    @Override
    public List<UsersVO> getEmployeeForBoard(Long storeId) {
        List<Users> users = usersService.getEmployeeForBoard(storeId);
        return BeanUtil.copyToList(users, UsersVO.class);
    }

    @Override
    public ReservationBoardVO getBoard(ReservationBoardPO reservationBoardPO) {
        ReservationBoardVO res = new ReservationBoardVO();

        List<UsersVO> user = usersBiz.listByStoreIdAndDepartmentIdAndUserId(
                reservationBoardPO.getStoreId(),
                reservationBoardPO.getDepartmentId(),
                reservationBoardPO.getUserId());
        if (CollUtil.isEmpty(user)) {
            return res;
        }

        List<Long> employeeIds = user.stream().map(UsersVO::getId).collect(Collectors.toList());
        List<ReservationServices> reservationServicesList = reservationServicesService.getBoard(
                reservationBoardPO.getReservationDate(),
                employeeIds,
                reservationBoardPO.getStoreId(),
                reservationBoardPO.getDepartmentId());
        List<ReservationServicesVO> rsList = BeanUtil.copyToList(reservationServicesList, ReservationServicesVO.class);
        usersBiz.fullUserName(rsList, ReservationServicesVO::getUserId, ReservationServicesVO::setUserName);
        productsBiz.fullData(rsList, ReservationServicesVO::getProductId,
                (rs, product) -> rs.setTotalQuantity(BigDecimal.valueOf(product.getStock() == null ? 0: product.getStock())));

        // 获取星期几,dayOfWeek
        // 1表示周日，所以要减1
        int weekDay = DateUtil.dayOfWeek(reservationBoardPO.getReservationDate()) - 1;
        if (weekDay == 0) {
            weekDay = 7;
        }
        String weekDayStr = String.valueOf(weekDay);
        List<ProductsVO> productsVOList = productsBiz.listProjectByFree(
                reservationBoardPO.getStoreId(),
                reservationBoardPO.getDepartmentId(),
                employeeIds,
                weekDayStr);

        res.init(user, rsList, productsVOList);
        return res;
    }

    @Override
    public List<ReservationServicesVO> list(ReservationServicesQueryPO reservationServicesQueryPO) {
        ReservationServices entity = BeanUtil.copyProperties(reservationServicesQueryPO, ReservationServices.class);
        List<ReservationServices> list = reservationServicesService.listEntity(entity);
        return BeanUtil.copyToList(list, ReservationServicesVO.class);
    }

    @Override
    public PageResult<ReservationServicesVO> pageList(ReservationServicesQueryPO queryPO) {
        ReservationServicesPageQueryDTO entity = BeanUtil.copyProperties(queryPO,
                ReservationServicesPageQueryDTO.class);
        Page<ReservationServicesPageResultDTO> result = reservationServicesService.pageList(queryPO.buildPage(),
                entity);
        PageResult<ReservationServicesVO> pageResult = PageResultHelper.transfer(result, ReservationServicesVO.class);
        List<ReservationServicesVO> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        storeBiz.fullName(list, ReservationServicesVO::getStoreId, ReservationServicesVO::setStoreName);
        departmentBiz.fullName(list, ReservationServicesVO::getDepartmentId, ReservationServicesVO::setDepartmentName);
        roomsBiz.fullName(list, ReservationServicesVO::getRoomId, ReservationServicesVO::setRoomName);
        devicesBiz.fullName(list, ReservationServicesVO::getDeviceId, ReservationServicesVO::setDeviceName);

        usersBiz.fullUserName(list, ReservationServicesVO::getEmployeeId, ReservationServicesVO::setEmployeeName);
        usersBiz.fullData(list, ReservationServicesVO::getEmployeeId, (vo, user) -> {
            vo.setEmployeeName(user.getName());
            vo.setEmployeePhone(user.getPhone());
        });
        usersBiz.fullUserName(list, ReservationServicesVO::getCancelUserId, ReservationServicesVO::setCancelUserName);
        usersBiz.fullUserName(list, ReservationServicesVO::getRefundUserId, ReservationServicesVO::setRefundUserName);
        usersBiz.fullData(list, ReservationServicesVO::getUserId, (vo, user) -> {
            vo.setUserName(user.getName());
            vo.setPhone(user.getPhone());
        });
        // 处理是否特殊项目
        List<Long> productIds = list.stream().map(ReservationServicesVO::getProductId).collect(Collectors.toList());
        Map<Long, Products> productsMap = productsService.getProductsMap(productIds);
        list.stream().forEach(r -> {
            Products products = productsMap.get(r.getProductId());
            r.setIzSpecialProject(products.getIzSpecialProject());
        });
        return pageResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void verify(ReservationVerifyPO reservationVerifyPO) {
        ReservationServices reservationServices = reservationServicesService.getById(reservationVerifyPO.getId());
        this.checkCanHandle(reservationServices);
        reservationServices.setStatus(ReservationServicesEnums.Status.COMPLETED.getCode());
        reservationServicesService.updateById(reservationServices);

        Orders orders = ordersService.getById(reservationServices.getOrderId());
        Verify verify = new Verify();
        verify.setReservationId(reservationServices.getId());
        verify.setVerifyTime(new Date());
        verify.setVerifyUserId(LoginUserUtil.getLoginUser().getUserId());
        verify.setVerifier(LoginUserUtil.getLoginUser().getName());
        verify.setAmount(reservationServices.getAmount());
        verify.setOrderNo(orders.getOrderNo());
        verify.setStoreId(orders.getStoreId());
        verify.setDepartmentId(orders.getDepartmentId());
        verify.setImageUrl(reservationVerifyPO.getImageUrl());
        verify.setRemark(reservationVerifyPO.getRemark());
        verify.setPointAmount(reservationServices.getPointAmount());
        verify.setPoint(reservationServices.getPoint());
        verify.setQuantity(reservationServices.getQuantity());
        verifyService.save(verify);

        this.checkOrderCompletion(reservationServices.getOrderId());

        // 用户服务金额，积分回显
        Long userServiceId = reservationServices.getUserServiceId();
        UserServices userServices = userServicesService.getById(userServiceId);
        BigDecimal add = reservationServices.getAmount().add(userServices.getUsedServiceAmount());
        userServices.setUsedServiceAmount(add);
//        Long add1 = userServices.getServiceTotalPoint()+reservationServices.getPoint();
//        userServices.setServiceTotalPoint(add1);
        userServicesService.updateById(userServices);
        try {
            Users user = usersService.getById(reservationServices.getUserId());
            UserServicesVO userServicesVO = userServicesBiz.getById(reservationServices.getUserServiceId());
            DepartmentVO departmentVO = departmentBiz.getById(userServicesVO.getDepartmentId());
            Users employee = usersService.getById(reservationServices.getEmployeeId());
            StoreVO storeVO = storeBiz.getById(userServicesVO.getStoreId());
            aliSmsBiz.writeOffSms(user.getPhone(),
                    departmentVO.getDepartmentName(),
                    userServicesVO.getProductName(),
                    DateUtil.format(reservationServices.getReservationDate(), "yyyy-MM-dd"),
                    DateUtil.format(reservationServices.getServiceStartTime(), "HH:mm"),
                    DateUtil.format(reservationServices.getServiceEndTime(), "HH:mm"),
                    employee.getName(),
                    String.valueOf(userServicesVO.getAvailableQuantity()), storeVO.getContactPhone());
        } catch (Exception e) {
            log.error("发送核销短信失败！", e);
        }

        try {
            operationLogService.saveOperationLog(reservationServices.getId(),
                    String.format("核销预约：{%s}", reservationServices.getReservationNo()),
                    "核销预约");
        } catch (Exception e) {
            log.error("核销预约保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onwKeyVerify(OnwKeyVerifyPO po) {
        UserServices us = userServicesService.getById(po.getId());
        if (Objects.isNull(us)) {
            throw new BusinessException("暂无可核销记录");
        }

        if (us.getAvailableQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("服务已经预约完了");
        }
        if (us.getAvailableQuantity().subtract(po.getQuantity()).compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("可用服务数量不足");
        }

        Products project = productsService.getById(us.getProductId());
        Users emp = usersService.getByEmployee(project.getRelateEmployeeId());
        Long empId = Objects.isNull(emp) ? LoginUserUtil.getLoginUser().getUserId() : emp.getId();

        Date now = new Date();

        // 核销次数小于0.5 算0次 等于0.5小于1 算0.5次

        // 求出预约结束时长
        BigDecimal timeDuration = po.getQuantity().multiply(BigDecimal.valueOf(project.getServiceDuration()));
        Integer duration = timeDuration.setScale(0, RoundingMode.CEILING).intValueExact();

        Date end = DateUtil.offsetMinute(now, duration);
        //now和end是否跨天
        LocalDate startDate = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = end.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if(!startDate.equals(endDate)){
            throw new BusinessException("核销次数太多，导致服务时间跨天，请减少核销次数！");
        }
        // 先创建预约信息
//        List<ReservationServicesVO> rsCreatedList = new ArrayList<>();
//        int quantity = po.getQuantity().setScale(0, RoundingMode.CEILING).intValueExact();
//        for (int i = 0; i < quantity; i++) {

            ReservationServicesCreatePO rsCreatePO = new ReservationServicesCreatePO();
            rsCreatePO.setRemark(BasicConstants.ONW_KEY_VERIFY_REMARKS);
            rsCreatePO.setUserServiceId(us.getId());
            rsCreatePO.setEmployeeId(empId);
            rsCreatePO.setReservationDate(now);
            rsCreatePO.setServiceStartTime(now);
            rsCreatePO.setServiceEndTime(end);
            rsCreatePO.setIsOnwKeyVerify(true);
            //人数默认是1
            rsCreatePO.setReservationNumber(1);
            ReservationServicesVO rs = this.create(rsCreatePO);
//            rsCreatedList.add(rs);
//        }

        // 再创建核销信息
//        for (ReservationServicesVO reservationServicesVO : rsCreatedList) {
            ReservationVerifyPO rvPO = new ReservationVerifyPO();
            rvPO.setId(rs.getId());
            rvPO.setRemark(BasicConstants.ONW_KEY_VERIFY_REMARKS);
            this.verify(rvPO);
//        }

        try {
            operationLogService.saveOperationLog(po.getId(),
                    String.format("会员{%s}订单{%s}中的{%s}一键核销{%s}次", usersBiz.getDetailById(us.getUserId()).getPhone(),
                            ordersService.getById(us.getOrderId()).getOrderNo(),
                            orderItemsService.getById(us.getOrderItemId()).getProductName(),
                            po.getQuantity()),
                    "延期");
        } catch (Exception e) {
            log.error("延期保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void verifyBatch(BatchReservationVerifyPO po) {
        List<ReservationServices> list = reservationServicesService.listByIds(po.getIds());
        for (ReservationServices reservationServices : list) {
            ReservationVerifyPO rvPO = new ReservationVerifyPO();
            rvPO.setId(reservationServices.getId());
            rvPO.setImageUrl(po.getImageUrl());
            if(StrUtil.isEmpty(po.getRemark())){
                rvPO.setRemark(BasicConstants.BOARD_VERIFY_REMARKS);
            } else {
                rvPO.setRemark(po.getRemark());
            }
            this.verify(rvPO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(Long id, boolean isMember) {
        ReservationServices reservationServices = reservationServicesService.getById(id);
        this.checkCanHandle(reservationServices);

        if (isMember) {
            // 会员取消才需要判断
            OrderSettings orderSettings = ordersettingsService.get();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String reservationDate = sdf.format(reservationServices.getReservationDate());
            SimpleDateFormat timeSdf = new SimpleDateFormat("HH:mm");
            String serviceStartTime = timeSdf.format(reservationServices.getServiceStartTime());
            SimpleDateFormat startSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            try {
                Date serviceStartTimeDate = startSdf.parse(reservationDate + " " + serviceStartTime);
                Date cancelTime = DateUtil.offsetHour(serviceStartTimeDate,
                        -orderSettings.getCancelTime());
                if (new Date().after(cancelTime)) {
                    throw new BusinessException("预约取消时间已过, 无法取消");
                }
            } catch (ParseException e) {
                log.error("取消预约时间格式化失败", e);
                throw new BusinessException("取消预约时间格式化失败");
            }
        }

        reservationServices.setStatus(ReservationServicesEnums.Status.CANCELLED.getCode());
        reservationServices.setCancelUserId(LoginUserUtil.getLoginUser().getUserId());
        reservationServices.setCancelTime(new Date());
        reservationServicesService.updateById(reservationServices);
        //这个数量不需要乘人数，因为在预约以及修改的时候，这个数量已经乘了数量
        //BigDecimal userServicesQuantity = reservationServices.getQuantity().multiply(BigDecimal.valueOf(reservationServices.getReservationNumber()));
        UserServices userServices = userServicesService.getById(reservationServices.getUserServiceId());
        BigDecimal used = userServices.getUsedQuantity();
        BigDecimal returnQuantity = reservationServices.getQuantity();
        if (used.compareTo(returnQuantity) < 0) {
            returnQuantity = used;
        }
        userServicesBiz.addQuantity(reservationServices.getUserServiceId(), returnQuantity);

        try {
            Users user = usersService.getById(reservationServices.getUserId());
            UserServicesVO userServicesVO = userServicesBiz.getById(reservationServices.getUserServiceId());
            DepartmentVO departmentVO = departmentBiz.getById(userServicesVO.getDepartmentId());
            StoreVO storeVO = storeBiz.getById(userServicesVO.getStoreId());
            Users employee = usersService.getById(reservationServices.getEmployeeId());
            aliSmsBiz.returnReservationSms(user.getPhone(),
                    storeVO.getStoreName(),
                    departmentVO.getDepartmentName(),
                    DateUtil.format(reservationServices.getReservationDate(), "yyyy-MM-dd"),
                    DateUtil.format(reservationServices.getServiceStartTime(), "HH:mm"),
                    DateUtil.format(reservationServices.getServiceEndTime(), "HH:mm"),
                    employee.getName(), storeVO.getContactPhone());
        } catch (Exception e) {
            log.error("发送【退预约状态提醒】消息发生异常", e);
        }

        try {
            operationLogService.saveOperationLog(reservationServices.getId(),
                    String.format("取消预约：{%s}", reservationServices.getReservationNo()),
                    "取消预约");
        } catch (Exception e) {
            log.error("取消预约保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelBatch(IdBatchPO<Long> po, boolean isMember) {
        List<ReservationServices> list = reservationServicesService.listByIds(po.getIds());
        for (ReservationServices reservationServices : list) {
            this.cancel(reservationServices.getId(), isMember);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refund(ReservationRefundPO po) {
        ReservationServices reservationServices = reservationServicesService.getById(po.getId());
        ReservationServicesEnums.Status status = ReservationServicesEnums.Status
                .getByCode(reservationServices.getStatus());
        if (ReservationServicesEnums.Status.COMPLETED != status) {
            throw new BusinessException("只有已完成的预约才能退还");
        }
        reservationServices.setStatus(ReservationServicesEnums.Status.REFUNDED.getCode());
        reservationServices.setRefundUserId(LoginUserUtil.getLoginUser().getUserId());
        reservationServices.setRefundRemark(po.getRefundRemark());
        reservationServices.setRefundTime(new Date());
        reservationServicesService.updateById(reservationServices);
        userServicesBiz.addQuantity(reservationServices.getUserServiceId(),reservationServices.getQuantity());
        this.checkOrderRollback(reservationServices.getOrderId());

        try {
            operationLogService.saveOperationLog(po.getId(),
                    String.format("退还：{%s}", reservationServices.getReservationNo()),
                    "退还");
        } catch (Exception e) {
            log.error("退还保存日志失败！", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOrderCompletion(Long orderId) {
        UserServices query = new UserServices();
        query.setOrderId(orderId);
        List<UserServices> usList = userServicesService.listEntity(query);
        boolean notCompletion = usList.stream()
                .map(UserServices::getAvailableQuantity)
                .anyMatch(aq -> aq.compareTo(BigDecimal.ZERO) > 0);
        if (notCompletion) {
            // 未完成
            return;
        }
        ReservationServices reservationServices = new ReservationServices();
        reservationServices.setOrderId(orderId);
        List<ReservationServices> rsList = reservationServicesService.listEntity(reservationServices);
        notCompletion = rsList.stream()
                .map(ReservationServices::getStatus)
                .map(ReservationServicesEnums.Status::getByCode)
                .anyMatch(s -> ReservationServicesEnums.Status.PENDING == s);
        if (notCompletion) {
            // 未完成
            return;
        }
        ordersService.completeOrder(orderId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOrderRollback(Long orderId) {
        ordersService.rollbackOrder(orderId);
    }

    @Override
    public ReservationServicesDetailVO getDetail(Long id) {
        ReservationServices reservationServices = reservationServicesService.getById(id);
        if (Objects.isNull(reservationServices)) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        ReservationServicesDetailVO res = BeanUtil.copyProperties(reservationServices,
                ReservationServicesDetailVO.class);

        usersBiz.fullData(Collections.singletonList(res), ReservationServicesDetailVO::getUserId, (vo, user) -> {
            vo.setNickname(user.getNickname());
            vo.setName(user.getName());
            vo.setPhone(user.getPhone());
            vo.setAvatarUrl(user.getAvatarUrl());
        });
        usersBiz.fullData(Collections.singletonList(res), ReservationServicesDetailVO::getEmployeeId, (vo, user) -> {
            vo.setEmployeeName(user.getName());
            vo.setEmployeePhone(user.getPhone());
        });

        roomsBiz.fullName(Collections.singletonList(res), ReservationServicesDetailVO::getRoomId,
                ReservationServicesDetailVO::setRoomName);
        devicesBiz.fullName(Collections.singletonList(res), ReservationServicesDetailVO::getDeviceId,
                ReservationServicesDetailVO::setDeviceName);

        Verify verify = verifyService.getByReservationId(res.getId());
        if (verify == null) {
            return res;
        }
        VerifyVO verifyVO = BeanUtil.copyProperties(verify, VerifyVO.class);
        res.setVerify(verifyVO);
        return res;
    }

    @Override
    public UserServiceOptionVO getUpdateParam(Long id) {
        ReservationServices reservationServices = reservationServicesService.getById(id);
        if (Objects.isNull(reservationServices)) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        return userServicesBiz.getUserServiceOptionById(reservationServices.getUserServiceId());
    }

    /**
     * 检查是否可以操作
     * 
     * @param reservationServices
     * @return
     */
    private void checkCanHandle(ReservationServices reservationServices) {
        ReservationServicesEnums.Status status = ReservationServicesEnums.Status
                .getByCode(reservationServices.getStatus());
        if (ReservationServicesEnums.Status.COMPLETED.equals(status)) {
            throw new BusinessException("已经核销了, 请勿重复核销");
        }
        if (ReservationServicesEnums.Status.CANCELLED.equals(status)) {
            throw new BusinessException("预约已经取消了, 无法核销");
        }
        if (ReservationServicesEnums.Status.REFUNDED.equals(status)) {
            throw new BusinessException("预约已经退还了, 无法核销");
        }
    }

    /**
     * 获取服务预约记录的金额
     * @param userServices
     * @param quantity 服务次数
     * @return
     */
    private AmontAndPointDTO getAvgPrice(UserServicesVO userServices,BigDecimal quantity) {
        // 服务总金额
        BigDecimal actualAmount = userServices.getServiceTotalAmount();
        // 服务总积分
        Long actualPoints = userServices.getServiceTotalPoint();
        // 订单服务总数量
        BigDecimal sum = userServices.getUsedQuantity().add(userServices.getAvailableQuantity()).add(userServices.getRefundQuantity()) ;
        // 单次服务单价 = 服务总金额 / 订单服务总数量
        BigDecimal singlePrice = actualAmount.divide(sum, 2, RoundingMode.DOWN);
        // 单次服务积分 = 服务总积分 / 订单服务总数量
        Long singlePoint = BigDecimal.valueOf(actualPoints).divide(sum,0, RoundingMode.HALF_UP).longValue();
        // 计算积分兑换比例
        OrderItems orderItems = orderItemsService.getById(userServices.getOrderItemId());
        if(Objects.isNull(orderItems)){
            throw new BusinessException("服务预约失败，订单异常");
        }
        // quantity 的价格 = 预约次数*单次服务单价
        BigDecimal price = quantity.multiply(singlePrice).setScale(2, RoundingMode.DOWN);

        // 服务积分 = 预约次数*单次服务积分
        Long point = quantity.multiply(BigDecimal.valueOf(singlePoint)).setScale(2, RoundingMode.DOWN).longValueExact();
        // 单次服务积分金额
        BigDecimal singlePointAmount = orderItems.getActualPointsAmount().divide(sum, 2, RoundingMode.DOWN);

        BigDecimal pointAmount = quantity.multiply(singlePointAmount).setScale(2, RoundingMode.DOWN);
        // 最后
        if (userServices.getAvailableQuantity().subtract(quantity).compareTo(BigDecimal.ZERO) == 0) {
            return new AmontAndPointDTO(
                    actualAmount.subtract(singlePrice.multiply(userServices.getUsedQuantity())),
                    orderItems.getActualPointsAmount().subtract(singlePointAmount.multiply(userServices.getUsedQuantity())),
                    actualPoints - BigDecimal.valueOf(singlePoint).multiply(userServices.getUsedQuantity()).longValueExact());
        } else {
            return new AmontAndPointDTO(price,pointAmount, point);
        }
    }

}