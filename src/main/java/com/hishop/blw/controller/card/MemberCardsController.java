package com.hishop.blw.controller.card;

import com.hishop.blw.model.po.card.*;
import com.hishop.blw.model.vo.card.MemberCardsStatisticsVO;
import com.hishop.blw.model.vo.card.MemberCardsTypeVO;
import com.hishop.common.pojo.IdPO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.blw.model.vo.card.MemberCardsVO;
import com.hishop.blw.biz.MemberCardsBiz;
import com.hishop.common.response.ResponseBean;
import com.hishop.common.response.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 会员卡表 相关接口
 * 
 * @Author: chenpeng
 * @since: 2025-01-10 10:50:00
 */

@Api(value = "MemberCardsController", tags = "会员卡表相关接口")
@RestController
@RequestMapping("/memberCards")
public class MemberCardsController {

    @Resource
    private MemberCardsBiz memberCardsBiz;

    @ApiOperation(value = "新增 会员卡表", httpMethod = "POST")
    @PostMapping({ "/create" })
    public ResponseBean<Void> create(@Validated @RequestBody MemberCardsCreatePO memberCardsCreatePO) {
        memberCardsBiz.create(memberCardsCreatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "调整储蓄卡", httpMethod = "POST")
    @PostMapping({ "/update" })
    public ResponseBean<Void> update(@RequestBody MemberCardsUpdatePO memberCardsUpdatePO) {
        memberCardsBiz.update(memberCardsUpdatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "冻结储蓄卡", httpMethod = "POST")
    @PostMapping({ "/freeze" })
    public ResponseBean<Void> freeze(@RequestBody MemberCardsUpdatePO memberCardsUpdatePO) {
        memberCardsBiz.freeze(memberCardsUpdatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "删除 会员卡表", httpMethod = "POST")
    @PostMapping({ "/delete" })
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        memberCardsBiz.deleteById(idPO.getId());
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "获取 会员卡表 列表", httpMethod = "POST")
    @PostMapping({ "/listByUserId" })
    public ResponseBean<List<MemberCardsVO>> listByUserId(@RequestBody MemberCardsQueryPO memberCardsQueryPO) {
        return ResponseBean.success(memberCardsBiz.listByUserId(memberCardsQueryPO));
    }

    @ApiOperation(value = "员工端获取 会员卡表 列表", httpMethod = "POST")
    @PostMapping({ "/listByUserIdForEmployeeApplet" })
    public ResponseBean<List<MemberCardsVO>> listByUserIdForEmployeeApplet(
            @RequestBody MemberCardsQueryPO memberCardsQueryPO) {
        // 员工端查会员所有的卡，不区分门店
        memberCardsQueryPO.setStoreId(null);
        memberCardsQueryPO.setIsEmployeeApplet(true);
        return ResponseBean.success(memberCardsBiz.listByUserId(memberCardsQueryPO));
    }

    @ApiOperation(value = "根据用户获取卡类型列表（传userId参数）", httpMethod = "POST")
    @PostMapping({ "/listCartTypeByUserId" })
    public ResponseBean<List<MemberCardsTypeVO>> listCartTypeByUserId(
            @RequestBody MemberCardsQueryPO memberCardsQueryPO) {
        return ResponseBean.success(memberCardsBiz.listCartTypeByUserId(memberCardsQueryPO));
    }

    @ApiOperation(value = "分页查询会员卡表列表", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<MemberCardsVO>> pageList(@RequestBody MemberCardsQueryPO memberCardsQueryPO) {
        return ResponseBean.success(memberCardsBiz.pageList(memberCardsQueryPO));
    }

    @ApiOperation(value = "PC-会员卡统计信息", httpMethod = "GET")
    @GetMapping({ "/memberCardsStatistics" })
    public ResponseBean<MemberCardsStatisticsVO> memberCardsStatistics(@RequestParam("userId") Long userId) {
        return ResponseBean.success(memberCardsBiz.memberCardsStatistics(userId));
    }

    @ApiOperation(value = "员工端小程序-代客充值", httpMethod = "POST")
    @PostMapping("/rechargeProxy")
    public ResponseBean<Void> rechargeProxy(@Validated @RequestBody MemberCardsRechargePO rechargePO) {
        memberCardsBiz.rechargeProxy(rechargePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "PC端-代客充值", httpMethod = "POST")
    @PostMapping("/rechargeProxyPC")
    public ResponseBean<Void> rechargeProxyPC(@RequestBody MemberCardsRechargePO rechargePO) {
        memberCardsBiz.rechargeProxyPC(rechargePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "PC端-取消充值", httpMethod = "POST")
    @PostMapping("/cancelRecharge")
    public ResponseBean<Void> cancelRecharge(@RequestBody IdPO<Long> idPO) {
        memberCardsBiz.cancelRecharge(idPO.getId());
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "解冻储蓄卡", httpMethod = "POST")
    @PostMapping({ "/unfreeze" })
    public ResponseBean<Void> unfreeze(@Validated @RequestBody UnfreezeUpdatePO unfreezeUpdatePO) {
        memberCardsBiz.unfreeze(unfreezeUpdatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "获取 会员卡表 列表(移动端)", httpMethod = "POST")
    @PostMapping({ "/mobileListByUserId" })
    public ResponseBean<List<MemberCardsVO>> mobileListByUserId(@RequestBody MemberCardsQueryPO memberCardsQueryPO) {
        return ResponseBean.success(memberCardsBiz.mobileListByUserId(memberCardsQueryPO));
    }
}