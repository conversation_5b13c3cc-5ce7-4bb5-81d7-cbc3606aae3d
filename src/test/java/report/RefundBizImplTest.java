package report;

import com.hishop.blw.HishopApplication;
import com.hishop.blw.biz.impl.RefundBizImpl;
import com.hishop.blw.repository.entity.RefundItems;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;

@SpringBootTest(classes = HishopApplication.class)
public class RefundBizImplTest {

    @Resource
    private RefundBizImpl refundBiz;




    @Test
    public void test() {

        RefundItems param = new RefundItems();
        param.setId(1937783295282511873L);
        param.setRefundAmount(BigDecimal.valueOf(11050));
        param.setRefundPoints(0L);
        refundBiz.handleRefundItemExtendAmount(param);


    }



}
