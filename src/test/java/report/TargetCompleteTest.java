package report;

import com.hishop.blw.HishopApplication;
import com.hishop.blw.biz.ReportTargetCompleteInfoServiceBiz;
import com.hishop.blw.enums.TargetTypeEnum;
import com.hishop.blw.model.po.report.TargetCompletePo;
import com.hishop.blw.model.vo.report.TargetCompleteInfoVO;
import com.hishop.common.response.PageResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname TargetCompleteTest
 * Description //TODO
 * @date 2025/2/7 15:32
 */
@SpringBootTest(classes = HishopApplication.class)
public class TargetCompleteTest {

    @Resource
    private ReportTargetCompleteInfoServiceBiz reportTargetComplete;

    @Test
    public void test() {
        // 刷新报表
//        _generateReport();
        // 分页查询
        _pageReport();
    }

    private void _generateReport() {
        reportTargetComplete.periodRefresh(new Date());
    }

    private void _pageReport() {
        TargetCompletePo targetCompletePo = new TargetCompletePo();
        targetCompletePo.setStoreIds(Arrays.asList(3L));
        targetCompletePo.setTargetType(TargetTypeEnum.YEAR.getCode());
//        targetCompletePo.setDateTime("2025");
        PageResult<TargetCompleteInfoVO> targetCompleteInfoVOPageResult = reportTargetComplete.pagePeriodCompleteInfo(targetCompletePo);
        System.out.println(targetCompleteInfoVOPageResult);
    }
}
